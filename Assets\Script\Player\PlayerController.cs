using UnityEngine;
using UnityEngine.InputSystem;

[RequireComponent(typeof(CharacterController))]
[RequireComponent(typeof(PlayerInput))]
public class PlayerController : MonoBehaviour
{
    [Header("Movement Settings")]
    [SerializeField] private float walkSpeed = 5f;
    [SerializeField] private float sprintSpeed = 8f;
    [SerializeField] private float crouchSpeed = 2f;
    [SerializeField] private float acceleration = 10f;
    [SerializeField] private float deceleration = 10f;
    [SerializeField] private float jumpHeight = 2f;
    [SerializeField] private float gravity = -9.81f;
    [SerializeField] private float airControl = 0.3f;

    [Header("Camera Settings")]
    [SerializeField] private float mouseSensitivity = 100f;
    [SerializeField] private float gamepadSensitivity = 150f;
    [SerializeField] private bool invertY = false;
    [SerializeField] private float topClamp = 90f;
    [SerializeField] private float bottomClamp = -90f;
    [SerializeField] private bool smoothCamera = true;
    [SerializeField] private float smoothTime = 0.1f;

    [Header("Third Person Camera")]
    [SerializeField] private float thirdPersonDistance = 5f;
    [SerializeField] private Vector3 thirdPersonOffset = new Vector3(0, 2, 0);
    [SerializeField] private float cameraTransitionSpeed = 5f;
    [SerializeField] private LayerMask cameraCollisionLayers = -1;
    [SerializeField] private float cameraCollisionBuffer = 0.2f;

    [Header("Interaction Settings")]
    [SerializeField] private float interactionRange = 3f;
    [SerializeField] private LayerMask interactionLayerMask = -1;
    [SerializeField] private bool useSphereCast = false;
    [SerializeField] private float sphereRadius = 0.5f;

    [Header("Combat Settings")]
    [SerializeField] private float attackDamage = 10f;
    [SerializeField] private float attackRange = 2f;
    [SerializeField] private float attackCooldown = 0.5f;
    [SerializeField] private LayerMask enemyLayerMask = -1;
    [SerializeField] private float attackForce = 5f;
    [SerializeField] private float cameraShakeIntensity = 0.1f;

    [Header("Player Components")]
    [SerializeField] private PlayerMovement playerMovement;
    [SerializeField] private PlayerCamera playerCamera;
    [SerializeField] private PlayerInteraction playerInteraction;
    [SerializeField] private PlayerCombat playerCombat;
    
    [Header("Camera Settings")]
    [SerializeField] private Camera playerCameraComponent;
    [SerializeField] private Transform cameraRoot;
    
    [Header("Ground Check")]
    [SerializeField] private Transform groundCheck;
    [SerializeField] private float groundDistance = 0.4f;
    [SerializeField] private LayerMask groundMask = 1;
    
    // Components
    private CharacterController characterController;
    private PlayerInput playerInputComponent;
    private InputSystem_Actions inputActions;
    
    // Input values
    private Vector2 moveInput;
    private Vector2 lookInput;
    private bool jumpInput;
    private bool jumpHeld; // Track if jump is being held
    private bool sprintInput;
    private bool crouchInput;
    private bool attackInput;
    private bool interactInput;
    
    // Properties
    public bool IsGrounded { get; private set; }
    public bool IsMoving => playerMovement != null ? playerMovement.IsMoving : false;
    public bool IsRunning => playerMovement != null ? playerMovement.IsSprinting : false;
    public CharacterController CharacterController => characterController;
    public Camera PlayerCamera => playerCameraComponent;
    public Transform CameraRoot => cameraRoot;
    
    private void Awake()
    {
        // Get components
        characterController = GetComponent<CharacterController>();
        playerInputComponent = GetComponent<PlayerInput>();

        // Initialize input actions
        inputActions = new InputSystem_Actions();

        // Get or create player components
        InitializePlayerComponents();

        // Sync settings with components
        SyncSettingsToComponents();
        
        // Setup ground check if not assigned
        if (groundCheck == null)
        {
            GameObject groundCheckObj = new GameObject("GroundCheck");
            groundCheckObj.transform.SetParent(transform);
            groundCheckObj.transform.localPosition = new Vector3(0, -1f, 0);
            groundCheck = groundCheckObj.transform;
        }
        
        // Setup camera if not assigned
        if (playerCameraComponent == null)
        {
            playerCameraComponent = Camera.main;
            if (playerCameraComponent == null)
            {
                playerCameraComponent = FindFirstObjectByType<Camera>();
            }
        }
        
        // Setup camera root if not assigned
        if (cameraRoot == null && playerCameraComponent != null)
        {
            cameraRoot = playerCameraComponent.transform.parent;
            if (cameraRoot == null)
            {
                cameraRoot = playerCameraComponent.transform;
            }
        }
    }
    
    private void InitializePlayerComponents()
    {
        // Get or add PlayerMovement
        if (playerMovement == null)
        {
            playerMovement = GetComponent<PlayerMovement>();
            if (playerMovement == null)
            {
                playerMovement = gameObject.AddComponent<PlayerMovement>();
            }
        }
        
        // Get or add PlayerCamera
        if (playerCamera == null)
        {
            playerCamera = GetComponent<PlayerCamera>();
            if (playerCamera == null)
            {
                playerCamera = gameObject.AddComponent<PlayerCamera>();
            }
        }
        
        // Get or add PlayerInteraction
        if (playerInteraction == null)
        {
            playerInteraction = GetComponent<PlayerInteraction>();
            if (playerInteraction == null)
            {
                playerInteraction = gameObject.AddComponent<PlayerInteraction>();
            }
        }
        
        // Get or add PlayerCombat
        if (playerCombat == null)
        {
            playerCombat = GetComponent<PlayerCombat>();
            if (playerCombat == null)
            {
                playerCombat = gameObject.AddComponent<PlayerCombat>();
            }
        }
    }
    
    private void OnEnable()
    {
        if (inputActions != null)
        {
            inputActions.Enable();

            // Subscribe to input events
            inputActions.Player.Move.performed += OnMove;
            inputActions.Player.Move.canceled += OnMove;
            inputActions.Player.Look.performed += OnLook;
            inputActions.Player.Look.canceled += OnLook;
            inputActions.Player.Jump.performed += OnJump;
            inputActions.Player.Sprint.performed += OnSprint;
            inputActions.Player.Sprint.canceled += OnSprint;
            inputActions.Player.Crouch.performed += OnCrouch;
            inputActions.Player.Attack.performed += OnAttack;
            inputActions.Player.Interact.performed += OnInteract;
            inputActions.Player.ToggleCamera.performed += OnToggleCamera;
        }
    }
    
    private void OnDisable()
    {
        if (inputActions != null)
        {
            inputActions.Disable();

            // Unsubscribe from input events
            inputActions.Player.Move.performed -= OnMove;
            inputActions.Player.Move.canceled -= OnMove;
            inputActions.Player.Look.performed -= OnLook;
            inputActions.Player.Look.canceled -= OnLook;
            inputActions.Player.Jump.performed -= OnJump;
            inputActions.Player.Sprint.performed -= OnSprint;
            inputActions.Player.Sprint.canceled -= OnSprint;
            inputActions.Player.Crouch.performed -= OnCrouch;
            inputActions.Player.Attack.performed -= OnAttack;
            inputActions.Player.Interact.performed -= OnInteract;
            inputActions.Player.ToggleCamera.performed -= OnToggleCamera;
        }
    }
    
    private void Update()
    {
        CheckGrounded();
        UpdatePlayerComponents();
    }
    
    private void CheckGrounded()
    {
        IsGrounded = Physics.CheckSphere(groundCheck.position, groundDistance, groundMask);
    }
    
    private void UpdatePlayerComponents()
    {
        // Update movement
        if (playerMovement != null)
        {
            playerMovement.UpdateMovement(moveInput, jumpHeld, sprintInput, crouchInput, IsGrounded);
        }
        
        // Update camera
        if (playerCamera != null)
        {
            playerCamera.UpdateCamera(lookInput);
        }
        
        // Update interaction
        if (playerInteraction != null)
        {
            playerInteraction.UpdateInteraction(interactInput);
        }
        
        // Update combat
        if (playerCombat != null)
        {
            playerCombat.UpdateCombat(attackInput);
        }
        
        // Reset single-frame inputs
        jumpInput = false;
        attackInput = false;
        interactInput = false;
    }
    
    #region Input Callbacks
    private void OnMove(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }
    
    private void OnLook(InputAction.CallbackContext context)
    {
        lookInput = context.ReadValue<Vector2>();
    }
    
    private void OnJump(InputAction.CallbackContext context)
    {
        jumpInput = context.performed;
    }
    
    private void OnSprint(InputAction.CallbackContext context)
    {
        sprintInput = context.ReadValue<float>() > 0.5f;
    }
    
    private void OnCrouch(InputAction.CallbackContext context)
    {
        crouchInput = !crouchInput; // Toggle crouch
    }
    
    private void OnAttack(InputAction.CallbackContext context)
    {
        attackInput = context.performed;
    }
    
    private void OnInteract(InputAction.CallbackContext context)
    {
        interactInput = context.performed;
    }

    private void OnToggleCamera(InputAction.CallbackContext context)
    {
        if (context.performed && playerCamera != null)
        {
            playerCamera.ToggleCameraMode();
        }
    }

    // Thêm các method cho PlayerInput SendMessages
    public void OnMove(UnityEngine.InputSystem.InputValue value)
    {
        moveInput = value.Get<Vector2>();
    }

    public void OnLook(UnityEngine.InputSystem.InputValue value)
    {
        lookInput = value.Get<Vector2>();
    }

    public void OnJump(UnityEngine.InputSystem.InputValue value)
    {
        jumpHeld = value.isPressed;
        if (value.isPressed)
            jumpInput = true;
    }

    public void OnSprint(UnityEngine.InputSystem.InputValue value)
    {
        sprintInput = value.isPressed;
    }

    public void OnCrouch(UnityEngine.InputSystem.InputValue value)
    {
        if (value.isPressed)
            crouchInput = !crouchInput; // Toggle crouch
    }

    public void OnAttack(UnityEngine.InputSystem.InputValue value)
    {
        attackInput = value.isPressed;
    }

    public void OnInteract(UnityEngine.InputSystem.InputValue value)
    {
        interactInput = value.isPressed;
    }

    public void OnToggleCamera(UnityEngine.InputSystem.InputValue value)
    {
        if (value.isPressed && playerCamera != null)
        {
            playerCamera.ToggleCameraMode();
        }
    }
    #endregion

    private void SyncSettingsToComponents()
    {
        // Sync movement settings
        if (playerMovement != null)
        {
            playerMovement.SetMovementSettings(walkSpeed, sprintSpeed, crouchSpeed, acceleration, deceleration);
            playerMovement.SetJumpSettings(jumpHeight, gravity);
            playerMovement.SetAirControl(airControl);
        }

        // Sync camera settings
        if (playerCamera != null)
        {
            playerCamera.SetCameraSettings(mouseSensitivity, gamepadSensitivity, invertY);
            playerCamera.SetCameraLimits(topClamp, bottomClamp);
            playerCamera.SetSmoothSettings(smoothCamera, smoothTime);
            playerCamera.SetThirdPersonSettings(thirdPersonDistance, thirdPersonOffset);
            playerCamera.SetCameraTransitionSettings(cameraTransitionSpeed, cameraCollisionLayers, cameraCollisionBuffer);
        }

        // Sync interaction settings
        if (playerInteraction != null)
        {
            playerInteraction.SetInteractionSettings(interactionRange, interactionLayerMask, useSphereCast, sphereRadius);
        }

        // Sync combat settings
        if (playerCombat != null)
        {
            playerCombat.SetCombatSettings(attackDamage, attackRange, attackCooldown, enemyLayerMask);
            playerCombat.SetCombatEffects(attackForce, cameraShakeIntensity);
        }
    }

    private void OnValidate()
    {
        // Sync settings when values change in Inspector
        if (Application.isPlaying)
        {
            SyncSettingsToComponents();
        }
    }

    private void OnDrawGizmosSelected()
    {
        // Draw ground check sphere
        if (groundCheck != null)
        {
            Gizmos.color = IsGrounded ? Color.green : Color.red;
            Gizmos.DrawWireSphere(groundCheck.position, groundDistance);
        }
    }

    // Public getters for settings
    public float WalkSpeed => walkSpeed;
    public float SprintSpeed => sprintSpeed;
    public float CrouchSpeed => crouchSpeed;
    public float MouseSensitivity => mouseSensitivity;
    public float AttackDamage => attackDamage;
    public float InteractionRange => interactionRange;
}
