Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker8
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker8.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [5300]  Target information:

Player connection [5300]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2101287257 [EditorId] 2101287257 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [5300] Host joined multi-casting on [***********:54997]...
Player connection [5300] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56032
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003374 seconds.
- Loaded All Assemblies, in  1.296 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.982 seconds
Domain Reload Profiling: 2278ms
	BeginReloadAssembly (574ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (5ms)
	RebuildCommonClasses (133ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (97ms)
	LoadAllAssembliesAndSetupDomain (467ms)
		LoadAssemblies (568ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (459ms)
			TypeCache.Refresh (458ms)
				TypeCache.ScanAssembly (422ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (983ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (886ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (62ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (214ms)
			ProcessInitializeOnLoadAttributes (484ms)
			ProcessInitializeOnLoadMethodAttributes (107ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.236 seconds
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.774 seconds
Domain Reload Profiling: 4008ms
	BeginReloadAssembly (342ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (85ms)
	LoadAllAssembliesAndSetupDomain (1715ms)
		LoadAssemblies (1003ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (870ms)
			TypeCache.Refresh (744ms)
				TypeCache.ScanAssembly (696ms)
			BuildScriptInfoCaches (97ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (1775ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1351ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (471ms)
			ProcessInitializeOnLoadAttributes (747ms)
			ProcessInitializeOnLoadMethodAttributes (121ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.15 seconds
Refreshing native plugins compatible for Editor in 4.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (9.2 MB). Loaded Objects now: 7294.
Memory consumption went from 163.1 MB to 153.9 MB.
Total: 154.186700 ms (FindLiveObjects: 2.722300 ms CreateObjectMapping: 2.349200 ms MarkObjects: 9.865300 ms  DeleteObjects: 139.247700 ms)

========================================================================
Received Import Request.
  Time since last request: 103330.751475 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_D.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_D.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '93897d4067704f8db5dbe360e6aa5306') in 1.341747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Green.prefab
  artifactKey: Guid(77b41cc5500a00344b8ea44b6fd251d7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Green.prefab using Guid(77b41cc5500a00344b8ea44b6fd251d7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '834267d33d23b82feab3e9bef6fb76fe') in 0.0446646 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Yellow.prefab
  artifactKey: Guid(bb6c99f5c2301784b8d9afd4e5cd0ec6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Yellow.prefab using Guid(bb6c99f5c2301784b8d9afd4e5cd0ec6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56a0fd6763624cb3e051d2006e36444f') in 0.1730366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_E.prefab
  artifactKey: Guid(47099d5a8751f264fa35067b5c76c156) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_E.prefab using Guid(47099d5a8751f264fa35067b5c76c156) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5afd7022e5cc7a74247146142c66d2f5') in 0.0222414 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/CandelStick_A.prefab
  artifactKey: Guid(96cb795b0da218343914f0f463d43418) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/CandelStick_A.prefab using Guid(96cb795b0da218343914f0f463d43418) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9029cef963fbc147c1ac5ad1335ef43e') in 0.0214664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Dish_B.prefab
  artifactKey: Guid(823e639296de42f47a9c28fd128daf46) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Dish_B.prefab using Guid(823e639296de42f47a9c28fd128daf46) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '307d6b25ebec41cdd721ed1c34061e2d') in 0.025256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_B.prefab
  artifactKey: Guid(1d103c5bf8e12054e91774edd76d4429) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_B.prefab using Guid(1d103c5bf8e12054e91774edd76d4429) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '98b32f4a456e91016dca3b2fca2def22') in 0.0603126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_H.prefab
  artifactKey: Guid(57d9308fd37a43949998a11bfe0e0130) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_H.prefab using Guid(57d9308fd37a43949998a11bfe0e0130) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b3ad7df38fcb52a332927dcd532c9a4') in 0.0276672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_B.prefab
  artifactKey: Guid(6bccb00b358e5c140925a86d14b2b7be) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_B.prefab using Guid(6bccb00b358e5c140925a86d14b2b7be) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7e99c95d402012cd95671129f9fbabc8') in 0.0351191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodB_D.fbx
  artifactKey: Guid(4b55ef08f4f57b149b199d42378a78de) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodB_D.fbx using Guid(4b55ef08f4f57b149b199d42378a78de) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e8d1a43a880d0605809b0f7d021180a') in 0.0779065 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_J.prefab
  artifactKey: Guid(470c6dcd44a4f5247a4ebb15c9ecf1a5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_J.prefab using Guid(470c6dcd44a4f5247a4ebb15c9ecf1a5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c82dbc9374c8e05b356d575396a6f44e') in 0.0294513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Yellow.prefab
  artifactKey: Guid(5026a8af21b42a144a753025fba4863f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Yellow.prefab using Guid(5026a8af21b42a144a753025fba4863f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c336225078567da5bfd7453dbf77f678') in 0.0206021 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Script/Player/PlayerControllerDemo.cs
  artifactKey: Guid(244f0bfc111c774489dff7cec23afd93) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/PlayerControllerDemo.cs using Guid(244f0bfc111c774489dff7cec23afd93) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d0f927515fc90a01185ae9946aaf18d') in 0.0236684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_A.prefab
  artifactKey: Guid(a1012c60a22de2648833e6a4eba62877) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_A.prefab using Guid(a1012c60a22de2648833e6a4eba62877) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '52b6f8f687072587f219f3481879868e') in 0.0239227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Script/Economy/UI/CurrencyUI.cs
  artifactKey: Guid(8f413db7670fcf04f9f32c35d8f7a3d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/UI/CurrencyUI.cs using Guid(8f413db7670fcf04f9f32c35d8f7a3d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d388cdbb15139bbf6e76b3c303bc81e') in 0.024707 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_G.prefab
  artifactKey: Guid(63aa36a75e83cbd4eb188bf4d58fb877) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_G.prefab using Guid(63aa36a75e83cbd4eb188bf4d58fb877) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b7add0c07d6c1438417ffd24dc5160d6') in 0.0264697 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_B.prefab
  artifactKey: Guid(aa5d3031a928b194694da7084baa15c7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_B.prefab using Guid(aa5d3031a928b194694da7084baa15c7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b685b4a41861da85b923c7fcbfc33bf0') in 0.0279269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Beerglass_B.fbx
  artifactKey: Guid(09d6802702109d54a85f09298a3b74e6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Beerglass_B.fbx using Guid(09d6802702109d54a85f09298a3b74e6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '146eb8a15274657b2a903f44e3cbe06d') in 0.0430801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_D.fbx
  artifactKey: Guid(4e57d19b6389ba748bc12e51755e01cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_D.fbx using Guid(4e57d19b6389ba748bc12e51755e01cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ffbf8f8befc1d3f1e7e6d50f806b85d4') in 0.0430247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_D.prefab
  artifactKey: Guid(5123f2ac6b69c6641a188021662067fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_D.prefab using Guid(5123f2ac6b69c6641a188021662067fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2a6095813554266fa5aa021f70955de1') in 0.0215517 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_Sign_E.fbx
  artifactKey: Guid(02ee5ade1bfeb2f4ebdfad746d7853aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_Sign_E.fbx using Guid(02ee5ade1bfeb2f4ebdfad746d7853aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b7d2755f98625e210af23fa737bf001') in 0.0300162 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_P.prefab
  artifactKey: Guid(fc7b8283225d51b45871e86ebfea7bba) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_P.prefab using Guid(fc7b8283225d51b45871e86ebfea7bba) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ac077c813b0eba1531bcce1aa8eb96e8') in 0.0189633 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Onion_Purple.prefab
  artifactKey: Guid(e5934cdb9642e1d4788ce147da1411d4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Onion_Purple.prefab using Guid(e5934cdb9642e1d4788ce147da1411d4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '462a07e64e16a0f2e4f1e9401195dd2a') in 0.0276029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000151 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_D.prefab
  artifactKey: Guid(d272ff7dc98607d459f91813fd8293e5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_D.prefab using Guid(d272ff7dc98607d459f91813fd8293e5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8b1619a17458ab23fddf98e1fc54fa0c') in 0.0204955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_E.prefab
  artifactKey: Guid(c44adc5c019f1ec4ba9bfc6e0e7ee88c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_E.prefab using Guid(c44adc5c019f1ec4ba9bfc6e0e7ee88c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '916ab029abae2e85abee18529a1d0170') in 0.0251959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_A.prefab
  artifactKey: Guid(2746c98c2cfc3944bbd70c1db880e65c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_A.prefab using Guid(2746c98c2cfc3944bbd70c1db880e65c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b0b763c9d974cd6befd1343a4ba162ce') in 0.0245026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_D.prefab
  artifactKey: Guid(97e9ce4d06ef06b4c9156931b512d8ce) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_D.prefab using Guid(97e9ce4d06ef06b4c9156931b512d8ce) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ae565d48bb3a754e588335cc3eebae55') in 0.0262402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_F.prefab
  artifactKey: Guid(49682b01faecb5a49af5af7118431fe6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_F.prefab using Guid(49682b01faecb5a49af5af7118431fe6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9607b4546d2360099c2c01b7eb1d1b7f') in 0.0269174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_B.fbx
  artifactKey: Guid(fee824c48153eea4caf6b44c7f29861d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_B.fbx using Guid(fee824c48153eea4caf6b44c7f29861d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5aa1833fcdd4b3e175130c7ba64461e') in 0.0412283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_M.prefab
  artifactKey: Guid(65e2bf70526c2c049b8a8d2bf9a835e8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_M.prefab using Guid(65e2bf70526c2c049b8a8d2bf9a835e8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a09f11abc5d242f42d957a4d02840470') in 0.0254225 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_F.prefab
  artifactKey: Guid(be91c8ee26b2ff241b14c75e36a20fbd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_F.prefab using Guid(be91c8ee26b2ff241b14c75e36a20fbd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '26e884fe0e8a162cbdf762065f543be4') in 0.0494109 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_B.prefab
  artifactKey: Guid(70757251a402c19499beb58ddd6de94d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_B.prefab using Guid(70757251a402c19499beb58ddd6de94d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ee2caebc8ad9a0885ef73983c68672e9') in 0.0631698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_A.prefab
  artifactKey: Guid(b1f8717edec570a47b6058dbffa24248) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_A.prefab using Guid(b1f8717edec570a47b6058dbffa24248) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd7e61c80f503f80b0265a1235cf853ad') in 0.0290407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_B.prefab
  artifactKey: Guid(f1fa534b38dbf1f46b0a645ff3b92a3d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_B.prefab using Guid(f1fa534b38dbf1f46b0a645ff3b92a3d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ccb634876c1611e292c565967f9fe7c8') in 0.0307525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pear_White.prefab
  artifactKey: Guid(5218ef9d9c7e1ed4ab97a52a21aac3be) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pear_White.prefab using Guid(5218ef9d9c7e1ed4ab97a52a21aac3be) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6f78fa31adc96dcf6d51e0e7220ba1c9') in 0.0259372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_B.prefab
  artifactKey: Guid(d9fee27a73a8b8b41891b71f98fe50b3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_B.prefab using Guid(d9fee27a73a8b8b41891b71f98fe50b3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '22d2ee4ec6cf8259258a13aa90f2a382') in 0.0256144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_C.prefab
  artifactKey: Guid(9426034b21ec7e94c9b4fe77887b4cc4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_C.prefab using Guid(9426034b21ec7e94c9b4fe77887b4cc4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '39dcb4d15350a08d509678caf204cd48') in 0.0336663 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_A.prefab
  artifactKey: Guid(a903833e8c6d31f418dcfedde9a99b71) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_A.prefab using Guid(a903833e8c6d31f418dcfedde9a99b71) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '821d289c4f3443ede027f13e82210781') in 0.0319715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_B.prefab
  artifactKey: Guid(284ee59d69677ea41b65f53f941d8d32) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_B.prefab using Guid(284ee59d69677ea41b65f53f941d8d32) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c8a147418acc5e9b5c33770973764c2f') in 0.0358086 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_A.fbx
  artifactKey: Guid(0b2fa9b8a72b42a4f81592b3864ffcdc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_A.fbx using Guid(0b2fa9b8a72b42a4f81592b3864ffcdc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd94bd09fb41295298a0f088f38ca8bcc') in 0.060013 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_F.prefab
  artifactKey: Guid(a8f1a6b1fe246a040a6894eacddd6b80) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_F.prefab using Guid(a8f1a6b1fe246a040a6894eacddd6b80) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7fe26740e44e8b19c4b367fa7f1740ee') in 0.0258614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_A.prefab
  artifactKey: Guid(8dd1e83924fc837499b313ada93265cc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_A.prefab using Guid(8dd1e83924fc837499b313ada93265cc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'babbbf4249e69502ad11b67f3c54030b') in 0.0301165 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_I.prefab
  artifactKey: Guid(e666f28a72b674b44ab78b2d4df9e1bd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_I.prefab using Guid(e666f28a72b674b44ab78b2d4df9e1bd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '20e7bf9dfce80688ae4daa792088a445') in 0.0299571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_C.prefab
  artifactKey: Guid(9426034b21ec7e94c9b4fe77887b4cc4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_C.prefab using Guid(9426034b21ec7e94c9b4fe77887b4cc4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9a6df17b61bc11c1bfd3aae08b9bb037') in 0.033144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000115 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Sword_F.fbx
  artifactKey: Guid(7cf5fa83a3593654484c852cbf88ee4f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Sword_F.fbx using Guid(7cf5fa83a3593654484c852cbf88ee4f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7739f9e83c05027dd08e98b5fa06ccae') in 0.0604948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_F.prefab
  artifactKey: Guid(f983798fc5971cd43b37097dbb74fb4b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_F.prefab using Guid(f983798fc5971cd43b37097dbb74fb4b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e8871fcbbb5629a7bb3141fd7b3c9d82') in 0.0384196 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_B.prefab
  artifactKey: Guid(eca570404962c4343a308d2105a47104) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_B.prefab using Guid(eca570404962c4343a308d2105a47104) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6ce7a120765216a0efdf042b03e40ff4') in 0.0329242 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000089 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_G.prefab
  artifactKey: Guid(d28c597ec1d67a947bfb454cce4cc4cd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_G.prefab using Guid(d28c597ec1d67a947bfb454cce4cc4cd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b0c476fb267552ef7bd071b3a785aa45') in 0.0312415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_D.fbx
  artifactKey: Guid(39af6f79c63bc7a41b20f6500e185f4b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_D.fbx using Guid(39af6f79c63bc7a41b20f6500e185f4b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71ea6964c01c6d12f07eab804b88e52a') in 0.0361211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Scenes/Demo1.unity
  artifactKey: Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Scenes/Demo1.unity using Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Layer Lab/3D Medieval Pack/Scenes/Demo1.unity additively'
Loaded scene 'Assets/Layer Lab/3D Medieval Pack/Scenes/Demo1.unity'
	Deserialize:            59.067 ms
	Integration:            890.815 ms
	Integration of assets:  0.010 ms
	Thread Wait Time:       1.089 ms
	Total Operation Time:   950.982 ms
 -> (artifact id: '127595404bcf6764fdc9e302cbb57f57') in 1.3920555 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3359

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_B.prefab
  artifactKey: Guid(f59b4b5ce6651f84393f7958e775bd65) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_B.prefab using Guid(f59b4b5ce6651f84393f7958e775bd65) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '443f68e5acaf50395c4e7a445ca421a7') in 0.0440205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_B.fbx
  artifactKey: Guid(4311fd3fb52fbd84bb533395b8ae589c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_B.fbx using Guid(4311fd3fb52fbd84bb533395b8ae589c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f985befe3a31175ea47e05e44cbadbad') in 0.0348636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_E.prefab
  artifactKey: Guid(47099d5a8751f264fa35067b5c76c156) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_E.prefab using Guid(47099d5a8751f264fa35067b5c76c156) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc9ef7f564d58113f9ff1c5264fc7df0') in 0.0335667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bread_B.fbx
  artifactKey: Guid(39dd614f14850704c86ac1cba429ce26) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bread_B.fbx using Guid(39dd614f14850704c86ac1cba429ce26) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4dd27f573a2df61a10d69d8efd39627a') in 0.0381379 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000134 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Yellow.prefab
  artifactKey: Guid(ba40d7629d4dcdd4387e11782121124c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Yellow.prefab using Guid(ba40d7629d4dcdd4387e11782121124c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '49350b290992b72634ddde30c8a2b963') in 0.0220321 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_D.prefab
  artifactKey: Guid(59da3f2227f916f46b3ff2272ac213c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_D.prefab using Guid(59da3f2227f916f46b3ff2272ac213c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e80717688ecb70ffcbaa2fe009d10d37') in 0.0359626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_B.prefab
  artifactKey: Guid(4e71abacdc0c08644b22be3cc9ea6780) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_B.prefab using Guid(4e71abacdc0c08644b22be3cc9ea6780) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0c81703232458750f3a342436d2f3da8') in 0.0325626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Part_Steel_C.fbx
  artifactKey: Guid(55da5dc827de79c4da11b3290d176932) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Part_Steel_C.fbx using Guid(55da5dc827de79c4da11b3290d176932) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'abb9d98583eb5820c2c7ab8f1694b09c') in 0.0381263 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pumpkin_B.prefab
  artifactKey: Guid(b2c9990dafe2ca24faf095618fef804e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pumpkin_B.prefab using Guid(b2c9990dafe2ca24faf095618fef804e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8fee8bf7f522f833aedf0251470d4f1d') in 0.027982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_G.fbx
  artifactKey: Guid(b758b633e75096d458a6fbd81424e317) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_G.fbx using Guid(b758b633e75096d458a6fbd81424e317) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f160c712d416bafbd8f7b85c7bc2f0b') in 0.0415071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_D.fbx
  artifactKey: Guid(71853e875265a144790a981e773f94d2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_D.fbx using Guid(71853e875265a144790a981e773f94d2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9645b9486beef2b7bdb5134435ebf19a') in 0.039006 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_L.fbx
  artifactKey: Guid(73b222823f4ade141abd3b1cccfaacba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_L.fbx using Guid(73b222823f4ade141abd3b1cccfaacba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '13db82030a6b2bf2bb0d9877288df6cf') in 0.0384748 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Open_E.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Open_E.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '27f009c82547e44057fdc7ca62162f18') in 0.0347807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_A.prefab
  artifactKey: Guid(2746c98c2cfc3944bbd70c1db880e65c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_A.prefab using Guid(2746c98c2cfc3944bbd70c1db880e65c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b71a57420bbedd475629918ffade29e') in 0.0302734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Catapult.prefab
  artifactKey: Guid(60da76fc055e19040b629a03ae940e6d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Catapult.prefab using Guid(60da76fc055e19040b629a03ae940e6d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54a2cc40e82aa814cc387ae820d2f50a') in 0.0282253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Beige_A.prefab
  artifactKey: Guid(aa5bb21f88590ba46a484e56d4138d8b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Beige_A.prefab using Guid(aa5bb21f88590ba46a484e56d4138d8b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e1c5e5b13435076265cc580756ff93ae') in 0.0298785 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_G.prefab
  artifactKey: Guid(9a1d7b254d500b646b79824d1a6ec1f6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_G.prefab using Guid(9a1d7b254d500b646b79824d1a6ec1f6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2feac46ac4043affaa2c44005d1f1818') in 0.0363807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_D.prefab
  artifactKey: Guid(108b2949af576544cbf443cfbb79f25d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_D.prefab using Guid(108b2949af576544cbf443cfbb79f25d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '374b7db00ff15d6ed6c7c4b0f53bd1df') in 0.0325254 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_D.prefab
  artifactKey: Guid(8655ef55f26834e46bd9012739204433) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_D.prefab using Guid(8655ef55f26834e46bd9012739204433) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '43e20f7cc36e871c3265eb51c12052fb') in 0.0351269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_E.fbx
  artifactKey: Guid(18a09f0f25414f34a9389cc41bc0c09b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_E.fbx using Guid(18a09f0f25414f34a9389cc41bc0c09b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad9a1811943545f2694b21199a6fd4c2') in 0.0416395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BalanceScale.prefab
  artifactKey: Guid(f1eaea8a13c030e4bafee40b015fbd82) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BalanceScale.prefab using Guid(f1eaea8a13c030e4bafee40b015fbd82) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98c5494cc9cea08fc74861b6f46e42e5') in 0.0275156 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_I.fbx
  artifactKey: Guid(2a92549635d9a2b4fae2edd599b10313) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_I.fbx using Guid(2a92549635d9a2b4fae2edd599b10313) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd5ad91b422212a468302bcd8175caf29') in 0.0438176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_E.fbx
  artifactKey: Guid(14c7e0a72218f8c4697c54cfcdc44dd6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_E.fbx using Guid(14c7e0a72218f8c4697c54cfcdc44dd6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a007991b7f4be2155c54df294919a25b') in 0.0481188 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Red.prefab
  artifactKey: Guid(47a6f69b730842a45b13634c24e06ebe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Red.prefab using Guid(47a6f69b730842a45b13634c24e06ebe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f3c02671eda702e689d8afac9f742f28') in 0.0305267 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_D.prefab
  artifactKey: Guid(25ab957f59d257047b16055027e6cf97) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_D.prefab using Guid(25ab957f59d257047b16055027e6cf97) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a0230846ed23e66c3a9df8ef78d0944') in 0.0370748 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_F.prefab
  artifactKey: Guid(dea4af319a3e3c948b7cbed57e61235c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_F.prefab using Guid(dea4af319a3e3c948b7cbed57e61235c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '694cbc7423a5e67256db27047fad7677') in 0.0312658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_B.prefab
  artifactKey: Guid(fee301f099047f042a49b1c068b833cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_B.prefab using Guid(fee301f099047f042a49b1c068b833cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a4ea224abbecc32279f17dd54e0e24f') in 0.028738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_A.prefab
  artifactKey: Guid(86e8f1324766f5343ae7e930a7782c2f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_A.prefab using Guid(86e8f1324766f5343ae7e930a7782c2f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c7c529597dc041e009e2ba0ddd629288') in 0.0290222 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_D.prefab
  artifactKey: Guid(13f95c4c097bbd34fad762466b949f86) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_D.prefab using Guid(13f95c4c097bbd34fad762466b949f86) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cee8a4c7118d905efe43b59bd24d2243') in 0.0259765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_A.fbx
  artifactKey: Guid(2e1198f32012de84ea7c74110855c820) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_A.fbx using Guid(2e1198f32012de84ea7c74110855c820) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '65785f16a7f85e78b9740a6b38d740b8') in 0.0348293 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wood_C.fbx
  artifactKey: Guid(38e705fe095b98942a8757e8fc120ac1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wood_C.fbx using Guid(38e705fe095b98942a8757e8fc120ac1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '92b5dcba5fe3d0ad4af41e3cd196bae2') in 0.0430069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_G.prefab
  artifactKey: Guid(2c99e27400f0fd04ca6577bcddd90f52) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_G.prefab using Guid(2c99e27400f0fd04ca6577bcddd90f52) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f080ec98586bfb51868a9156b68790d7') in 0.0322805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Grass.fbx
  artifactKey: Guid(0f0166cc51ecfc0408d63467b6c6c74b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Grass.fbx using Guid(0f0166cc51ecfc0408d63467b6c6c74b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2595194b1ba18d61e4ff2175a57b4b3b') in 0.0375538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_F.fbx
  artifactKey: Guid(5fe44285dd0202a4aa6cd1cbdf520f42) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_F.fbx using Guid(5fe44285dd0202a4aa6cd1cbdf520f42) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b92bf0bb92a33193d3a6742792db484') in 0.0406025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wood_A.fbx
  artifactKey: Guid(34ab1f98f50e42d4aaa81d27a441f53c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wood_A.fbx using Guid(34ab1f98f50e42d4aaa81d27a441f53c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d072df80e7e977993df6f189b87189c') in 0.0465062 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Candelabrum_B.fbx
  artifactKey: Guid(d25f8a0c0770904459a78fd55e11befd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Candelabrum_B.fbx using Guid(d25f8a0c0770904459a78fd55e11befd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '095a8756eb38360e539125d9773b6443') in 0.0549804 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cup_A.prefab
  artifactKey: Guid(70fb689ccac5ecc43ade338ed50ed559) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cup_A.prefab using Guid(70fb689ccac5ecc43ade338ed50ed559) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a3ff06361f1bbc5fc425ecc16a70392') in 0.030736 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_F.fbx
  artifactKey: Guid(e2c7977ed7055e24a84eb31f04073e84) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_F.fbx using Guid(e2c7977ed7055e24a84eb31f04073e84) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '430b94280f6b277962c887b7e701644e') in 0.0400948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_I.prefab
  artifactKey: Guid(286f638552fb094409ed49fe87cd841a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_I.prefab using Guid(286f638552fb094409ed49fe87cd841a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec71f426b10d544631c5a38ca2baefcf') in 0.0291597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_D.fbx
  artifactKey: Guid(7c58a4f3ac79d7f40aa25a63c88470e3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_D.fbx using Guid(7c58a4f3ac79d7f40aa25a63c88470e3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2ffc3e178fc3315815fc39027f02c3c') in 0.0549336 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Banana_Yellow.fbx
  artifactKey: Guid(22d6d289d7fc0c341834a08e7d8ff3fd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Banana_Yellow.fbx using Guid(22d6d289d7fc0c341834a08e7d8ff3fd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6bac04d3c38310648680467fca68004') in 0.0443107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_C.prefab
  artifactKey: Guid(e9c7da5341073d84d8005faa9edac7fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_C.prefab using Guid(e9c7da5341073d84d8005faa9edac7fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c8958688fd85c56b9a269cbdeb8887bf') in 0.0309989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_J.fbx
  artifactKey: Guid(360cacced95506b4db7dd981e191e57c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_J.fbx using Guid(360cacced95506b4db7dd981e191e57c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e374cd28ed4a8430b98b0de283cd5e44') in 0.0394916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tent_C.fbx
  artifactKey: Guid(6a3119e0e49ffac46a0151635f083378) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tent_C.fbx using Guid(6a3119e0e49ffac46a0151635f083378) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5cd9e9aed943f47791029c22c96c6b1a') in 0.0358442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Manger_D.fbx
  artifactKey: Guid(a814e2df893af7c49a5b6f3803dc25f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Manger_D.fbx using Guid(a814e2df893af7c49a5b6f3803dc25f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '65942cf272a582044676cbd18ec41f8a') in 0.0436433 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_C.prefab
  artifactKey: Guid(cbc7a7d2caa56a54b89502dd88aff94f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_C.prefab using Guid(cbc7a7d2caa56a54b89502dd88aff94f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2722f8974c145240cc7720752df38c0b') in 0.0322465 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Mushroom_Brown.fbx
  artifactKey: Guid(23c173db5df774640b683aab470468d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Mushroom_Brown.fbx using Guid(23c173db5df774640b683aab470468d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '643e04daec04693d83ce86c67c7ea7d1') in 0.0365423 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_D.prefab
  artifactKey: Guid(bfb523f96e9f42849ab2eef3dee0e284) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_D.prefab using Guid(bfb523f96e9f42849ab2eef3dee0e284) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5d76b8017f1d5ac3624880bad69bf0a9') in 0.0337905 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fire_A.fbx
  artifactKey: Guid(a1f5daf20b62226438369a2e4fe5d58b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fire_A.fbx using Guid(a1f5daf20b62226438369a2e4fe5d58b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '00cbe888d1643a3d391a12b4dff251c7') in 0.038934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_B.prefab
  artifactKey: Guid(3498abc376298eb429918e2768d5810e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_B.prefab using Guid(3498abc376298eb429918e2768d5810e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0592316f603e66555c2cedbf421a01ea') in 0.022446 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_B.prefab
  artifactKey: Guid(cdb23be4a0ec2914496262d81181b6a4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_B.prefab using Guid(cdb23be4a0ec2914496262d81181b6a4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6c7d5eb1c4ba24cb0cbefbdbf442ee94') in 0.0252237 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_A.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_A.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f7c5708a5c719559f666bbfa54c05a8f') in 0.0242303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_Part_A.prefab
  artifactKey: Guid(130dcbb0cab492d49863701249d27bb3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_Part_A.prefab using Guid(130dcbb0cab492d49863701249d27bb3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cdb196aec8999b654cede421e35a38f4') in 0.028359 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

Editor requested this worker to shutdown with reason: balancing worker count downwards
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0