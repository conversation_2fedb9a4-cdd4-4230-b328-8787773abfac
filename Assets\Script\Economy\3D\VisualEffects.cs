using UnityEngine;
using System.Collections;

namespace EconomySystem.Shop3D
{
    /// <summary>
    /// <PERSON>u<PERSON>n lý các hiệu ứng visual cho cửa hàng 3D
    /// Bao gồm particle effects, screen effects, animations
    /// </summary>
    public class VisualEffects : MonoBehaviour
    {
        #region Singleton
        private static VisualEffects _instance;
        public static VisualEffects Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<VisualEffects>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("VisualEffects");
                        _instance = go.AddComponent<VisualEffects>();
                    }
                }
                return _instance;
            }
        }
        #endregion

        [Header("Purchase Effects")]
        [SerializeField] private ParticleSystem purchaseParticles;
        [SerializeField] private ParticleSystem coinParticles;
        [SerializeField] private GameObject purchaseSuccessEffect;
        [SerializeField] private Color purchaseColor = Color.green;

        [Header("Hover Effects")]
        [SerializeField] private ParticleSystem hoverParticles;
        [SerializeField] private GameObject hoverGlowEffect;
        [SerializeField] private Color hoverColor = Color.yellow;

        [Header("Screen Effects")]
        [SerializeField] private Camera effectCamera;
        [SerializeField] private float screenShakeIntensity = 0.1f;
        [SerializeField] private float screenShakeDuration = 0.2f;

        [Header("UI Effects")]
        [SerializeField] private GameObject floatingTextPrefab;
        [SerializeField] private Transform floatingTextParent;
        [SerializeField] private float floatingTextDuration = 2f;

        [Header("Outline Effects")]
        [SerializeField] private Material outlineMaterial;
        [SerializeField] private float outlineWidth = 0.1f;

        [Header("Audio Effects")]
        [SerializeField] private AudioSource effectAudioSource;
        [SerializeField] private AudioClip purchaseSound;
        [SerializeField] private AudioClip hoverSound;
        [SerializeField] private AudioClip errorSound;
        [SerializeField] private AudioClip coinSound;

        // Private variables
        private Camera originalCamera;
        private Vector3 originalCameraPosition;
        private Coroutine screenShakeCoroutine;

        #region Unity Methods
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
                return;
            }

            InitializeComponents();
        }

        private void Start()
        {
            SetupEffects();
            RegisterEvents();
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }
        #endregion

        #region Initialization
        private void InitializeComponents()
        {
            // Get effect camera
            if (effectCamera == null)
            {
                effectCamera = Camera.main;
            }

            if (effectCamera != null)
            {
                originalCamera = effectCamera;
                originalCameraPosition = effectCamera.transform.localPosition;
            }

            // Setup audio source
            if (effectAudioSource == null)
            {
                effectAudioSource = gameObject.AddComponent<AudioSource>();
                effectAudioSource.playOnAwake = false;
                effectAudioSource.spatialBlend = 0f; // 2D sound
            }

            // Setup floating text parent
            if (floatingTextParent == null && floatingTextPrefab != null)
            {
                GameObject parent = new GameObject("FloatingTextParent");
                parent.transform.SetParent(transform);
                floatingTextParent = parent.transform;
            }
        }

        private void SetupEffects()
        {
            // Configure particle systems
            SetupParticleSystem(purchaseParticles, purchaseColor);
            SetupParticleSystem(coinParticles, Color.yellow);
            SetupParticleSystem(hoverParticles, hoverColor);
        }

        private void SetupParticleSystem(ParticleSystem particles, Color color)
        {
            if (particles == null) return;

            var main = particles.main;
            main.startColor = color;
            main.playOnAwake = false;

            var emission = particles.emission;
            emission.enabled = true;

            var shape = particles.shape;
            shape.enabled = true;
        }

        private void RegisterEvents()
        {
            // Register to economy events
            if (ShopManager.Instance != null)
            {
                ShopManager.OnItemPurchased += OnItemPurchased;
                ShopManager.OnItemSold += OnItemSold;
                ShopManager.OnTransactionFailed += OnTransactionFailed;
            }

            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.OnCurrencyChanged += OnCurrencyChanged;
            }
        }

        private void UnregisterEvents()
        {
            if (ShopManager.Instance != null)
            {
                ShopManager.OnItemPurchased -= OnItemPurchased;
                ShopManager.OnItemSold -= OnItemSold;
                ShopManager.OnTransactionFailed -= OnTransactionFailed;
            }

            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.OnCurrencyChanged -= OnCurrencyChanged;
            }
        }
        #endregion

        #region Event Handlers
        private void OnItemPurchased(Item item, int quantity)
        {
            PlayPurchaseEffect(item, quantity, true);
        }

        private void OnItemSold(Item item, int quantity)
        {
            PlayPurchaseEffect(item, quantity, false);
        }

        private void OnTransactionFailed(string reason)
        {
            PlayErrorEffect();
            ShowFloatingText(reason, Color.red);
        }

        private void OnCurrencyChanged(int newAmount)
        {
            PlayCoinEffect();
        }
        #endregion

        #region Purchase Effects
        public void PlayPurchaseEffect(Item item, int quantity, bool isPurchase)
        {
            Vector3 effectPosition = GetCurrentInteractablePosition();
            
            // Play particle effect
            if (purchaseParticles != null)
            {
                purchaseParticles.transform.position = effectPosition;
                purchaseParticles.Play();
            }

            // Play success effect
            if (purchaseSuccessEffect != null)
            {
                GameObject effect = Instantiate(purchaseSuccessEffect, effectPosition, Quaternion.identity);
                Destroy(effect, 3f);
            }

            // Screen shake
            TriggerScreenShake();

            // Floating text
            string action = isPurchase ? "Mua" : "Bán";
            string text = $"{action} {quantity}x {item.TenVatPham}";
            Color textColor = isPurchase ? Color.green : Color.blue;
            ShowFloatingText(text, textColor, effectPosition);

            // Sound effect
            PlaySound(purchaseSound);
        }

        public void PlayHoverEffect(Vector3 position)
        {
            if (hoverParticles != null)
            {
                hoverParticles.transform.position = position;
                hoverParticles.Play();
            }

            if (hoverGlowEffect != null)
            {
                GameObject glow = Instantiate(hoverGlowEffect, position, Quaternion.identity);
                Destroy(glow, 1f);
            }

            PlaySound(hoverSound);
        }

        public void PlayCoinEffect()
        {
            if (coinParticles != null)
            {
                coinParticles.Play();
            }

            PlaySound(coinSound);
        }

        public void PlayErrorEffect()
        {
            // Red screen flash effect
            StartCoroutine(ScreenFlashCoroutine(Color.red, 0.3f));
            
            // Error sound
            PlaySound(errorSound);
            
            // Screen shake
            TriggerScreenShake(screenShakeIntensity * 1.5f);
        }
        #endregion

        #region Outline Effects
        public void AddOutlineToObject(GameObject obj, Color outlineColor)
        {
            if (obj == null) return;

            Outline outline = obj.GetComponent<Outline>();
            if (outline == null)
            {
                outline = obj.AddComponent<Outline>();
            }

            outline.OutlineColor = outlineColor;
            outline.OutlineWidth = outlineWidth;
            outline.enabled = true;
        }

        public void RemoveOutlineFromObject(GameObject obj)
        {
            if (obj == null) return;

            Outline outline = obj.GetComponent<Outline>();
            if (outline != null)
            {
                outline.enabled = false;
            }
        }

        public void HighlightObject(GameObject obj, Color highlightColor)
        {
            if (obj == null) return;

            // Add outline
            AddOutlineToObject(obj, highlightColor);

            // Add glow effect
            if (hoverGlowEffect != null)
            {
                GameObject glow = Instantiate(hoverGlowEffect, obj.transform.position, Quaternion.identity);
                glow.transform.SetParent(obj.transform);
                
                // Auto destroy after some time
                Destroy(glow, 5f);
            }
        }

        public void UnhighlightObject(GameObject obj)
        {
            RemoveOutlineFromObject(obj);
            
            // Remove glow effects
            Transform[] children = obj.GetComponentsInChildren<Transform>();
            foreach (Transform child in children)
            {
                if (child.name.Contains("Glow"))
                {
                    Destroy(child.gameObject);
                }
            }
        }
        #endregion

        #region Screen Effects
        public void TriggerScreenShake(float intensity = -1f, float duration = -1f)
        {
            if (effectCamera == null) return;

            if (intensity < 0) intensity = screenShakeIntensity;
            if (duration < 0) duration = screenShakeDuration;

            if (screenShakeCoroutine != null)
            {
                StopCoroutine(screenShakeCoroutine);
            }

            screenShakeCoroutine = StartCoroutine(ScreenShakeCoroutine(intensity, duration));
        }

        private IEnumerator ScreenShakeCoroutine(float intensity, float duration)
        {
            float elapsed = 0f;

            while (elapsed < duration)
            {
                float x = Random.Range(-1f, 1f) * intensity;
                float y = Random.Range(-1f, 1f) * intensity;

                effectCamera.transform.localPosition = originalCameraPosition + new Vector3(x, y, 0);

                elapsed += Time.deltaTime;
                yield return null;
            }

            effectCamera.transform.localPosition = originalCameraPosition;
            screenShakeCoroutine = null;
        }

        private IEnumerator ScreenFlashCoroutine(Color flashColor, float duration)
        {
            // Create flash overlay
            GameObject flashOverlay = new GameObject("ScreenFlash");
            Canvas canvas = flashOverlay.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 1000;

            UnityEngine.UI.Image flashImage = flashOverlay.AddComponent<UnityEngine.UI.Image>();
            flashImage.color = new Color(flashColor.r, flashColor.g, flashColor.b, 0.5f);

            // Fade out
            float elapsed = 0f;
            Color startColor = flashImage.color;
            Color endColor = new Color(flashColor.r, flashColor.g, flashColor.b, 0f);

            while (elapsed < duration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / duration;
                flashImage.color = Color.Lerp(startColor, endColor, t);
                yield return null;
            }

            Destroy(flashOverlay);
        }
        #endregion

        #region Floating Text
        public void ShowFloatingText(string text, Color color, Vector3? worldPosition = null)
        {
            if (floatingTextPrefab == null) return;

            Vector3 spawnPosition = worldPosition ?? GetCurrentInteractablePosition();
            
            GameObject floatingText = Instantiate(floatingTextPrefab, spawnPosition, Quaternion.identity, floatingTextParent);
            
            // Configure text
            TMPro.TextMeshPro textMesh = floatingText.GetComponent<TMPro.TextMeshPro>();
            if (textMesh != null)
            {
                textMesh.text = text;
                textMesh.color = color;
                textMesh.fontSize = 4f;
            }

            // Animate floating text
            StartCoroutine(FloatingTextAnimation(floatingText));
        }

        private IEnumerator FloatingTextAnimation(GameObject textObj)
        {
            Vector3 startPos = textObj.transform.position;
            Vector3 endPos = startPos + Vector3.up * 2f;
            
            float elapsed = 0f;
            
            while (elapsed < floatingTextDuration)
            {
                elapsed += Time.deltaTime;
                float t = elapsed / floatingTextDuration;
                
                // Move up
                textObj.transform.position = Vector3.Lerp(startPos, endPos, t);
                
                // Fade out
                TMPro.TextMeshPro textMesh = textObj.GetComponent<TMPro.TextMeshPro>();
                if (textMesh != null)
                {
                    Color color = textMesh.color;
                    color.a = 1f - t;
                    textMesh.color = color;
                }
                
                yield return null;
            }
            
            Destroy(textObj);
        }
        #endregion

        #region Audio
        private void PlaySound(AudioClip clip)
        {
            if (effectAudioSource != null && clip != null)
            {
                effectAudioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Helper Methods
        private Vector3 GetCurrentInteractablePosition()
        {
            var currentInteractable = EconomyPlayerAdapter.Instance?.GetCurrentEconomyInteractable();
            if (currentInteractable != null)
            {
                return currentInteractable.transform.position + Vector3.up;
            }

            // Fallback to camera position
            if (effectCamera != null)
            {
                return effectCamera.transform.position + effectCamera.transform.forward * 2f;
            }

            return Vector3.zero;
        }
        #endregion

        #region Public Methods
        public void SetEffectIntensity(float intensity)
        {
            screenShakeIntensity = intensity;
        }

        public void EnableEffects(bool enable)
        {
            enabled = enable;
        }
        #endregion

        #region Editor Methods
        #if UNITY_EDITOR
        [ContextMenu("Test Purchase Effect")]
        private void TestPurchaseEffect()
        {
            if (Application.isPlaying)
            {
                var testItem = new Item("test", "Test Item", "Test description", 100, 50);
                PlayPurchaseEffect(testItem, 1, true);
            }
        }

        [ContextMenu("Test Screen Shake")]
        private void TestScreenShake()
        {
            if (Application.isPlaying)
            {
                TriggerScreenShake();
            }
        }

        [ContextMenu("Test Floating Text")]
        private void TestFloatingText()
        {
            if (Application.isPlaying)
            {
                ShowFloatingText("Test Text!", Color.yellow);
            }
        }
        #endif
        #endregion
    }
}
