using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace EconomySystem
{
    /// <summary>
    /// UI hiển thị inventory của người chơi
    /// Tích hợp với InventoryManager
    /// </summary>
    public class InventoryUI : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Transform containerVatPham;
        [SerializeField] private GameObject prefabInventoryItem;
        [SerializeField] private ScrollRect scrollRect;

        [Header("Thông Tin")]
        [SerializeField] private TextMeshProUGUI textSoSlot;
        [SerializeField] private TextMeshProUGUI textTongSoVatPham;
        [SerializeField] private Slider sliderSoSlot;

        [Header("Bộ Lọc")]
        [SerializeField] private TMP_Dropdown dropdownLoaiVatPham;
        [SerializeField] private Button buttonSapXep;
        [SerializeField] private Button buttonXoaTatCa;

        [Header("Cài Đặt")]
        [SerializeField] private bool tuDongCapNhat = true;
        [SerializeField] private bool hienThiLog = false;

        private List<InventoryItemUI> danhSachItemUI = new List<InventoryItemUI>();

        #region Unity Methods
        private void Start()
        {
            KhoiTaoUI();
            
            if (tuDongCapNhat)
            {
                DangKyEvents();
            }

            CapNhatHienThi();
        }

        private void OnDestroy()
        {
            HuyDangKyEvents();
        }
        #endregion

        #region Initialization
        private void KhoiTaoUI()
        {
            // Thiết lập dropdown loại vật phẩm
            if (dropdownLoaiVatPham != null)
            {
                ThietLapDropdownLoaiVatPham();
                dropdownLoaiVatPham.onValueChanged.AddListener(OnLoaiVatPhamThayDoi);
            }

            // Thiết lập buttons
            if (buttonSapXep != null)
                buttonSapXep.onClick.AddListener(SapXepInventory);

            if (buttonXoaTatCa != null)
                buttonXoaTatCa.onClick.AddListener(XoaTatCaInventory);

            // Kiểm tra prefab
            if (prefabInventoryItem == null)
            {
                Debug.LogError("Prefab InventoryItem chưa được gán!");
            }

            if (containerVatPham == null)
            {
                Debug.LogError("Container vật phẩm chưa được gán!");
            }
        }

        private void ThietLapDropdownLoaiVatPham()
        {
            dropdownLoaiVatPham.ClearOptions();
            
            var options = new List<string> { "Tất Cả" };
            
            // Thêm các loại vật phẩm
            foreach (ItemType loai in System.Enum.GetValues(typeof(ItemType)))
            {
                options.Add(LayTenLoaiVatPham(loai));
            }

            dropdownLoaiVatPham.AddOptions(options);
        }

        private void DangKyEvents()
        {
            InventoryManager.OnInventoryChanged += OnInventoryThayDoi;
            InventoryManager.OnItemAdded += OnThemVatPham;
            InventoryManager.OnItemRemoved += OnXoaVatPham;
            InventoryManager.OnInventoryFull += OnInventoryDay;
        }

        private void HuyDangKyEvents()
        {
            InventoryManager.OnInventoryChanged -= OnInventoryThayDoi;
            InventoryManager.OnItemAdded -= OnThemVatPham;
            InventoryManager.OnItemRemoved -= OnXoaVatPham;
            InventoryManager.OnInventoryFull -= OnInventoryDay;
        }
        #endregion

        #region Event Handlers
        private void OnInventoryThayDoi()
        {
            CapNhatHienThi();
        }

        private void OnThemVatPham(Item item, int soLuong)
        {
            if (hienThiLog)
                Debug.Log($"Inventory UI: Đã thêm {soLuong}x {item.TenVatPham}");
        }

        private void OnXoaVatPham(Item item, int soLuong)
        {
            if (hienThiLog)
                Debug.Log($"Inventory UI: Đã xóa {soLuong}x {item.TenVatPham}");
        }

        private void OnInventoryDay()
        {
            if (hienThiLog)
                Debug.Log("Inventory UI: Inventory đã đầy!");
        }

        private void OnLoaiVatPhamThayDoi(int index)
        {
            ApDungBoLoc();
        }
        #endregion

        #region UI Updates
        /// <summary>
        /// Cập nhật hiển thị inventory
        /// </summary>
        public void CapNhatHienThi()
        {
            if (InventoryManager.Instance == null) return;

            // Cập nhật thông tin tổng
            CapNhatThongTinTong();

            // Hiển thị danh sách vật phẩm
            ApDungBoLoc();
        }

        /// <summary>
        /// Áp dụng bộ lọc
        /// </summary>
        private void ApDungBoLoc()
        {
            if (InventoryManager.Instance == null) return;

            var danhSachVatPham = InventoryManager.Instance.DanhSachVatPham;

            // Lọc theo loại nếu được chọn
            if (dropdownLoaiVatPham != null && dropdownLoaiVatPham.value > 0)
            {
                ItemType loaiDaChon = (ItemType)(dropdownLoaiVatPham.value - 1);
                danhSachVatPham = InventoryManager.Instance.LayVatPhamTheoLoai(loaiDaChon);
            }

            // Hiển thị danh sách đã lọc
            HienThiDanhSachVatPham(danhSachVatPham);
        }

        /// <summary>
        /// Hiển thị danh sách vật phẩm
        /// </summary>
        /// <param name="danhSach">Danh sách vật phẩm</param>
        private void HienThiDanhSachVatPham(List<InventoryItem> danhSach)
        {
            // Xóa các item UI cũ
            XoaTatCaItemUI();

            // Tạo item UI mới
            foreach (var inventoryItem in danhSach)
            {
                TaoInventoryItemUI(inventoryItem);
            }

            // Reset scroll về đầu
            if (scrollRect != null)
            {
                scrollRect.verticalNormalizedPosition = 1f;
            }

            if (hienThiLog)
                Debug.Log($"Hiển thị {danhSach.Count} loại vật phẩm trong inventory");
        }

        /// <summary>
        /// Tạo UI cho một vật phẩm trong inventory
        /// </summary>
        /// <param name="inventoryItem">Vật phẩm trong inventory</param>
        private void TaoInventoryItemUI(InventoryItem inventoryItem)
        {
            if (prefabInventoryItem == null || containerVatPham == null) return;

            GameObject itemObj = Instantiate(prefabInventoryItem, containerVatPham);
            InventoryItemUI itemUI = itemObj.GetComponent<InventoryItemUI>();

            if (itemUI != null)
            {
                itemUI.ThietLapInventoryItem(inventoryItem);
                danhSachItemUI.Add(itemUI);
            }
            else
            {
                Debug.LogError("Prefab InventoryItem không có component InventoryItemUI!");
                Destroy(itemObj);
            }
        }

        /// <summary>
        /// Xóa tất cả item UI
        /// </summary>
        private void XoaTatCaItemUI()
        {
            foreach (var itemUI in danhSachItemUI)
            {
                if (itemUI != null)
                {
                    Destroy(itemUI.gameObject);
                }
            }

            danhSachItemUI.Clear();
        }

        /// <summary>
        /// Cập nhật thông tin tổng
        /// </summary>
        private void CapNhatThongTinTong()
        {
            if (InventoryManager.Instance == null) return;

            var inventory = InventoryManager.Instance;

            // Cập nhật text số slot
            if (textSoSlot != null)
            {
                textSoSlot.text = $"{inventory.SoSlotDaSuDung} / {inventory.SoSlotDaSuDung + inventory.SoSlotTrong}";
            }

            // Cập nhật text tổng số vật phẩm
            if (textTongSoVatPham != null)
            {
                textTongSoVatPham.text = $"Tổng: {inventory.TongSoVatPham} vật phẩm";
            }

            // Cập nhật slider
            if (sliderSoSlot != null)
            {
                int tongSlot = inventory.SoSlotDaSuDung + inventory.SoSlotTrong;
                sliderSoSlot.maxValue = tongSlot;
                sliderSoSlot.value = inventory.SoSlotDaSuDung;
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Sắp xếp inventory
        /// </summary>
        public void SapXepInventory()
        {
            if (InventoryManager.Instance != null)
            {
                InventoryManager.Instance.SapXepInventory();
            }
        }

        /// <summary>
        /// Xóa tất cả inventory (với xác nhận)
        /// </summary>
        public void XoaTatCaInventory()
        {
            #if UNITY_EDITOR
            if (UnityEditor.EditorUtility.DisplayDialog("Xác Nhận", 
                "Bạn có chắc muốn xóa tất cả vật phẩm trong inventory?", "Có", "Không"))
            {
                if (InventoryManager.Instance != null)
                {
                    InventoryManager.Instance.XoaToanBoInventory();
                }
            }
            #else
            // Trong build, có thể sử dụng UI popup để xác nhận
            if (InventoryManager.Instance != null)
            {
                InventoryManager.Instance.XoaToanBoInventory();
            }
            #endif
        }

        /// <summary>
        /// Đặt bộ lọc loại vật phẩm
        /// </summary>
        /// <param name="loai">Loại vật phẩm</param>
        public void DatBoLocLoai(ItemType loai)
        {
            if (dropdownLoaiVatPham != null)
            {
                dropdownLoaiVatPham.value = (int)loai + 1;
            }
        }

        /// <summary>
        /// Xóa bộ lọc
        /// </summary>
        public void XoaBoLoc()
        {
            if (dropdownLoaiVatPham != null)
            {
                dropdownLoaiVatPham.value = 0;
            }
        }

        /// <summary>
        /// Làm mới hiển thị
        /// </summary>
        public void LamMoiHienThi()
        {
            CapNhatHienThi();
        }
        #endregion

        #region Private Methods
        private string LayTenLoaiVatPham(ItemType loai)
        {
            switch (loai)
            {
                case ItemType.VatLieu: return "Vật Liệu";
                case ItemType.CongCu: return "Công Cụ";
                case ItemType.VuKhi: return "Vũ Khí";
                case ItemType.GiapGiap: return "Giáp";
                case ItemType.TieuHao: return "Tiêu Hao";
                case ItemType.QuyGia: return "Quý Giá";
                case ItemType.Khac: return "Khác";
                default: return "Không Xác Định";
            }
        }
        #endregion

        #region Properties
        /// <summary>
        /// Số lượng vật phẩm đang hiển thị
        /// </summary>
        public int SoLuongVatPhamHienThi => danhSachItemUI.Count;
        #endregion
    }

    /// <summary>
    /// UI component cho một vật phẩm trong inventory
    /// </summary>
    public class InventoryItemUI : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Image imageIcon;
        [SerializeField] private TextMeshProUGUI textTenVatPham;
        [SerializeField] private TextMeshProUGUI textSoLuong;
        [SerializeField] private TextMeshProUGUI textLoaiVatPham;
        [SerializeField] private Button buttonSuDung;
        [SerializeField] private Button buttonBan;

        private InventoryItem inventoryItem;

        /// <summary>
        /// Thiết lập inventory item cho UI này
        /// </summary>
        /// <param name="item">Inventory item</param>
        public void ThietLapInventoryItem(InventoryItem item)
        {
            inventoryItem = item;
            CapNhatHienThi();
        }

        /// <summary>
        /// Cập nhật hiển thị
        /// </summary>
        public void CapNhatHienThi()
        {
            if (inventoryItem?.Item == null) return;

            var item = inventoryItem.Item;

            // Cập nhật thông tin
            if (textTenVatPham != null)
                textTenVatPham.text = item.TenVatPham;

            if (textSoLuong != null)
                textSoLuong.text = $"x{inventoryItem.SoLuong}";

            if (textLoaiVatPham != null)
                textLoaiVatPham.text = item.LayTenLoaiVatPham();

            // Cập nhật icon
            if (imageIcon != null)
            {
                if (item.Icon != null)
                {
                    imageIcon.sprite = item.Icon;
                    imageIcon.gameObject.SetActive(true);
                }
                else
                {
                    imageIcon.gameObject.SetActive(false);
                }
            }

            // Cập nhật buttons
            if (buttonSuDung != null)
            {
                buttonSuDung.onClick.RemoveAllListeners();
                buttonSuDung.onClick.AddListener(SuDungVatPham);
            }

            if (buttonBan != null)
            {
                buttonBan.onClick.RemoveAllListeners();
                buttonBan.onClick.AddListener(BanVatPham);
                buttonBan.interactable = item.CoTheBan;
            }
        }

        private void SuDungVatPham()
        {
            // Implement logic sử dụng vật phẩm ở đây
            Debug.Log($"Sử dụng {inventoryItem.Item.TenVatPham}");
        }

        private void BanVatPham()
        {
            if (ShopManager.Instance != null)
            {
                ShopManager.Instance.BanVatPham(inventoryItem.Item, 1);
            }
        }

        public InventoryItem InventoryItem => inventoryItem;
    }
}
