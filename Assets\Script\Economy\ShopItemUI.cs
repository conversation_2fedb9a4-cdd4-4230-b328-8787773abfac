using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace EconomySystem
{
    /// <summary>
    /// UI component cho một vật phẩm trong cửa hàng
    /// Hiển thị thông tin và xử lý mua/bán
    /// </summary>
    public class ShopItemUI : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Image imageIcon;
        [SerializeField] private TextMeshProUGUI textTenVatPham;
        [SerializeField] private TextMeshProUGUI textMoTa;
        [SerializeField] private TextMeshProUGUI textGiaMua;
        [SerializeField] private TextMeshProUGUI textGiaBan;
        [SerializeField] private TextMeshP<PERSON>UGUI textLoaiVatPham;
        [SerializeField] private TextMeshProUGUI textDoHiem;

        [Header("Buttons")]
        [SerializeField] private Button buttonMua;
        [SerializeField] private But<PERSON> buttonBan;
        [SerializeField] private Button buttonThongTin;

        [Header("Số Lượng")]
        [SerializeField] private TMP_InputField inputSoLuong;
        [SerializeField] private Button buttonTangSoLuong;
        [SerializeField] private Button buttonGiamSoLuong;

        [Header("Hiển Thị")]
        [SerializeField] private Image imageBorder;
        [SerializeField] private GameObject tagVatPhamMoi;
        [SerializeField] private GameObject tagVatPhamNoiBat;

        [Header("Cài Đặt")]
        [SerializeField] private bool hienThiLog = false;
        [SerializeField] private int soLuongMacDinh = 1;
        [SerializeField] private int soLuongToiDa = 99;

        private Item vatPham;
        private int soLuongHienTai = 1;

        #region Unity Methods
        private void Start()
        {
            KhoiTaoUI();
        }

        #if UNITY_EDITOR
        private void OnValidate()
        {
            soLuongMacDinh = Mathf.Max(1, soLuongMacDinh);
            soLuongToiDa = Mathf.Max(soLuongMacDinh, soLuongToiDa);
        }
        #endif
        #endregion

        #region Initialization
        private void KhoiTaoUI()
        {
            // Thiết lập buttons
            if (buttonMua != null)
                buttonMua.onClick.AddListener(MuaVatPham);

            if (buttonBan != null)
                buttonBan.onClick.AddListener(BanVatPham);

            if (buttonThongTin != null)
                buttonThongTin.onClick.AddListener(HienThiThongTinChiTiet);

            if (buttonTangSoLuong != null)
                buttonTangSoLuong.onClick.AddListener(TangSoLuong);

            if (buttonGiamSoLuong != null)
                buttonGiamSoLuong.onClick.AddListener(GiamSoLuong);

            // Thiết lập input số lượng
            if (inputSoLuong != null)
            {
                inputSoLuong.onValueChanged.AddListener(OnSoLuongThayDoi);
                inputSoLuong.text = soLuongMacDinh.ToString();
            }

            soLuongHienTai = soLuongMacDinh;
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Thiết lập vật phẩm cho UI này
        /// </summary>
        /// <param name="item">Vật phẩm</param>
        public void ThietLapVatPham(Item item)
        {
            vatPham = item;
            CapNhatHienThi();
        }

        /// <summary>
        /// Cập nhật hiển thị thông tin vật phẩm
        /// </summary>
        public void CapNhatHienThi()
        {
            if (vatPham == null) return;

            // Cập nhật thông tin cơ bản
            if (textTenVatPham != null)
                textTenVatPham.text = vatPham.TenVatPham;

            if (textMoTa != null)
                textMoTa.text = vatPham.MoTa;

            if (textGiaMua != null)
                textGiaMua.text = $"{vatPham.GiaMua:N0} Lea";

            if (textGiaBan != null)
                textGiaBan.text = $"{vatPham.GiaBan:N0} Lea";

            if (textLoaiVatPham != null)
                textLoaiVatPham.text = vatPham.LayTenLoaiVatPham();

            if (textDoHiem != null)
            {
                textDoHiem.text = vatPham.LayTenDoHiem();
                textDoHiem.color = vatPham.LayMauTheoDoHiem();
            }

            // Cập nhật icon
            if (imageIcon != null)
            {
                if (vatPham.Icon != null)
                {
                    imageIcon.sprite = vatPham.Icon;
                    imageIcon.gameObject.SetActive(true);
                }
                else
                {
                    imageIcon.gameObject.SetActive(false);
                }
            }

            // Cập nhật border theo độ hiếm
            if (imageBorder != null)
            {
                imageBorder.color = vatPham.LayMauTheoDoHiem();
            }

            // Cập nhật trạng thái buttons
            CapNhatTrangThaiButtons();

            // Cập nhật tags (nếu có)
            CapNhatTags();
        }

        /// <summary>
        /// Mua vật phẩm
        /// </summary>
        public void MuaVatPham()
        {
            if (vatPham == null || ShopManager.Instance == null) return;

            bool thanhCong = ShopManager.Instance.MuaVatPham(vatPham, soLuongHienTai);

            if (thanhCong && hienThiLog)
            {
                Debug.Log($"Đã mua {soLuongHienTai}x {vatPham.TenVatPham}");
            }

            // Cập nhật lại trạng thái buttons
            CapNhatTrangThaiButtons();
        }

        /// <summary>
        /// Bán vật phẩm
        /// </summary>
        public void BanVatPham()
        {
            if (vatPham == null || ShopManager.Instance == null) return;

            bool thanhCong = ShopManager.Instance.BanVatPham(vatPham, soLuongHienTai);

            if (thanhCong && hienThiLog)
            {
                Debug.Log($"Đã bán {soLuongHienTai}x {vatPham.TenVatPham}");
            }

            // Cập nhật lại trạng thái buttons
            CapNhatTrangThaiButtons();
        }

        /// <summary>
        /// Hiển thị thông tin chi tiết
        /// </summary>
        public void HienThiThongTinChiTiet()
        {
            if (vatPham == null) return;

            string thongTin = $"=== {vatPham.TenVatPham} ===\n" +
                             $"Mô tả: {vatPham.MoTa}\n" +
                             $"Loại: {vatPham.LayTenLoaiVatPham()}\n" +
                             $"Độ hiếm: {vatPham.LayTenDoHiem()}\n" +
                             $"Giá mua: {vatPham.GiaMua:N0} Lea\n" +
                             $"Giá bán: {vatPham.GiaBan:N0} Lea\n" +
                             $"Có thể bán: {(vatPham.CoTheBan ? "Có" : "Không")}\n" +
                             $"Số lượng tối đa: {vatPham.SoLuongToiDa}";

            Debug.Log(thongTin);

            // Có thể mở popup UI ở đây
            // PopupManager.Instance?.HienThiPopup(vatPham.TenVatPham, thongTin);
        }

        /// <summary>
        /// Tăng số lượng
        /// </summary>
        public void TangSoLuong()
        {
            if (soLuongHienTai < soLuongToiDa)
            {
                soLuongHienTai++;
                CapNhatInputSoLuong();
                CapNhatTrangThaiButtons();
            }
        }

        /// <summary>
        /// Giảm số lượng
        /// </summary>
        public void GiamSoLuong()
        {
            if (soLuongHienTai > 1)
            {
                soLuongHienTai--;
                CapNhatInputSoLuong();
                CapNhatTrangThaiButtons();
            }
        }

        /// <summary>
        /// Đặt số lượng
        /// </summary>
        /// <param name="soLuong">Số lượng mới</param>
        public void DatSoLuong(int soLuong)
        {
            soLuongHienTai = Mathf.Clamp(soLuong, 1, soLuongToiDa);
            CapNhatInputSoLuong();
            CapNhatTrangThaiButtons();
        }
        #endregion

        #region Private Methods
        private void OnSoLuongThayDoi(string giaTriMoi)
        {
            if (int.TryParse(giaTriMoi, out int soLuong))
            {
                soLuongHienTai = Mathf.Clamp(soLuong, 1, soLuongToiDa);
                CapNhatTrangThaiButtons();
            }
            else
            {
                // Nếu input không hợp lệ, reset về giá trị hiện tại
                CapNhatInputSoLuong();
            }
        }

        private void CapNhatInputSoLuong()
        {
            if (inputSoLuong != null)
            {
                inputSoLuong.text = soLuongHienTai.ToString();
            }
        }

        private void CapNhatTrangThaiButtons()
        {
            if (vatPham == null) return;

            // Button mua
            if (buttonMua != null)
            {
                bool coTheMua = ShopManager.Instance?.CoTheMuaVatPham(vatPham, soLuongHienTai) ?? false;
                buttonMua.interactable = coTheMua;
            }

            // Button bán
            if (buttonBan != null)
            {
                bool coTheBan = ShopManager.Instance?.CoTheBanVatPham(vatPham, soLuongHienTai) ?? false;
                buttonBan.interactable = coTheBan && vatPham.CoTheBan;
            }

            // Buttons số lượng
            if (buttonTangSoLuong != null)
            {
                buttonTangSoLuong.interactable = soLuongHienTai < soLuongToiDa;
            }

            if (buttonGiamSoLuong != null)
            {
                buttonGiamSoLuong.interactable = soLuongHienTai > 1;
            }
        }

        private void CapNhatTags()
        {
            // Cập nhật tag vật phẩm mới (cần thông tin từ ShopItemData)
            if (tagVatPhamMoi != null)
            {
                // Tạm thời ẩn, cần tích hợp với ShopItemData
                tagVatPhamMoi.SetActive(false);
            }

            // Cập nhật tag vật phẩm nổi bật
            if (tagVatPhamNoiBat != null)
            {
                // Tạm thời ẩn, cần tích hợp với ShopItemData
                tagVatPhamNoiBat.SetActive(false);
            }
        }

        private int TinhTongTienMua()
        {
            return vatPham != null ? vatPham.GiaMua * soLuongHienTai : 0;
        }

        private int TinhTongTienBan()
        {
            return vatPham != null ? vatPham.GiaBan * soLuongHienTai : 0;
        }
        #endregion

        #region Properties
        /// <summary>
        /// Vật phẩm hiện tại
        /// </summary>
        public Item VatPham => vatPham;

        /// <summary>
        /// Số lượng hiện tại
        /// </summary>
        public int SoLuongHienTai => soLuongHienTai;

        /// <summary>
        /// Tổng tiền mua
        /// </summary>
        public int TongTienMua => TinhTongTienMua();

        /// <summary>
        /// Tổng tiền bán
        /// </summary>
        public int TongTienBan => TinhTongTienBan();
        #endregion
    }
}
