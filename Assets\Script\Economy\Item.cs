using UnityEngine;

namespace EconomySystem
{
    /// <summary>
    /// Enum định nghĩa các loại vật phẩm trong game
    /// </summary>
    public enum ItemType
    {
        VatLieu,    // Vật liệ<PERSON>,     // <PERSON><PERSON><PERSON> c<PERSON>,      // <PERSON><PERSON>hí
        <PERSON>,   // <PERSON><PERSON><PERSON><PERSON>,    // Tiêu hao
        <PERSON>,     // Quý gi<PERSON>        // Khác
    }

    /// <summary>
    /// Enum định nghĩa độ hiếm của vật phẩm
    /// </summary>
    public enum ItemRarity
    {
        <PERSON>hu<PERSON>Thuong,   // Thường thường (Trắng)
        Khong<PERSON>,    // <PERSON>hông thường (Xanh lá)
        HiemCo,         // Hiếm có (Xanh dương)
        CucHiem,        // C<PERSON><PERSON> hiếm (Tím)
        ThanThoai       // Thần thoại (Vàng)
    }

    /// <summary>
    /// Class định nghĩa một vật phẩm trong hệ thống Economy
    /// </summary>
    [System.Serializable]
    public class Item
    {
        [Header("Thông Tin Cơ Bản")]
        [SerializeField] private string id;
        [SerializeField] private string tenVatPham;
        [SerializeField] [TextArea(2, 4)] private string moTa;
        [SerializeField] private Sprite icon;

        [Header("Giá Cả")]
        [SerializeField] private int giaMua = 10;
        [SerializeField] private int giaBan = 5;
        [SerializeField] private bool coTheBan = true;

        [Header("Phân Loại")]
        [SerializeField] private ItemType loaiVatPham = ItemType.VatLieu;
        [SerializeField] private ItemRarity doHiem = ItemRarity.ThuongThuong;

        [Header("Giới Hạn")]
        [SerializeField] private int soLuongToiDa = 99;

        // Properties
        public string ID => id;
        public string TenVatPham => tenVatPham;
        public string MoTa => moTa;
        public Sprite Icon => icon;
        public int GiaMua => giaMua;
        public int GiaBan => giaBan;
        public bool CoTheBan => coTheBan;
        public ItemType LoaiVatPham => loaiVatPham;
        public ItemRarity DoHiem => doHiem;
        public int SoLuongToiDa => soLuongToiDa;

        /// <summary>
        /// Constructor mặc định
        /// </summary>
        public Item()
        {
            id = System.Guid.NewGuid().ToString();
            tenVatPham = "Vật phẩm mới";
            moTa = "Mô tả vật phẩm";
            giaMua = 10;
            giaBan = 5;
            coTheBan = true;
            loaiVatPham = ItemType.VatLieu;
            doHiem = ItemRarity.ThuongThuong;
            soLuongToiDa = 99;
        }

        /// <summary>
        /// Constructor với tham số
        /// </summary>
        public Item(string id, string ten, string moTa, int giaMua, int giaBan)
        {
            this.id = id;
            this.tenVatPham = ten;
            this.moTa = moTa;
            this.giaMua = giaMua;
            this.giaBan = giaBan;
            this.coTheBan = true;
            this.loaiVatPham = ItemType.VatLieu;
            this.doHiem = ItemRarity.ThuongThuong;
            this.soLuongToiDa = 99;
        }

        /// <summary>
        /// Lấy màu sắc theo độ hiếm
        /// </summary>
        public Color LayMauTheoDoHiem()
        {
            switch (doHiem)
            {
                case ItemRarity.ThuongThuong: return Color.white;
                case ItemRarity.KhongThuong: return Color.green;
                case ItemRarity.HiemCo: return Color.blue;
                case ItemRarity.CucHiem: return Color.magenta;
                case ItemRarity.ThanThoai: return Color.yellow;
                default: return Color.white;
            }
        }

        /// <summary>
        /// Lấy tên độ hiếm bằng tiếng Việt
        /// </summary>
        public string LayTenDoHiem()
        {
            switch (doHiem)
            {
                case ItemRarity.ThuongThuong: return "Thường Thường";
                case ItemRarity.KhongThuong: return "Không Thường";
                case ItemRarity.HiemCo: return "Hiếm Có";
                case ItemRarity.CucHiem: return "Cực Hiếm";
                case ItemRarity.ThanThoai: return "Thần Thoại";
                default: return "Không Xác Định";
            }
        }

        /// <summary>
        /// Lấy tên loại vật phẩm bằng tiếng Việt
        /// </summary>
        public string LayTenLoaiVatPham()
        {
            switch (loaiVatPham)
            {
                case ItemType.VatLieu: return "Vật Liệu";
                case ItemType.CongCu: return "Công Cụ";
                case ItemType.VuKhi: return "Vũ Khí";
                case ItemType.GiapGiap: return "Giáp";
                case ItemType.TieuHao: return "Tiêu Hao";
                case ItemType.QuyGia: return "Quý Giá";
                case ItemType.Khac: return "Khác";
                default: return "Không Xác Định";
            }
        }

        /// <summary>
        /// Kiểm tra tính hợp lệ của vật phẩm
        /// </summary>
        public bool KiemTraHopLe()
        {
            return !string.IsNullOrEmpty(id) && 
                   !string.IsNullOrEmpty(tenVatPham) && 
                   giaMua >= 0 && 
                   giaBan >= 0 && 
                   soLuongToiDa > 0;
        }

        /// <summary>
        /// Tạo bản sao của vật phẩm
        /// </summary>
        public Item TaoBanSao()
        {
            Item banSao = new Item();
            banSao.id = this.id;
            banSao.tenVatPham = this.tenVatPham;
            banSao.moTa = this.moTa;
            banSao.icon = this.icon;
            banSao.giaMua = this.giaMua;
            banSao.giaBan = this.giaBan;
            banSao.coTheBan = this.coTheBan;
            banSao.loaiVatPham = this.loaiVatPham;
            banSao.doHiem = this.doHiem;
            banSao.soLuongToiDa = this.soLuongToiDa;
            return banSao;
        }

        public override string ToString()
        {
            return $"{tenVatPham} (ID: {id}, Giá: {giaMua} Lea)";
        }
    }

    /// <summary>
    /// Class chứa thông tin vật phẩm trong inventory với số lượng
    /// </summary>
    [System.Serializable]
    public class InventoryItem
    {
        [SerializeField] private Item item;
        [SerializeField] private int soLuong;

        public Item Item => item;
        public int SoLuong 
        { 
            get => soLuong; 
            set => soLuong = Mathf.Clamp(value, 0, item?.SoLuongToiDa ?? 99); 
        }

        public InventoryItem(Item item, int soLuong)
        {
            this.item = item;
            this.SoLuong = soLuong;
        }

        /// <summary>
        /// Kiểm tra có thể thêm số lượng không
        /// </summary>
        public bool CoTheThemSoLuong(int soLuongThem)
        {
            return soLuong + soLuongThem <= item.SoLuongToiDa;
        }

        /// <summary>
        /// Thêm số lượng và trả về số lượng thực tế đã thêm
        /// </summary>
        public int ThemSoLuong(int soLuongThem)
        {
            int soLuongCu = soLuong;
            SoLuong += soLuongThem;
            return soLuong - soLuongCu;
        }

        /// <summary>
        /// Trừ số lượng và trả về số lượng thực tế đã trừ
        /// </summary>
        public int TruSoLuong(int soLuongTru)
        {
            int soLuongCu = soLuong;
            SoLuong -= soLuongTru;
            return soLuongCu - soLuong;
        }

        public override string ToString()
        {
            return $"{item.TenVatPham} x{soLuong}";
        }
    }
}
