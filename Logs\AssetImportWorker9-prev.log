Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker9
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker9.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [17168]  Target information:

Player connection [17168]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1124540465 [EditorId] 1124540465 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [17168] Host joined multi-casting on [***********:54997]...
Player connection [17168] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56384
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002669 seconds.
- Loaded All Assemblies, in  1.185 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.963 seconds
Domain Reload Profiling: 2148ms
	BeginReloadAssembly (476ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (4ms)
	RebuildCommonClasses (115ms)
	RebuildNativeTypeToScriptingClass (39ms)
	initialDomainReloadingComplete (105ms)
	LoadAllAssembliesAndSetupDomain (449ms)
		LoadAssemblies (470ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (442ms)
			TypeCache.Refresh (440ms)
				TypeCache.ScanAssembly (410ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (964ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (862ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (53ms)
			SetLoadedEditorAssemblies (21ms)
			BeforeProcessingInitializeOnLoad (278ms)
			ProcessInitializeOnLoadAttributes (410ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.290 seconds
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.712 seconds
Domain Reload Profiling: 4001ms
	BeginReloadAssembly (376ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (89ms)
	LoadAllAssembliesAndSetupDomain (1732ms)
		LoadAssemblies (1054ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (848ms)
			TypeCache.Refresh (724ms)
				TypeCache.ScanAssembly (689ms)
			BuildScriptInfoCaches (102ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1713ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1303ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (376ms)
			ProcessInitializeOnLoadAttributes (784ms)
			ProcessInitializeOnLoadMethodAttributes (131ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 17.00 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (10.6 MB). Loaded Objects now: 7294.
Memory consumption went from 161.5 MB to 150.8 MB.
Total: 98.520200 ms (FindLiveObjects: 2.019400 ms CreateObjectMapping: 2.583000 ms MarkObjects: 10.486900 ms  DeleteObjects: 83.427900 ms)

========================================================================
Received Import Request.
  Time since last request: 103331.444888 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Red.prefab
  artifactKey: Guid(8140d14ca68b665428789770f9994865) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Red.prefab using Guid(8140d14ca68b665428789770f9994865) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5ed7e7f456bb96bd00a94d993137cf00') in 0.8407611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cup_B.prefab
  artifactKey: Guid(8775ca8174311be46876c299f133b301) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cup_B.prefab using Guid(8775ca8174311be46876c299f133b301) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9a0e3d436c08c24312f4aa61462cafb4') in 0.0263477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_D.prefab
  artifactKey: Guid(114f56ec46fc7f048a208f0a2a151fe3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_D.prefab using Guid(114f56ec46fc7f048a208f0a2a151fe3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b45833f82d2398878f12389acaf77754') in 0.0229184 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Green.prefab
  artifactKey: Guid(4e4609e3409ee2343b6e3a14b246a014) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Green.prefab using Guid(4e4609e3409ee2343b6e3a14b246a014) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '295945be610e3b692357806ae4504d39') in 0.1568618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Catapult.prefab
  artifactKey: Guid(60da76fc055e19040b629a03ae940e6d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Catapult.prefab using Guid(60da76fc055e19040b629a03ae940e6d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '39734fa8a42604638ab6f6e090f6cb89') in 0.0211679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Script/Player/Guides/HUONG_DAN_TIENG_VIET.md
  artifactKey: Guid(893267ab14f866041933bf7a5281e607) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/HUONG_DAN_TIENG_VIET.md using Guid(893267ab14f866041933bf7a5281e607) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aff024488377da6dcd3aa22c96514701') in 0.0341182 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_C.prefab
  artifactKey: Guid(0fee65b29124ffd439f062a49fafa7e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_C.prefab using Guid(0fee65b29124ffd439f062a49fafa7e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b8471be0095b59fa830793c8dc94b73d') in 0.0431903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_F.fbx
  artifactKey: Guid(772de00000994e44a8774887d3dcece6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_F.fbx using Guid(772de00000994e44a8774887d3dcece6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3081ace07612a732072175761bc0d731') in 0.1120572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Brown_B.fbx
  artifactKey: Guid(22fa89c0f66f3b94fa3154b80e9efecf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Brown_B.fbx using Guid(22fa89c0f66f3b94fa3154b80e9efecf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e717841c2173fa4db0682864af9d5f9c') in 0.0358231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_H.prefab
  artifactKey: Guid(14166236bef07bc438774648e556a90a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_H.prefab using Guid(14166236bef07bc438774648e556a90a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2997672265de0ec54c80a18b164b4549') in 0.0225786 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BatteringRam.prefab
  artifactKey: Guid(9fbec1d124250b842a0459b626fcbc31) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BatteringRam.prefab using Guid(9fbec1d124250b842a0459b626fcbc31) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7dc9a0923984fe8f931dc46f933cee7b') in 0.0237158 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_Beige.prefab
  artifactKey: Guid(fb9a32debb833004594bfdd1e230881d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_Beige.prefab using Guid(fb9a32debb833004594bfdd1e230881d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd0d8b7739b23bdf43965ca514fa4ffb7') in 0.0312981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_E.prefab
  artifactKey: Guid(c26fcd64f2447c348a6fedc42010b8b0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_E.prefab using Guid(c26fcd64f2447c348a6fedc42010b8b0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '197419f9daa6cb082e578d2e9512bb6c') in 0.0308361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_C.prefab
  artifactKey: Guid(3b42da1841446314b98ed56397abe1af) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_C.prefab using Guid(3b42da1841446314b98ed56397abe1af) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '970df22997224cd51b4e948b42136c2c') in 0.0243573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_A.prefab
  artifactKey: Guid(23945059dcc2918449ccdc9741e0f4b9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_A.prefab using Guid(23945059dcc2918449ccdc9741e0f4b9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '679908003cc8fe0a070a714103819a02') in 0.0227926 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Yellow.prefab
  artifactKey: Guid(4c63f087cac89f743a1ededaf44bc7ca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Yellow.prefab using Guid(4c63f087cac89f743a1ededaf44bc7ca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2675b42c30b726bece16dab233e6dc08') in 0.0376409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_C.fbx
  artifactKey: Guid(dcc94b0d5e191704aa6964b69c450deb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_C.fbx using Guid(dcc94b0d5e191704aa6964b69c450deb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17563c6a9122c9af903bb64c5805df1a') in 0.0420055 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_B.prefab
  artifactKey: Guid(0b9ea1e0d4308df429857cf58e6a6ec3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_B.prefab using Guid(0b9ea1e0d4308df429857cf58e6a6ec3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'defc4a928f99465a55254a4424a6ebe4') in 0.026557 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_D.prefab
  artifactKey: Guid(42b80b98eb55ed442a5961ff219969da) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_D.prefab using Guid(42b80b98eb55ed442a5961ff219969da) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5661135f272610a35151aab4650c6790') in 0.0285556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_C.prefab
  artifactKey: Guid(75088b9a3503cb242aa439290c54b3fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_C.prefab using Guid(75088b9a3503cb242aa439290c54b3fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d82e37eb77472db327a06472d47fc9e') in 0.0255819 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_E.prefab
  artifactKey: Guid(60d1f2dd17728a14cb732d65fde272b1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_E.prefab using Guid(60d1f2dd17728a14cb732d65fde272b1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bfb028e4b52ba4b4c6e31ec3b86838d5') in 0.0265678 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_C.prefab
  artifactKey: Guid(3108a1d0d725bf94a8b98efb4a9c57e1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_C.prefab using Guid(3108a1d0d725bf94a8b98efb4a9c57e1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa6b20b632486be350bd9d3c153fa76b') in 0.0282107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bowl_Green.fbx
  artifactKey: Guid(f463d143ed037a246bf17e5466fb714a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bowl_Green.fbx using Guid(f463d143ed037a246bf17e5466fb714a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b4055a415533198e647dea25b6b5ae4a') in 0.0354974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ink.prefab
  artifactKey: Guid(56fd174b834921940b8aec1b03c37d13) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ink.prefab using Guid(56fd174b834921940b8aec1b03c37d13) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8cd46552e38db5123bad7f038fb88cb0') in 0.0227017 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_I.prefab
  artifactKey: Guid(75d9c3bad581ad54e86670c606cb1a05) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_I.prefab using Guid(75d9c3bad581ad54e86670c606cb1a05) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c3789b5dc50669046ecbfbcdda248f8d') in 0.0228042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_M.fbx
  artifactKey: Guid(2ab0c00816080dc4ebc18c1d5aba35a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_M.fbx using Guid(2ab0c00816080dc4ebc18c1d5aba35a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '42d85f438186d2b71608066748e7c773') in 0.0419668 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_I.prefab
  artifactKey: Guid(4b45e77a8ad15c44b8bab5ba68f0a64d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_I.prefab using Guid(4b45e77a8ad15c44b8bab5ba68f0a64d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1038675e9bfb6db9ae77c5ccd8ea87a9') in 0.0281276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_L.prefab
  artifactKey: Guid(503bf02efad4cbf43beeda2249ccbae0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_L.prefab using Guid(503bf02efad4cbf43beeda2249ccbae0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '65372c7f1f27f5fcfc6ce5261aaeabad') in 0.0266855 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000189 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_A.prefab
  artifactKey: Guid(bffc626bb005fbd4789e4fbba4bc4900) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_A.prefab using Guid(bffc626bb005fbd4789e4fbba4bc4900) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0876b2b6e4074f7bde6caa0d9973e964') in 0.0286989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Yellow.prefab
  artifactKey: Guid(48d004cb989fb60499597c1fc04c5f47) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Yellow.prefab using Guid(48d004cb989fb60499597c1fc04c5f47) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e55ad658630c51bcc948fe4e861a3a6e') in 0.0292046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Blue.prefab
  artifactKey: Guid(f899e296387d0ff439a609f79d1d1fd5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Blue.prefab using Guid(f899e296387d0ff439a609f79d1d1fd5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6ad91dd67590815e10ac5fb14da4c409') in 0.0305552 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bench_F.fbx
  artifactKey: Guid(e1fe6a1cf3f4a564bae1049862468fb2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bench_F.fbx using Guid(e1fe6a1cf3f4a564bae1049862468fb2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac5468ae4184a3789741692b4e9c7305') in 0.041657 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_F.prefab
  artifactKey: Guid(2baf982870f80d1459180344178b8200) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_F.prefab using Guid(2baf982870f80d1459180344178b8200) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ed66f0416889128ac447b8ebe86ddd75') in 0.0204069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_A.prefab
  artifactKey: Guid(9c710ca8038128a48a7ca2572cf21ed3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_A.prefab using Guid(9c710ca8038128a48a7ca2572cf21ed3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '840c2fb78de43fb73b836f0a9973df97') in 0.0319346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_D.prefab
  artifactKey: Guid(43f613e6669932247a149afc0f336f2b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_D.prefab using Guid(43f613e6669932247a149afc0f336f2b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '605353292204ac9d412242773055f2b8') in 0.0346597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_H.prefab
  artifactKey: Guid(fbda5d97dae8649408b547b269d97700) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_H.prefab using Guid(fbda5d97dae8649408b547b269d97700) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c8ec4110be7cac206ed57d090f21a5b6') in 0.0343443 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_E.prefab
  artifactKey: Guid(ae0792bccdea00b459bc2d5232358d37) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_E.prefab using Guid(ae0792bccdea00b459bc2d5232358d37) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ea844ffafdc8210ab181f3f114188d24') in 0.0278928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_A.prefab
  artifactKey: Guid(e2ee60e2dad25c94f86aa4f998e2a4d4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_A.prefab using Guid(e2ee60e2dad25c94f86aa4f998e2a4d4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bb9cbb01c3eef753018518cfcbf9c1a6') in 0.0258341 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000098 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_F.prefab
  artifactKey: Guid(3ce40b7f85353da4d84da50026f013a6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_F.prefab using Guid(3ce40b7f85353da4d84da50026f013a6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b73544ad0ef55902b471cd17b93dfc0a') in 0.0314776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_A.prefab
  artifactKey: Guid(66af56f5ac1a0c44393d61bf32c91005) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_A.prefab using Guid(66af56f5ac1a0c44393d61bf32c91005) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2d224fb72c960a726f781366c5b4d9c3') in 0.0223557 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_D.prefab
  artifactKey: Guid(108b2949af576544cbf443cfbb79f25d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_D.prefab using Guid(108b2949af576544cbf443cfbb79f25d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4656e5b54c06aff34f951f40fc24f145') in 0.0322507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_S.fbx
  artifactKey: Guid(c9d3905a559de8c42a46dfe8edd6df19) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_S.fbx using Guid(c9d3905a559de8c42a46dfe8edd6df19) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '064a4ff5169689268817bb962a7dfae3') in 0.0495425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_F.prefab
  artifactKey: Guid(dbf0783442bfd6e4590b669c0e5e5778) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_F.prefab using Guid(dbf0783442bfd6e4590b669c0e5e5778) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0f873f5ae867005275f28f77f7218a48') in 0.0336251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_D.prefab
  artifactKey: Guid(076974b3e7208204282f49426c616193) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_D.prefab using Guid(076974b3e7208204282f49426c616193) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '48af60481e0fbcf381d73f3eeaee4e32') in 0.0276735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_K.fbx
  artifactKey: Guid(ee04430c0d5d64d488338f07576f0979) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_K.fbx using Guid(ee04430c0d5d64d488338f07576f0979) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ca5bc90dede67098905d0717c94d84c2') in 0.0474825 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Plane.prefab
  artifactKey: Guid(a7ec83fb0a621754facfd7342af4926f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Plane.prefab using Guid(a7ec83fb0a621754facfd7342af4926f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1464dc623dc7dab74f461d4e2daa199a') in 0.053938 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_A.prefab
  artifactKey: Guid(274833ec1e44e964caa1cdd7aa8f7f6f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_A.prefab using Guid(274833ec1e44e964caa1cdd7aa8f7f6f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8d83cd1ea235d454e98c691539a33ca2') in 0.0307016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_C.fbx
  artifactKey: Guid(c2ca179a6fc447b4a9403ed7163ba1cd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_C.fbx using Guid(c2ca179a6fc447b4a9403ed7163ba1cd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1e41996e0cd8e8fb045e3b730a4618c9') in 0.0370255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tree_F.fbx
  artifactKey: Guid(d76227d3e7a25704a90b4209f0f2f718) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tree_F.fbx using Guid(d76227d3e7a25704a90b4209f0f2f718) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5012a8ae723065cc318423e23052a60') in 0.0349169 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_J.fbx
  artifactKey: Guid(e8b26aba6b7c19546adce902fa07838a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_J.fbx using Guid(e8b26aba6b7c19546adce902fa07838a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7cce4f7b96f0f63782270346d71ed818') in 0.0422076 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000104 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_B.prefab
  artifactKey: Guid(9d89a73f46939a04e94c7e39cea0a222) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_B.prefab using Guid(9d89a73f46939a04e94c7e39cea0a222) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bab4a1b97694616d484000af51cf7ff8') in 0.0311938 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_D.prefab
  artifactKey: Guid(8655ef55f26834e46bd9012739204433) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_D.prefab using Guid(8655ef55f26834e46bd9012739204433) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7683ed0609ef478f825aaa32d052539b') in 0.0939307 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_F.prefab
  artifactKey: Guid(3f03c31eb0bf9b34498ca4c8180fd0c7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_F.prefab using Guid(3f03c31eb0bf9b34498ca4c8180fd0c7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e38294ea63ed198b90acf20821ca111a') in 0.0250596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_B.prefab
  artifactKey: Guid(3158c86f78c137f4893285695fb1e14f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_B.prefab using Guid(3158c86f78c137f4893285695fb1e14f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3248e08045ea862ed7316959c62da262') in 0.035314 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Banana_Yellow.prefab
  artifactKey: Guid(9b3ff622e7d227f46983c68cdc2af006) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Banana_Yellow.prefab using Guid(9b3ff622e7d227f46983c68cdc2af006) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '31345aafda4109c430ea6da93f8bf968') in 0.0393929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000115 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_I.prefab
  artifactKey: Guid(c107862298ce55f49b5a42cc0ec95c40) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_I.prefab using Guid(c107862298ce55f49b5a42cc0ec95c40) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fbef9cf5ed9434a9063c1ecac9a42835') in 0.0275667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Mountain_D.fbx
  artifactKey: Guid(70ef4a2f52dc3a94fb677adfc4b16788) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Mountain_D.fbx using Guid(70ef4a2f52dc3a94fb677adfc4b16788) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '63eafb287871375fa760dc3ea0be94ae') in 0.0372161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_E.prefab
  artifactKey: Guid(eb50bee0027e4de4892b4bb47dbb9d55) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_E.prefab using Guid(eb50bee0027e4de4892b4bb47dbb9d55) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c74f47a0ef56e84fe363a84d94e78577') in 0.0197763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_H.prefab
  artifactKey: Guid(3fe001d29ca95a348bbd6f6abd426b49) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_H.prefab using Guid(3fe001d29ca95a348bbd6f6abd426b49) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e9ba70945223bb89d68396747e6d268c') in 0.0308319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_C.prefab
  artifactKey: Guid(891a0a190bd865c4a83ea89744539994) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_C.prefab using Guid(891a0a190bd865c4a83ea89744539994) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '897922ea04ca52a65edc21659c460c19') in 0.0241521 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_B.fbx
  artifactKey: Guid(e2a3148dd8d4e2c49bde944b080df581) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_B.fbx using Guid(e2a3148dd8d4e2c49bde944b080df581) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0792959b0189c4512f6337839d0b8cb3') in 0.039079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_D.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_D.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c095408028cc13daecf3b2bd06f587c') in 0.0428438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_D.fbx
  artifactKey: Guid(190d27b5a9b7da345b74fa44b23b9503) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_D.fbx using Guid(190d27b5a9b7da345b74fa44b23b9503) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ca6ab6b6baf0c1fae6a0aeacf085f62') in 0.0420318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_C.fbx
  artifactKey: Guid(2de34faa8e26c2e4c862238ed09b439f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_C.fbx using Guid(2de34faa8e26c2e4c862238ed09b439f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1322d7aafa11f7ae976f58acb25256fa') in 0.0332655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BowlMedicine_B.fbx
  artifactKey: Guid(f7aaa95ddf38505418d4b779ba45f90c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BowlMedicine_B.fbx using Guid(f7aaa95ddf38505418d4b779ba45f90c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a5bfb5b72cdb9398ce81aeac90b6a91') in 0.0468735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_D.prefab
  artifactKey: Guid(916b190bbc9ce1542b8201779ceb4ed8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_D.prefab using Guid(916b190bbc9ce1542b8201779ceb4ed8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7369ab45529e986fc69b33ff940957d1') in 0.0248612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Onion_Yellow.prefab
  artifactKey: Guid(19006d0d6e3988c48ba339539f26a47a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Onion_Yellow.prefab using Guid(19006d0d6e3988c48ba339539f26a47a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a04d527f0465ba9a1dbd0cee19b20bfb') in 0.0273749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_I.fbx
  artifactKey: Guid(1c8a16b50acab6b4da5fa3ea26e5b738) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_I.fbx using Guid(1c8a16b50acab6b4da5fa3ea26e5b738) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '866e9518ed0df8748bceb35e54b3d4eb') in 0.040892 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_G.prefab
  artifactKey: Guid(924c7f8dd6a40df49a1c66d31992b179) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_G.prefab using Guid(924c7f8dd6a40df49a1c66d31992b179) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4a6c3e88b332871d95912175ce35d0df') in 0.0283135 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_A.fbx
  artifactKey: Guid(edf6c2a2b0eb2fe4da9d4ffbf77ffda2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_A.fbx using Guid(edf6c2a2b0eb2fe4da9d4ffbf77ffda2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57f176d2bba28656c29282e2e2f81c02') in 0.0556714 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_I.prefab
  artifactKey: Guid(b78347208df254f40bccbe9ff001671d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_I.prefab using Guid(b78347208df254f40bccbe9ff001671d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff1350d98c22b5bcefb3bef6e44eee71') in 0.0266187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Carrier_Wood.prefab
  artifactKey: Guid(561cd7eb1fa3eb54793ed3cc5b9ef5b2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Carrier_Wood.prefab using Guid(561cd7eb1fa3eb54793ed3cc5b9ef5b2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '819725f984ef7c257a43fa8adf7f9dfa') in 0.0283346 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_E.fbx
  artifactKey: Guid(9839900b1c12fb544af3c18423e053ca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_E.fbx using Guid(9839900b1c12fb544af3c18423e053ca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e132528382bc506cb65dcba755b33525') in 0.0341773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_G.prefab
  artifactKey: Guid(7c553f4edf2df5e43b6aa40532fe9dc7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_G.prefab using Guid(7c553f4edf2df5e43b6aa40532fe9dc7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3c50a01269fd974f27897bfcb9296b83') in 0.0291546 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_G.fbx
  artifactKey: Guid(51905c49f2526734bafeee16dadbc6c0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_G.fbx using Guid(51905c49f2526734bafeee16dadbc6c0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '145af83757d7fb41de69233a6b82b9aa') in 0.0499293 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass.prefab
  artifactKey: Guid(83154d7c8aa64c3428059e75ea272d27) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass.prefab using Guid(83154d7c8aa64c3428059e75ea272d27) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1946ef860284f6ff4aa28b8fa5244550') in 0.0281173 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_F.fbx
  artifactKey: Guid(acbeb17491db8de4085aa1f568822af2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_F.fbx using Guid(acbeb17491db8de4085aa1f568822af2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5b8fbaf221e7aff606cace51524c5f2') in 0.0396962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_B.fbx
  artifactKey: Guid(1ef4dfd2fd35fc341aa010922153ecc4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_B.fbx using Guid(1ef4dfd2fd35fc341aa010922153ecc4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '16a651b1f9fba2cb940e570cec291385') in 0.0371851 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Stable_A.fbx
  artifactKey: Guid(578c25a4ebeb9fd41b9f7d23be02de76) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Stable_A.fbx using Guid(578c25a4ebeb9fd41b9f7d23be02de76) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6772095b45da7dfe3aadd0685104e9fa') in 0.0358005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Table_A.fbx
  artifactKey: Guid(14f59c6adfe32a54296d18e4deda4cc1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Table_A.fbx using Guid(14f59c6adfe32a54296d18e4deda4cc1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cd6dea31573bdb958a9dc1a37ca5e062') in 0.0409439 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_C.prefab
  artifactKey: Guid(120eada2927331f4991061bbdc16b876) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_C.prefab using Guid(120eada2927331f4991061bbdc16b876) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a1370d135c995c6df97b04e8727edc97') in 0.0243097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Water_C.fbx
  artifactKey: Guid(17b596f65ce66324885b58efa98aa93a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Water_C.fbx using Guid(17b596f65ce66324885b58efa98aa93a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8f6d44b236c8386b57a3b6751137fab') in 0.0360932 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_L.fbx
  artifactKey: Guid(b15f1092ff0e27148aa7c124251f1918) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_L.fbx using Guid(b15f1092ff0e27148aa7c124251f1918) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e7c2d5cb72ec28e4d5c6b27d571009a8') in 0.0335259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_C.prefab
  artifactKey: Guid(69fe52486d86d114cbd5d7e43cc480aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_C.prefab using Guid(69fe52486d86d114cbd5d7e43cc480aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5a60f9ff3714b5e874478c78b94e7e2') in 0.0295586 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_F.prefab
  artifactKey: Guid(6d888d1b7f74c9a44acb3604c1830aeb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_F.prefab using Guid(6d888d1b7f74c9a44acb3604c1830aeb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8a55f94f4e567b39119dfc5891f0ba68') in 0.0334114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Sword_A.fbx
  artifactKey: Guid(b95dd789be9ee2849ac725628bd1fbec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Sword_A.fbx using Guid(b95dd789be9ee2849ac725628bd1fbec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b434cc1c17522b92171afb48f17a9eae') in 0.0421643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_K.prefab
  artifactKey: Guid(f2dec7f6f47dbb546bbfec5126d5dfa0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_K.prefab using Guid(f2dec7f6f47dbb546bbfec5126d5dfa0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '722c3f609e0c5bb849cd750863ae3ee2') in 0.0289862 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Color.mat
  artifactKey: Guid(57c3db2e0def48446b765b48765610ca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Color.mat using Guid(57c3db2e0def48446b765b48765610ca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6788dae20a874ea67fbfcbcb804b6860') in 0.0575213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Carrot.fbx
  artifactKey: Guid(c471ad312598d3b4e85053b2b261c5b6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Carrot.fbx using Guid(c471ad312598d3b4e85053b2b261c5b6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '16c2018dda53e33f58af424c3042affb') in 0.0390829 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_Open_A.fbx
  artifactKey: Guid(06b3c6f327901084189263b744fdae1c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_Open_A.fbx using Guid(06b3c6f327901084189263b744fdae1c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '231f5f09a854a47f9b73b4b7b02fa118') in 0.0497405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_B.fbx
  artifactKey: Guid(316af432f4beb934d8eaec5c916b699b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_B.fbx using Guid(316af432f4beb934d8eaec5c916b699b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '53ae6655cc52cbb8308bf57ffdcd10ed') in 0.0438572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bush_B.fbx
  artifactKey: Guid(dc0345c9dccd53e479252bda163c171b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bush_B.fbx using Guid(dc0345c9dccd53e479252bda163c171b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'faae7a0a66260d34cc7aaffe5c5459e5') in 0.0427132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Open_C.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Open_C.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dba4ff4dd324ce3979df1c1cd80d71d0') in 0.0380677 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Rock_F.fbx
  artifactKey: Guid(15467f24a76c4774a9844547b7f1c631) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Rock_F.fbx using Guid(15467f24a76c4774a9844547b7f1c631) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e4f353ef52179862f2b5d4e6d9f1a925') in 0.0345418 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_A.prefab
  artifactKey: Guid(f5011b0dc69dbb74a88845ddb732641f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_A.prefab using Guid(f5011b0dc69dbb74a88845ddb732641f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0c568966cf1f2a976ae28f65748ddcf1') in 0.0429175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Blue.prefab
  artifactKey: Guid(f899e296387d0ff439a609f79d1d1fd5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Blue.prefab using Guid(f899e296387d0ff439a609f79d1d1fd5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a87dbe944aad2cc986369b5f7472cd2') in 0.032128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_A.prefab
  artifactKey: Guid(75a25c3532820674ba6e62f883baa293) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_A.prefab using Guid(75a25c3532820674ba6e62f883baa293) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc61f4a05de334072552a3478212d6ae') in 0.0414784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_A.prefab
  artifactKey: Guid(23a0bdd64b4369c4db2f1024b76f5494) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_A.prefab using Guid(23a0bdd64b4369c4db2f1024b76f5494) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d11f2d3650d43298929f847a7e22011') in 0.0275805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Emission.mat
  artifactKey: Guid(fb133731a320524458604654a88de236) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Emission.mat using Guid(fb133731a320524458604654a88de236) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a76d2c3ef4639ba8eb45e708a3422e5a') in 0.0506277 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_D.fbx
  artifactKey: Guid(988fe1a7ef3b59d47997bfa4298aa492) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_D.fbx using Guid(988fe1a7ef3b59d47997bfa4298aa492) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '541f924a25c6f0def154574e2e0a5405') in 0.0432212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_D.fbx
  artifactKey: Guid(d00f13612c9fe3a43a3ef646d0da8dc3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_D.fbx using Guid(d00f13612c9fe3a43a3ef646d0da8dc3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c88e1955fc0cc2ff531095cd7bd7458c') in 0.0343265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_E.fbx
  artifactKey: Guid(a69a0563c37344e459cab4153e6ff2b1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_E.fbx using Guid(a69a0563c37344e459cab4153e6ff2b1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '256fde1b6b1202fe33c85ca646ba55b2') in 0.0344839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Brown_C.fbx
  artifactKey: Guid(48dd439bb30c8c6439184dfcf594f6b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Brown_C.fbx using Guid(48dd439bb30c8c6439184dfcf594f6b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1845034d1c3a2124e07d94be38be2aef') in 0.054254 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_A.fbx
  artifactKey: Guid(324c9e4b539caf742b4818db7f68f34e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_A.fbx using Guid(324c9e4b539caf742b4818db7f68f34e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c76a4e43aa5be237c1565c85d370553') in 0.0654023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_A.fbx
  artifactKey: Guid(ed7f00249673392409b551a11828b468) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_A.fbx using Guid(ed7f00249673392409b551a11828b468) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a67599215650a60fb6055bad1178088') in 0.0397965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Yellow.fbx
  artifactKey: Guid(caa01b72cbca63947bba0837e4767dd0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Yellow.fbx using Guid(caa01b72cbca63947bba0837e4767dd0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '617ac7222bdfedb62a26dc7c7d786ec7') in 0.0372207 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_H.fbx
  artifactKey: Guid(c5d7696fb8370b84ba284a4b8a9263e1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_H.fbx using Guid(c5d7696fb8370b84ba284a4b8a9263e1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f00c6b2c100a359671332e573938be44') in 0.0432506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Yellow.prefab
  artifactKey: Guid(ba40d7629d4dcdd4387e11782121124c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Yellow.prefab using Guid(ba40d7629d4dcdd4387e11782121124c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8fa020d60798e490524fb42d83e4789b') in 0.0302834 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_B.prefab
  artifactKey: Guid(f1fa534b38dbf1f46b0a645ff3b92a3d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_B.prefab using Guid(f1fa534b38dbf1f46b0a645ff3b92a3d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7d8c4b82ab6e3798ff71690d9d0c37aa') in 0.0270105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_B.prefab
  artifactKey: Guid(8cded43a618401f4e88daa88e4929c71) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_B.prefab using Guid(8cded43a618401f4e88daa88e4929c71) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9ece599aaafe3c67a52d7f56bcdbf054') in 0.0229254 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Mountain_C.fbx
  artifactKey: Guid(99deb7e0e2474424eb57860dbccfb195) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Mountain_C.fbx using Guid(99deb7e0e2474424eb57860dbccfb195) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'edba6a1f7b7c5d199a189997096373af') in 0.0377627 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_B.fbx
  artifactKey: Guid(cea203c07eee66f4b972ad288209d2f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_B.fbx using Guid(cea203c07eee66f4b972ad288209d2f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e62dde7cddf358aba408cfc60350c0e') in 0.0365639 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0