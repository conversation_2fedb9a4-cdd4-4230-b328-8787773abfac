Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker10
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker10.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [18340]  Target information:

Player connection [18340]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 4251823516 [EditorId] 4251823516 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [18340] Host joined multi-casting on [***********:54997]...
Player connection [18340] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56584
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003728 seconds.
- Loaded All Assemblies, in  1.280 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.944 seconds
Domain Reload Profiling: 2224ms
	BeginReloadAssembly (560ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (112ms)
	RebuildNativeTypeToScriptingClass (35ms)
	initialDomainReloadingComplete (104ms)
	LoadAllAssembliesAndSetupDomain (468ms)
		LoadAssemblies (558ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (464ms)
			TypeCache.Refresh (462ms)
				TypeCache.ScanAssembly (430ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (945ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (860ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (43ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (298ms)
			ProcessInitializeOnLoadAttributes (406ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.266 seconds
Refreshing native plugins compatible for Editor in 2.15 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.659 seconds
Domain Reload Profiling: 3924ms
	BeginReloadAssembly (352ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (1763ms)
		LoadAssemblies (931ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1006ms)
			TypeCache.Refresh (853ms)
				TypeCache.ScanAssembly (806ms)
			BuildScriptInfoCaches (128ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1660ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1252ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (331ms)
			ProcessInitializeOnLoadAttributes (793ms)
			ProcessInitializeOnLoadMethodAttributes (116ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 1.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (7.7 MB). Loaded Objects now: 7294.
Memory consumption went from 163.3 MB to 155.7 MB.
Total: 24.529900 ms (FindLiveObjects: 2.270800 ms CreateObjectMapping: 2.651100 ms MarkObjects: 12.552200 ms  DeleteObjects: 7.052700 ms)

========================================================================
Received Import Request.
  Time since last request: 103330.507688 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Anvil_A.prefab
  artifactKey: Guid(e2b76348f3d1ee446bfa1ba69d789888) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Anvil_A.prefab using Guid(e2b76348f3d1ee446bfa1ba69d789888) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2942aff22630454738ea8d80dd8be774') in 1.3270014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Script/Player/PlayerMovementFixer.cs
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/PlayerMovementFixer.cs using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9c3f949604996adeb253c1f27bc255e4') in 0.127133 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_A.prefab
  artifactKey: Guid(25d5f42405738fb43947296a1e0ed7ad) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_A.prefab using Guid(25d5f42405738fb43947296a1e0ed7ad) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c87d99af342d774e1a165195cab95c19') in 0.0349906 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cucumber.prefab
  artifactKey: Guid(9241f67c97e0af24fbc5d59db57e8dcb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cucumber.prefab using Guid(9241f67c97e0af24fbc5d59db57e8dcb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '711243a294bc7da17c4bafd6438be137') in 0.0350603 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab
  artifactKey: Guid(3d7996c8b5e69564e898e9a905a75b3b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab using Guid(3d7996c8b5e69564e898e9a905a75b3b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3dff5d3bf946062e03820e5ea0f478f4') in 0.0273202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_E.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_E.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f21a65d400d469016afc9fcb538a686d') in 0.1343259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_D.prefab
  artifactKey: Guid(1338d5cb091eae64d97b3dc3e5a76c6a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_D.prefab using Guid(1338d5cb091eae64d97b3dc3e5a76c6a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7d293865d722b99f904bae72850afec4') in 0.0250225 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bench_B.fbx
  artifactKey: Guid(5cd63b215ded87340bb3f8ee5296faef) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bench_B.fbx using Guid(5cd63b215ded87340bb3f8ee5296faef) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f927348a4ad68164455816297e1a6a68') in 0.0357935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_B.fbx
  artifactKey: Guid(3f19db6275f7e884a9133eb520e1f327) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_B.fbx using Guid(3f19db6275f7e884a9133eb520e1f327) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c4982559bd343ff08d4f2331dba02abf') in 0.043217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_H.prefab
  artifactKey: Guid(f4dbaa58359d2da45b30c98d562023b5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_H.prefab using Guid(f4dbaa58359d2da45b30c98d562023b5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '182e7e64b4d9c6e05cb3ade2101caf16') in 0.0308032 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_White.prefab
  artifactKey: Guid(b758abb8dcf12a241bb2a3f268f59400) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_White.prefab using Guid(b758abb8dcf12a241bb2a3f268f59400) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'be75a4b6d5b902630962aa36c3d2e524') in 0.0250533 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_J.prefab
  artifactKey: Guid(5618744d4fa017e4ca7cab993531a415) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_J.prefab using Guid(5618744d4fa017e4ca7cab993531a415) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6b2b80bdf3beb5d447e615398039c673') in 0.0204893 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_I.prefab
  artifactKey: Guid(286f638552fb094409ed49fe87cd841a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_I.prefab using Guid(286f638552fb094409ed49fe87cd841a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2126cd090b917d6d1457ed49bf3d17d8') in 0.0266451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Hammer_C.fbx
  artifactKey: Guid(3958abbfc7c530f47858d98881eff4f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Hammer_C.fbx using Guid(3958abbfc7c530f47858d98881eff4f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8418fc0570a2d0a57fa0164cf7876b46') in 0.04781 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_C.prefab
  artifactKey: Guid(cb6fd5d63c87d4144832f932d053dea4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_C.prefab using Guid(cb6fd5d63c87d4144832f932d053dea4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f03a44646e3b50c15bafdbde9bdcf073') in 0.0255864 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_C.prefab
  artifactKey: Guid(03db32261ed5fca4bab071a2e382ba2f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_C.prefab using Guid(03db32261ed5fca4bab071a2e382ba2f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '824d8215aad2f6b5a734faaeca08c067') in 0.0255221 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_F.prefab
  artifactKey: Guid(be91c8ee26b2ff241b14c75e36a20fbd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_F.prefab using Guid(be91c8ee26b2ff241b14c75e36a20fbd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '77f3be6be2c80ffc56564108010607d4') in 0.0253209 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_B.prefab
  artifactKey: Guid(0f71f4a1d49887e4cb2b32cea8d79d36) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_B.prefab using Guid(0f71f4a1d49887e4cb2b32cea8d79d36) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '667d8c0f321e976d4571682ba362f711') in 0.0273981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Carrot.prefab
  artifactKey: Guid(a3ac9a812ed48814487c367973361ca0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Carrot.prefab using Guid(a3ac9a812ed48814487c367973361ca0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7a73b015930354e4b1868dbfb0c000f7') in 0.028416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pear_Yellow.fbx
  artifactKey: Guid(c7df04b9c715f6d42b1354fb59112c30) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pear_Yellow.fbx using Guid(c7df04b9c715f6d42b1354fb59112c30) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '310c9cb7db6125d7a0d45f73029107f4') in 0.0360277 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_B.prefab
  artifactKey: Guid(e2b36106184ed97499212c9a1abb669a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_B.prefab using Guid(e2b36106184ed97499212c9a1abb669a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8d472609b3fc8bba388e2586f3b0b8dd') in 0.0261216 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_H.prefab
  artifactKey: Guid(83d06f963d266524f944a047b8d4a341) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_H.prefab using Guid(83d06f963d266524f944a047b8d4a341) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2991319827c3647cbdcdb6316c715086') in 0.0270442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_E.prefab
  artifactKey: Guid(9a560aaee32960148b8e0757b9dc2fae) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_E.prefab using Guid(9a560aaee32960148b8e0757b9dc2fae) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0baad57026114a8360d416b310be95e1') in 0.0219547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_D.prefab
  artifactKey: Guid(3f7d2ea433dcfdb49ae575af6a323c9a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_D.prefab using Guid(3f7d2ea433dcfdb49ae575af6a323c9a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c16b9561c996eba2cb5aa2c0b6ff1bf0') in 0.0254767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_F.prefab
  artifactKey: Guid(3f03c31eb0bf9b34498ca4c8180fd0c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_F.prefab using Guid(3f03c31eb0bf9b34498ca4c8180fd0c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5fd90f507eb24fa9770c19b26d8c1be2') in 0.0243476 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_I.fbx
  artifactKey: Guid(a6f780251545d164fac9dc892421900f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_I.fbx using Guid(a6f780251545d164fac9dc892421900f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c79305d581cc762e178c3d98e844aed') in 0.0420688 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_D.prefab
  artifactKey: Guid(b06a4bb702e578c4d8bf72f9d41869e9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_D.prefab using Guid(b06a4bb702e578c4d8bf72f9d41869e9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '835004c9fd527122c929cf1521626c5a') in 0.020773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_B.prefab
  artifactKey: Guid(4acb8425bd5652640a653a1cbbeb5efd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_B.prefab using Guid(4acb8425bd5652640a653a1cbbeb5efd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '43527e40f59ef9318cf1040a7510f530') in 0.0213092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Chaff_A.prefab
  artifactKey: Guid(87d14f7e4a179bd47aa54b0ece65659c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Chaff_A.prefab using Guid(87d14f7e4a179bd47aa54b0ece65659c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f610232fc766e58c47dbc1cf655c8e51') in 0.025139 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_B.prefab
  artifactKey: Guid(2e25825c8c834b746bcce755381fcfe0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_B.prefab using Guid(2e25825c8c834b746bcce755381fcfe0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad9606f0434f9681b7d9acee3a6c15e5') in 0.0293929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Steel_B.fbx
  artifactKey: Guid(31d122d5963cde440905246209952a5b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Steel_B.fbx using Guid(31d122d5963cde440905246209952a5b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc3c9c02c18dd191fe3c2c7a0df2a0e8') in 0.0390299 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_B.prefab
  artifactKey: Guid(1d103c5bf8e12054e91774edd76d4429) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_B.prefab using Guid(1d103c5bf8e12054e91774edd76d4429) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6797d6b05d15889a6217a72e57894a20') in 0.0320629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_D.prefab
  artifactKey: Guid(43f613e6669932247a149afc0f336f2b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_D.prefab using Guid(43f613e6669932247a149afc0f336f2b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2f9131b342ffe5b4107bdadf56a73f35') in 0.0428803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000173 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_D.prefab
  artifactKey: Guid(8cdf31b700b475b41baf731edc60942f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_D.prefab using Guid(8cdf31b700b475b41baf731edc60942f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3215a9d20406f9b425afa4862f0ec8ca') in 0.0268618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Beige_A.fbx
  artifactKey: Guid(c123e4db168d88447b04ecca4816642b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Beige_A.fbx using Guid(c123e4db168d88447b04ecca4816642b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e8645f47bab46866e46d148cee390d52') in 0.0460962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pear_White.fbx
  artifactKey: Guid(36e1b1e16833aca4691791a90be3547f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pear_White.fbx using Guid(36e1b1e16833aca4691791a90be3547f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71c03ba69d3a14e25640a7bb6edc9ed0') in 0.0517872 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_A.prefab
  artifactKey: Guid(6adb6412bd2ab154397825b72fe9fd62) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_A.prefab using Guid(6adb6412bd2ab154397825b72fe9fd62) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8da15aab03593d7cf2f84be8fd4fddb5') in 0.0352492 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_E.prefab
  artifactKey: Guid(03d0ea17e99320741815f3ec783c6352) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_E.prefab using Guid(03d0ea17e99320741815f3ec783c6352) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'abc1bfcd633534872b1c12105b489227') in 0.0261651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_D.prefab
  artifactKey: Guid(1a5a7885ad3974543a25750cd1db1925) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_D.prefab using Guid(1a5a7885ad3974543a25750cd1db1925) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb94b276cc703857d75c8c9c66bd5ead') in 0.0329331 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_D.prefab
  artifactKey: Guid(1a5a7885ad3974543a25750cd1db1925) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_D.prefab using Guid(1a5a7885ad3974543a25750cd1db1925) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e25da2a450ea7f9cc0ce0e0530988128') in 0.037249 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_F.prefab
  artifactKey: Guid(154cd0487252bb747b3bd0544c1fb578) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_F.prefab using Guid(154cd0487252bb747b3bd0544c1fb578) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c01764bf0dcca51f9164e5b2a23a08ea') in 0.0478506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_H.prefab
  artifactKey: Guid(83d06f963d266524f944a047b8d4a341) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_H.prefab using Guid(83d06f963d266524f944a047b8d4a341) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '02c4e86d61d3e3fffab718f8147aff33') in 0.020644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_C.prefab
  artifactKey: Guid(632cfdd3b0364524a87f33d3943fc1e4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_C.prefab using Guid(632cfdd3b0364524a87f33d3943fc1e4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ad0ac3cd94e1173337fd7fe54b63dedc') in 0.0256045 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BowlMedicine_B.prefab
  artifactKey: Guid(4cd7093e49ac2834ca8ba5f090921f1c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BowlMedicine_B.prefab using Guid(4cd7093e49ac2834ca8ba5f090921f1c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f1e1fb3fc85676f745e39d5c6ea1003') in 0.0356936 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_H.fbx
  artifactKey: Guid(0badb61171fd40d49b2d4922fc5de3a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_H.fbx using Guid(0badb61171fd40d49b2d4922fc5de3a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ac9c203711e7aca04d2c1b6a6095549') in 0.078311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_G.prefab
  artifactKey: Guid(e01aca851b43ce242a64d94330a0c53c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_G.prefab using Guid(e01aca851b43ce242a64d94330a0c53c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '20128737dcd8a71a5e2ee9194dfa4f72') in 0.0371732 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_A.prefab
  artifactKey: Guid(863e2c8d99beb2f45b926f70b34a592d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_A.prefab using Guid(863e2c8d99beb2f45b926f70b34a592d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'be316f499cb3effcc46fa939e9488e73') in 0.0387741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_C.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_C.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd27bac27109a819d5aa765ba546514fb') in 0.050826 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_E.prefab
  artifactKey: Guid(b880f3052200d2b409b92092d09b2efb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_E.prefab using Guid(b880f3052200d2b409b92092d09b2efb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '67310e79b633617198e0c4e261f07e5a') in 0.0370533 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Hourglass_C.fbx
  artifactKey: Guid(05d0b56e75feaa045bbecf15c96d25ff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Hourglass_C.fbx using Guid(05d0b56e75feaa045bbecf15c96d25ff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2529f5921b943ccb44f3308997526c88') in 0.0379741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Parchment_B.fbx
  artifactKey: Guid(11b5df44db227454582174e65c4025f2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Parchment_B.fbx using Guid(11b5df44db227454582174e65c4025f2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd7449911c77c31de21887c145c7a2068') in 0.0350757 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Steel_A.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Steel_A.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d1860f194695557442a32f2a92a8d72') in 0.0444208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_B.prefab
  artifactKey: Guid(a3610280da04ff242af3d2fb02dab358) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_B.prefab using Guid(a3610280da04ff242af3d2fb02dab358) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ee8a21007ce1184a15e3a125e1fcd4ee') in 0.0272445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_B.fbx
  artifactKey: Guid(ed7dd576fe18ac5498f470a0dd95c900) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_B.fbx using Guid(ed7dd576fe18ac5498f470a0dd95c900) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8a0710812ada0ecfaea2687dad147ce3') in 0.0425274 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Beerglass_B.prefab
  artifactKey: Guid(a989399f82b998b49ae798b4529abafd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Beerglass_B.prefab using Guid(a989399f82b998b49ae798b4529abafd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b3691cb2020c849b8161310151417663') in 0.0790841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_H.prefab
  artifactKey: Guid(f4dbaa58359d2da45b30c98d562023b5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_H.prefab using Guid(f4dbaa58359d2da45b30c98d562023b5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5aa798cf02b4a56505f965f7c7e71998') in 0.0256742 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_G.prefab
  artifactKey: Guid(59e963992a88cc24f86f19271e37699c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_G.prefab using Guid(59e963992a88cc24f86f19271e37699c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8a2ee32f6b7b7b4503bc4d2fb79bc9a9') in 0.0267773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_B.prefab
  artifactKey: Guid(2155bbf3a7539524b9c0ed2418191700) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_B.prefab using Guid(2155bbf3a7539524b9c0ed2418191700) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '57bfbe2518180fbf9f7a1f1a5375def4') in 0.025012 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Dish_A.fbx
  artifactKey: Guid(d4ef785c5b2bcc44895aebc04604202c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Dish_A.fbx using Guid(d4ef785c5b2bcc44895aebc04604202c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '37a5b1d74862df4e40f9e9334b6c5ec1') in 0.0347363 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_G.prefab
  artifactKey: Guid(6eecd510f46dcd84ebac96e34757b6ba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_G.prefab using Guid(6eecd510f46dcd84ebac96e34757b6ba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f177e02e3a90a2e182fe9049a6622c81') in 0.0334797 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_C.prefab
  artifactKey: Guid(a40a0c1c91b73e44e85aa90673e26f44) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_C.prefab using Guid(a40a0c1c91b73e44e85aa90673e26f44) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cec83809b424557c687ef402b7e19dea') in 0.0270059 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_I.prefab
  artifactKey: Guid(234540db91fa10f46b69eae3d087b0f1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_I.prefab using Guid(234540db91fa10f46b69eae3d087b0f1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '17d4ebbad069107cbf541c74175c6d88') in 0.0306772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_D.prefab
  artifactKey: Guid(069787d8567adb34584b9c0c0de129ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_D.prefab using Guid(069787d8567adb34584b9c0c0de129ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ce1aecd1b8554c847d15199e230d29d') in 0.0380385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_D.prefab
  artifactKey: Guid(76fd593f03898184d97c39c9f8021bc2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_D.prefab using Guid(76fd593f03898184d97c39c9f8021bc2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a36a37fb6f9f14d786b44b430dadc83') in 0.0259316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_F.fbx
  artifactKey: Guid(3056655d2cb3b7f4797f631e459143b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_F.fbx using Guid(3056655d2cb3b7f4797f631e459143b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f9336868af25aea53198df4401f37d18') in 0.0390239 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_C.prefab
  artifactKey: Guid(d22d75ea99877444dbb933e64ba4016b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_C.prefab using Guid(d22d75ea99877444dbb933e64ba4016b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4751d87aef16c058d1cc5f90cbd20a5a') in 0.0280962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_I.prefab
  artifactKey: Guid(a31832b87f370a949a36eae5dd7215a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_I.prefab using Guid(a31832b87f370a949a36eae5dd7215a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dfe064f989911546ca9e518ae26d3bf1') in 0.0314191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_C.prefab
  artifactKey: Guid(a53975610aaccb74da71e55390097a77) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_C.prefab using Guid(a53975610aaccb74da71e55390097a77) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '290cb649983b54fa76284efc8534f4eb') in 0.0290247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_D.fbx
  artifactKey: Guid(d6e3378421d7e9a4aba03324cd66ecde) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_D.fbx using Guid(d6e3378421d7e9a4aba03324cd66ecde) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5f2c05d29f8190247bbfe2312434b24') in 0.0379003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_I.prefab
  artifactKey: Guid(f8c590ec91412624ba7f6759e70d6884) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_I.prefab using Guid(f8c590ec91412624ba7f6759e70d6884) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8361342d148576a59bf8f4c6ce5a5c18') in 0.038706 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Manger_B.fbx
  artifactKey: Guid(fd521b51579a1fb4b99437bea164bed3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Manger_B.fbx using Guid(fd521b51579a1fb4b99437bea164bed3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0670a113c4e3c5a4bd1939010b7ab90d') in 0.0370195 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_E.prefab
  artifactKey: Guid(aa283037a16d54a459b4f80e8ce60c07) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_E.prefab using Guid(aa283037a16d54a459b4f80e8ce60c07) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '075206aa258e817ff016b609ec807796') in 0.0291311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Scenes/Demo2.unity
  artifactKey: Guid(da8ab1f04352341449cd7f9586b070d7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Scenes/Demo2.unity using Guid(da8ab1f04352341449cd7f9586b070d7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fb916263e1dd58f274af4f329aba0fde') in 1.2760901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Well.prefab
  artifactKey: Guid(e24cfae59a3c17d489b0a6e65e74a149) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Well.prefab using Guid(e24cfae59a3c17d489b0a6e65e74a149) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab80543a724e6dd1dba208b69694eddb') in 0.0305502 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_D.fbx
  artifactKey: Guid(dab5556ce77fb69419224c40410a037b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_D.fbx using Guid(dab5556ce77fb69419224c40410a037b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1a87cfe0651b65cf8057b70b2aa324bf') in 0.0347345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stool_C.fbx
  artifactKey: Guid(1fdd209a322b3b74096d15c3e5c8cdf6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stool_C.fbx using Guid(1fdd209a322b3b74096d15c3e5c8cdf6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0803d80c0742bce83cc96c04cc10cf9') in 0.0361506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_F.fbx
  artifactKey: Guid(8e0ec2b78aee75746bb69c4910b0be5a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_F.fbx using Guid(8e0ec2b78aee75746bb69c4910b0be5a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec7636a2e3112e4fe4c78c6ac1cab79f') in 0.0415151 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Brown.prefab
  artifactKey: Guid(18d78272b9a4b254882fa45194eb318b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Brown.prefab using Guid(18d78272b9a4b254882fa45194eb318b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eebd29884571b37109463bde05eb61ef') in 0.0280099 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_L.fbx
  artifactKey: Guid(7404fec3b7449f34bb8f0175c4f28dc0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_L.fbx using Guid(7404fec3b7449f34bb8f0175c4f28dc0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8e06c69db7ebc764e616a148205d3886') in 0.0564348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_C.prefab
  artifactKey: Guid(295a8c7738d85ef49988563e40bc31cd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_C.prefab using Guid(295a8c7738d85ef49988563e40bc31cd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dbffbfc81c32a21d4d034c14c7dc4c0c') in 0.0439901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bench_E.fbx
  artifactKey: Guid(b5b7d45f009384b4097e5ecb4316a1ca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bench_E.fbx using Guid(b5b7d45f009384b4097e5ecb4316a1ca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '74c4256216265816b12bae3c8706beb8') in 0.0465827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Water_B.fbx
  artifactKey: Guid(e5b7109e6fba7a74b983c1ae4fbb7d2e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Water_B.fbx using Guid(e5b7109e6fba7a74b983c1ae4fbb7d2e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f9f7dfc0628974008625895bb21b6bb') in 0.0413294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Prop_C.fbx
  artifactKey: Guid(255e25f2c977c114d98f72c22178324c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Prop_C.fbx using Guid(255e25f2c977c114d98f72c22178324c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4470771672e4435cc0d7732d290bf900') in 0.0377893 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_B.prefab
  artifactKey: Guid(2e7cb8d40151bb849b4d5d35bcd61034) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_B.prefab using Guid(2e7cb8d40151bb849b4d5d35bcd61034) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ce4eeafb09ff834609493bba6ed7bec') in 0.0306506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_E.prefab
  artifactKey: Guid(9a560aaee32960148b8e0757b9dc2fae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_E.prefab using Guid(9a560aaee32960148b8e0757b9dc2fae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89ebcf3901f8c2a04bcadbef5d9569c2') in 0.0312107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/CandleChandelier_B.fbx
  artifactKey: Guid(82e248e62e46d6e4fa292df697de95d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/CandleChandelier_B.fbx using Guid(82e248e62e46d6e4fa292df697de95d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '95e0b132029517bc6dc5673c63316450') in 0.0510055 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_C.prefab
  artifactKey: Guid(fe7f934f17ffd744ab90d3b77cbf8916) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_C.prefab using Guid(fe7f934f17ffd744ab90d3b77cbf8916) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '655cdf2abb6dc37730ae9bd1aea0063b') in 0.0320519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_C.fbx
  artifactKey: Guid(0d35316c09f643248baef50b8c57e37e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_C.fbx using Guid(0d35316c09f643248baef50b8c57e37e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a8995bb95c7435fe0e52a3a834b411b') in 0.0368892 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_D.prefab
  artifactKey: Guid(eabd4d6fda3311f4b916fb6784dfaec8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_D.prefab using Guid(eabd4d6fda3311f4b916fb6784dfaec8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cd2ecc4408ace6a8301bb25cc37ab03a') in 0.03006 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_A.prefab
  artifactKey: Guid(c7a6ae6a0287d5648a3bbe692ffe016b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_A.prefab using Guid(c7a6ae6a0287d5648a3bbe692ffe016b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9cc8210ad437b84be6294db812de586b') in 0.0377213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_A.prefab
  artifactKey: Guid(a4f3d19d5f135f8488c9699bd299df52) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_A.prefab using Guid(a4f3d19d5f135f8488c9699bd299df52) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c0ee00db156a02e03005ede49e13c1c') in 0.0284883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/SiegeTower_A.prefab
  artifactKey: Guid(eadb7807054abd8479bae0780283dce9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/SiegeTower_A.prefab using Guid(eadb7807054abd8479bae0780283dce9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eed94e2c072686b0c5cdefdea4242b3b') in 0.0284596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_H.fbx
  artifactKey: Guid(ef61e31de902e7e41a4fa7f67ab092c0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_H.fbx using Guid(ef61e31de902e7e41a4fa7f67ab092c0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc3c445838a77ff12f22e8e15ee7ea9f') in 0.0413647 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_I.fbx
  artifactKey: Guid(bde885c917453724e9d53cdd0188e68a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_I.fbx using Guid(bde885c917453724e9d53cdd0188e68a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a2e3d735036e6c2d380e658194ae3f1') in 0.0344856 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_B.prefab
  artifactKey: Guid(83f7f91ffd16f39409d9fcfeec4b21d2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_B.prefab using Guid(83f7f91ffd16f39409d9fcfeec4b21d2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc1cbee751abbc432c50fe533a6f792b') in 0.0347983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Chimney.fbx
  artifactKey: Guid(c0607f1fd654bba4598421f27b9572a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Chimney.fbx using Guid(c0607f1fd654bba4598421f27b9572a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f8c9aed6e6a896c6ee12e556da75dda') in 0.041118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_B.prefab
  artifactKey: Guid(f4cb87f72c4ae6846bbe4245c36bfff1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_B.prefab using Guid(f4cb87f72c4ae6846bbe4245c36bfff1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b16822b82c4852ed1de524cada052c38') in 0.0302091 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pumpkin_A.prefab
  artifactKey: Guid(fc41251d8b3f4034985141e1b8988bb6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pumpkin_A.prefab using Guid(fc41251d8b3f4034985141e1b8988bb6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '527d07e146c2e0b1e0082fd70dcc0bcc') in 0.0291509 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_J.fbx
  artifactKey: Guid(354a21cef40867e499f999789dd51e8e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_J.fbx using Guid(354a21cef40867e499f999789dd51e8e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a428dba2093433b207b24767b2496898') in 0.0429543 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Script/Economy/UI/ShopItemUI.cs
  artifactKey: Guid(7221155e241221147bc43e904eb05ff2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/UI/ShopItemUI.cs using Guid(7221155e241221147bc43e904eb05ff2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '38e496027013e315e5e0287cb80e3fea') in 0.0255107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_H.prefab
  artifactKey: Guid(57d9308fd37a43949998a11bfe0e0130) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_H.prefab using Guid(57d9308fd37a43949998a11bfe0e0130) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '47a95611653a16a954f5a6c415887d4a') in 0.0266839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part.prefab
  artifactKey: Guid(42ca3118d1852ba46ae739f0fe44923b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part.prefab using Guid(42ca3118d1852ba46ae739f0fe44923b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e227a7edaa6f11caef6622ade0395c4a') in 0.0284218 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Plane.fbx
  artifactKey: Guid(5f1a44803154d4043afc85ae36f2ceff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Plane.fbx using Guid(5f1a44803154d4043afc85ae36f2ceff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '10965462ecfa68c8661135ca10e68a2f') in 0.0390235 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_E.prefab
  artifactKey: Guid(bee69969654bab2419ca1b9ba00ca9d9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_E.prefab using Guid(bee69969654bab2419ca1b9ba00ca9d9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7f8a111c5d5441f7fa3a781f8e3bf312') in 0.0455051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

Editor requested this worker to shutdown with reason: balancing worker count downwards
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0