Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker2.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21792]  Target information:

Player connection [21792]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3075453402 [EditorId] 3075453402 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21792] Host joined multi-casting on [***********:54997]...
Player connection [21792] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56012
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002202 seconds.
- Loaded All Assemblies, in  1.184 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.121 seconds
Domain Reload Profiling: 2307ms
	BeginReloadAssembly (259ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (787ms)
		LoadAssemblies (257ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (783ms)
			TypeCache.Refresh (774ms)
				TypeCache.ScanAssembly (710ms)
			BuildScriptInfoCaches (1ms)
			ResolveRequiredComponents (5ms)
	FinalizeReload (1122ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (875ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (112ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (187ms)
			ProcessInitializeOnLoadAttributes (422ms)
			ProcessInitializeOnLoadMethodAttributes (139ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.834 seconds
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.752 seconds
Domain Reload Profiling: 3583ms
	BeginReloadAssembly (356ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (88ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (76ms)
	LoadAllAssembliesAndSetupDomain (1286ms)
		LoadAssemblies (638ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (838ms)
			TypeCache.Refresh (591ms)
				TypeCache.ScanAssembly (536ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (49ms)
	FinalizeReload (1752ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1287ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (13ms)
			BeforeProcessingInitializeOnLoad (369ms)
			ProcessInitializeOnLoadAttributes (809ms)
			ProcessInitializeOnLoadMethodAttributes (89ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (4.2 MB). Loaded Objects now: 7294.
Memory consumption went from 169.0 MB to 164.9 MB.
Total: 21.384900 ms (FindLiveObjects: 1.576100 ms CreateObjectMapping: 2.162800 ms MarkObjects: 11.803400 ms  DeleteObjects: 5.840800 ms)

========================================================================
Received Import Request.
  Time since last request: 103329.037616 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_C.prefab
  artifactKey: Guid(d3756bbc267910243920407c724a4328) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_C.prefab using Guid(d3756bbc267910243920407c724a4328) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '016f8817dd4b568a61399bee2543afbb') in 0.4123043 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_B.fbx
  artifactKey: Guid(5df9c43dcf79d7e4594a9fc0c91337e7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_B.fbx using Guid(5df9c43dcf79d7e4594a9fc0c91337e7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8cd754373e74373858729bb68fccc13') in 0.6660924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Script/Player/PlayerSetupHelper.cs
  artifactKey: Guid(48fdf146c82a7ca418f40dbca9d05632) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/PlayerSetupHelper.cs using Guid(48fdf146c82a7ca418f40dbca9d05632) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7c2666d64d899b634ad70b4cacd9eb9c') in 0.0666989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_C.prefab
  artifactKey: Guid(a227505c56765be4cabdc802c8d5e969) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_C.prefab using Guid(a227505c56765be4cabdc802c8d5e969) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34cd0e090ff051ccbe09db214bee43f9') in 0.0368333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_A.prefab
  artifactKey: Guid(afab7be7eda641640961e5f751b0e9b9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_A.prefab using Guid(afab7be7eda641640961e5f751b0e9b9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b8b40892d3766fca3e0562352f98a5ee') in 0.2065275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_B.prefab
  artifactKey: Guid(7362c22d45c575b41ac0170615c0920f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_B.prefab using Guid(7362c22d45c575b41ac0170615c0920f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45206920f14b2b518ccd01177dc5d8ff') in 0.0262212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Settings/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/UniversalRenderPipelineGlobalSettings.asset using Guid(18dc0cd2c080841dea60987a38ce93fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0cf07c40ce9220be6d1fef208485ec2f') in 0.1630442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_A.fbx
  artifactKey: Guid(bc58444d8ed4f9446866eba6329d88a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_A.fbx using Guid(bc58444d8ed4f9446866eba6329d88a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bfff3c9e202f0af181974f5cf38824e0') in 0.0331268 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Script/Player/CameraSystemValidator.cs
  artifactKey: Guid(7687b484e541eb54d9981b4c937719bf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/CameraSystemValidator.cs using Guid(7687b484e541eb54d9981b4c937719bf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d0d2975f030399b8eb46aa52ae527ed') in 0.0198317 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_C.prefab
  artifactKey: Guid(189e84ff372d3254e9ab89cd5a5255a7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_C.prefab using Guid(189e84ff372d3254e9ab89cd5a5255a7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2b7b849f23ce4d52de6dfc35415be352') in 0.0244698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Script/Player/Editor/PlayerModelSetupEditor.cs
  artifactKey: Guid(d17df5db34b698e489e899ec23374109) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Editor/PlayerModelSetupEditor.cs using Guid(d17df5db34b698e489e899ec23374109) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '51eeba92dd53bc5da9d45e0aa9c31e15') in 0.0293203 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Script/Player/CameraTestDemo.cs
  artifactKey: Guid(074eeb385c17c45429c0577a06844509) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/CameraTestDemo.cs using Guid(074eeb385c17c45429c0577a06844509) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f9b4e0700aeb951284a0ca332e262183') in 0.022484 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Script/Player/Guides/HUONG_DAN_SU_DUNG_CAMERA.md
  artifactKey: Guid(2dd233d31885e814887f7436a13496e6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/HUONG_DAN_SU_DUNG_CAMERA.md using Guid(2dd233d31885e814887f7436a13496e6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ccf162a4919578b4c0562b06a3e1bdc7') in 0.0346622 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Chisel.prefab
  artifactKey: Guid(52f88ce38e88f9a49a85725cfd817733) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Chisel.prefab using Guid(52f88ce38e88f9a49a85725cfd817733) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '102d4b6ce7db2eb33378c268bbc80243') in 0.0211241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_M.prefab
  artifactKey: Guid(f9667894b9766334d8ea630f8fec0c15) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_M.prefab using Guid(f9667894b9766334d8ea630f8fec0c15) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1774416dc6812c361eab347bf90e8a20') in 0.0231019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_White.prefab
  artifactKey: Guid(1f92c86feee0c7f41a808ac2a1bb7fbf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_White.prefab using Guid(1f92c86feee0c7f41a808ac2a1bb7fbf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1da51fa8812e25bab6e131a0e65229e3') in 0.0293122 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_B.prefab
  artifactKey: Guid(0aee6e4d8ad1c9e45a652d62a103dd91) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_B.prefab using Guid(0aee6e4d8ad1c9e45a652d62a103dd91) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '49e90ae910765294a6243d31cdc4f81a') in 0.0232671 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tree_E.fbx
  artifactKey: Guid(28f7a98eb189e0247aac9c78a9a10a8b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tree_E.fbx using Guid(28f7a98eb189e0247aac9c78a9a10a8b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '53c73c0297573f390d0f2ac7e02b9ff9') in 0.0395229 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Water_Tile_A.fbx
  artifactKey: Guid(2d3c37d5f0b01dd4eb346dacb2cfe6e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Water_Tile_A.fbx using Guid(2d3c37d5f0b01dd4eb346dacb2cfe6e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45064050e80b5ce10be3e93cfe587ded') in 0.0360441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/FeatherQuillPenInk_B.fbx
  artifactKey: Guid(e6322b22d44b97b44aa0b6bad7598648) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/FeatherQuillPenInk_B.fbx using Guid(e6322b22d44b97b44aa0b6bad7598648) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd692990adce94850ebe73ba28f1c2781') in 0.0378447 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_T.prefab
  artifactKey: Guid(b4a85e25ebd23aa4b94f627a80b3bd4d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_T.prefab using Guid(b4a85e25ebd23aa4b94f627a80b3bd4d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '27771fbae04fe623a5edfb8c42f4bd9a') in 0.0328823 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Apple_Yellow.fbx
  artifactKey: Guid(2e8b3dee7990a4145a538c97f103f5c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Apple_Yellow.fbx using Guid(2e8b3dee7990a4145a538c97f103f5c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '667c77b55d6cc68b49cce8cb12cd37bd') in 0.0385034 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_D.prefab
  artifactKey: Guid(958246969e6df094daa9f4048dcf3adb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_D.prefab using Guid(958246969e6df094daa9f4048dcf3adb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd26be5702cd7c77156e3dc7a68905eb9') in 0.0215692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Carrot.prefab
  artifactKey: Guid(a3ac9a812ed48814487c367973361ca0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Carrot.prefab using Guid(a3ac9a812ed48814487c367973361ca0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5fd43e025d8dd772a6acffbda8f76465') in 0.0207042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_D.prefab
  artifactKey: Guid(6c67f74f452ded741a93e84cf491e67f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_D.prefab using Guid(6c67f74f452ded741a93e84cf491e67f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6c9c2f900056e3a63b8525dfcda269aa') in 0.0238692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_B.prefab
  artifactKey: Guid(7362c22d45c575b41ac0170615c0920f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_B.prefab using Guid(7362c22d45c575b41ac0170615c0920f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '43d1faebfd6e58dfee545b3cfc9206a9') in 0.0227444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000362 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_B.prefab
  artifactKey: Guid(cc061631cd2b78b4f995a5cea9765dfb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_B.prefab using Guid(cc061631cd2b78b4f995a5cea9765dfb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c430809ebea5894d0c2033ce3d0da3c8') in 0.0340751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_H.prefab
  artifactKey: Guid(49a0237bc758372418f97f59ddf69271) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_H.prefab using Guid(49a0237bc758372418f97f59ddf69271) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b4f579124ba8194cf75374f14591fb7c') in 0.0229893 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_J.prefab
  artifactKey: Guid(2f10db60eb0a4194aba0d558deb6ac25) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_J.prefab using Guid(2f10db60eb0a4194aba0d558deb6ac25) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f6dff2f74b5fc0710d04661506d4386') in 0.0321928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_C.prefab
  artifactKey: Guid(e9c45f806622211479bb545666655237) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_C.prefab using Guid(e9c45f806622211479bb545666655237) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7981aba666085993409c22bdfe376ed5') in 0.0273561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_A.fbx
  artifactKey: Guid(10763f030b0113d48886dc8513b29c0d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_A.fbx using Guid(10763f030b0113d48886dc8513b29c0d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f4ea22ae8b97704b922005b5fcad202a') in 0.0406364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_G.prefab
  artifactKey: Guid(35834a89c5743454fab1db36463ee40e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_G.prefab using Guid(35834a89c5743454fab1db36463ee40e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '73c7a49b9de83ed4d2fbb62ccd9d1945') in 0.0231982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_O.prefab
  artifactKey: Guid(b7aee3d129cec3b46956b3ac917cb919) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_O.prefab using Guid(b7aee3d129cec3b46956b3ac917cb919) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f4fbef6b40ce9db3099b73d29d611e93') in 0.022824 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_D.prefab
  artifactKey: Guid(bfb523f96e9f42849ab2eef3dee0e284) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_D.prefab using Guid(bfb523f96e9f42849ab2eef3dee0e284) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2b52e15d32fbefb6f06e3017dc3af170') in 0.0237316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_K.prefab
  artifactKey: Guid(6fc4f53d9b505ee49b1a4d2f02dea5cf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_K.prefab using Guid(6fc4f53d9b505ee49b1a4d2f02dea5cf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8acc395b0e81ba7ffcaa2ceb2ecb2698') in 0.0203113 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_L.prefab
  artifactKey: Guid(f389977473f85ac47bdb277cca621df6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_L.prefab using Guid(f389977473f85ac47bdb277cca621df6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '66104e4dd19e51f9ee099359bf5793dc') in 0.0259982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_A.prefab
  artifactKey: Guid(70fc929f93fb6d44a80290abc88edc22) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_A.prefab using Guid(70fc929f93fb6d44a80290abc88edc22) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1df0a3c095baebd444cf86384f56475c') in 0.0309248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000163 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_B.prefab
  artifactKey: Guid(795976c6200121c408074881b8d0d368) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_B.prefab using Guid(795976c6200121c408074881b8d0d368) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6aeb6656c9c176490ffe241a73ee3963') in 0.0307672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_A.prefab
  artifactKey: Guid(9fdff433abb1c52498f4a03ae2421ba6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_A.prefab using Guid(9fdff433abb1c52498f4a03ae2421ba6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fc14933ca590d58786fbaf3df616d292') in 0.024946 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_L.prefab
  artifactKey: Guid(a20b372cd1a4a334e875c26c57612cdb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_L.prefab using Guid(a20b372cd1a4a334e875c26c57612cdb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '57d2d4a8125a7534e7740edd17c3a925') in 0.0264176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_D.fbx
  artifactKey: Guid(d77da1a1cb4ec5945bb122f4957f061e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_D.fbx using Guid(d77da1a1cb4ec5945bb122f4957f061e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ba5509c8011ea1e95538ec4755447eff') in 0.0360012 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_B.fbx
  artifactKey: Guid(a5a1867afaf6a344e8797c0bb8db2596) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_B.fbx using Guid(a5a1867afaf6a344e8797c0bb8db2596) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8342a531ea5bc7f23605327cafa0174a') in 0.0359093 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_C.prefab
  artifactKey: Guid(f03f21bc33e4d014fa77a5bb6350cea1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_C.prefab using Guid(f03f21bc33e4d014fa77a5bb6350cea1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '272fe70eb7d82ec37ed4660c4de1c7c8') in 0.059639 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_B.prefab
  artifactKey: Guid(88069fd65e27ae648bfb5f67aa30db2d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_B.prefab using Guid(88069fd65e27ae648bfb5f67aa30db2d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f465068de1b5ad399b99ff2dc26cb184') in 0.06979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_C.prefab
  artifactKey: Guid(2daa8b7bdb5112743a7816fc13aef758) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_C.prefab using Guid(2daa8b7bdb5112743a7816fc13aef758) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd86f8b64146d4a2dd61753f43715c47f') in 0.0269515 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_F.fbx
  artifactKey: Guid(581c604aabc782a418b8bcd2bfd50ca5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_F.fbx using Guid(581c604aabc782a418b8bcd2bfd50ca5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '93c913d5c795da1f51323180fa960038') in 0.0490967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_D.prefab
  artifactKey: Guid(d909c1a2efcb72d4d87b3c9daf5c3f7d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_D.prefab using Guid(d909c1a2efcb72d4d87b3c9daf5c3f7d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd130f3575a477d2dfe25e342afba4518') in 0.0414572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_B.prefab
  artifactKey: Guid(88069fd65e27ae648bfb5f67aa30db2d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_B.prefab using Guid(88069fd65e27ae648bfb5f67aa30db2d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d8f903fe93e7cd58af52e9d0b5a5c55') in 0.0359119 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000114 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Radish.prefab
  artifactKey: Guid(b0b380fa8bc46e7498f2143a15a93afc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Radish.prefab using Guid(b0b380fa8bc46e7498f2143a15a93afc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '460a6561b0563edf73ba81ecb9645423') in 0.0241006 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_F.prefab
  artifactKey: Guid(5b5048f232d5af6458fecb05c2d5f6aa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_F.prefab using Guid(5b5048f232d5af6458fecb05c2d5f6aa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2a2c828af17828484b8d68b3814a93c') in 0.0327061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_D.prefab
  artifactKey: Guid(4679d96fbc9b79c4d93cc4409a42491b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_D.prefab using Guid(4679d96fbc9b79c4d93cc4409a42491b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ff959752b0dd7e2cf371e3151851aa5') in 0.0404296 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Chaff_B.prefab
  artifactKey: Guid(220df28aef0fc404c883bece59dcd74c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Chaff_B.prefab using Guid(220df28aef0fc404c883bece59dcd74c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c62a0eb4629268d254cd4e5065cfe72') in 0.0351876 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_C.prefab
  artifactKey: Guid(609dc42943db16a49a3f14a5a6bac380) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_C.prefab using Guid(609dc42943db16a49a3f14a5a6bac380) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd57760f689b0f017f958cef2b5723ac6') in 0.0224655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_A.prefab
  artifactKey: Guid(bd7a59239ce6dcb4884350f8b44cfcdd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_A.prefab using Guid(bd7a59239ce6dcb4884350f8b44cfcdd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2a37dedbb7b8b83fccd933a7feac6dc0') in 0.0260164 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Table_G.fbx
  artifactKey: Guid(a214386049b21a9448ede39ab6fdac11) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Table_G.fbx using Guid(a214386049b21a9448ede39ab6fdac11) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ffd0ebdfeb8bc92367c0ceef21cdfc12') in 0.0442657 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_A.prefab
  artifactKey: Guid(7cd595948f2c8c14fb6a10b1048b5279) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_A.prefab using Guid(7cd595948f2c8c14fb6a10b1048b5279) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d61bb6a073eaceff8965e7392f3a684') in 0.0665974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_B.prefab
  artifactKey: Guid(dc82ce884131ddb4c9873829df12d61f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_B.prefab using Guid(dc82ce884131ddb4c9873829df12d61f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb4e357e7ca78cb8c4de4667ccc435b7') in 0.0394506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_A.prefab
  artifactKey: Guid(7fecedaa3e5944a4b9deea930b0ff8da) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_A.prefab using Guid(7fecedaa3e5944a4b9deea930b0ff8da) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a5a0ef7ba98350a51bbcf5b11a20652a') in 0.0294759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_C.prefab
  artifactKey: Guid(9deaa2f3df142c54f81826a5d1d43ad0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_C.prefab using Guid(9deaa2f3df142c54f81826a5d1d43ad0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ba799bf94cc8968d60dea68e4fa9cab8') in 0.0364146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_C.prefab
  artifactKey: Guid(5bb3e555d0e555444ba6b1abb43e32dd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_C.prefab using Guid(5bb3e555d0e555444ba6b1abb43e32dd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '49c5815bca9d46e64c39e3a7edde8914') in 0.0380819 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_A.prefab
  artifactKey: Guid(13abd986b4814744da95d88992f89972) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_A.prefab using Guid(13abd986b4814744da95d88992f89972) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '90830b9b29d15753d0a92e121ffa57c1') in 0.0345607 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_H.prefab
  artifactKey: Guid(f1ed631a750e7434f9e744021e8d3668) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_H.prefab using Guid(f1ed631a750e7434f9e744021e8d3668) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd2278c4da0822e4e65590f43ab3177d0') in 0.0227755 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000167 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Prop_B.fbx
  artifactKey: Guid(fc4fe9be574bca64185b33c38a538219) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Prop_B.fbx using Guid(fc4fe9be574bca64185b33c38a538219) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4627839c33e4e533baaab851393ff7ef') in 0.0416392 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodB_A.fbx
  artifactKey: Guid(28619516831fdbd4082f459223b30af2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodB_A.fbx using Guid(28619516831fdbd4082f459223b30af2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '38b6c84497f10ba5f372b489addcde19') in 0.0398056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_C.prefab
  artifactKey: Guid(69d4c9b575c6f2448a8e49159482982b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_C.prefab using Guid(69d4c9b575c6f2448a8e49159482982b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c617cb8bd08a03bda687a883c3dbbb15') in 0.0544833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_A.fbx
  artifactKey: Guid(983c0c9269077c549b6da7ee1d71f8f9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_A.fbx using Guid(983c0c9269077c549b6da7ee1d71f8f9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b540d3e180015d14285c6f2e04c9827') in 0.048088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_A.prefab
  artifactKey: Guid(8aab0018324fa7a4ca02c2cf0bbb9cd0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_A.prefab using Guid(8aab0018324fa7a4ca02c2cf0bbb9cd0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e90681fbd9ddea51084189c90705f5df') in 0.0910868 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_C.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_C.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '646d5f9c07bb711984112aa39636f699') in 0.0347807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_F.fbx
  artifactKey: Guid(a4a8c06a7dfabd04798f977e6b5ed6e2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_F.fbx using Guid(a4a8c06a7dfabd04798f977e6b5ed6e2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe8adef4995604fc408bf302e1f1f5b4') in 0.0438058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Window.mat
  artifactKey: Guid(43ea1f0fe66b5a64eac755138f7fd0de) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Window.mat using Guid(43ea1f0fe66b5a64eac755138f7fd0de) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d174528024a203f51b8b140be694d24') in 0.058929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_F.prefab
  artifactKey: Guid(a8f1a6b1fe246a040a6894eacddd6b80) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_F.prefab using Guid(a8f1a6b1fe246a040a6894eacddd6b80) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3e04869e3c854cbad47953ddc68c0d7') in 0.0379611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_Q.prefab
  artifactKey: Guid(4b504dde5f72b224e998f5109bc8b772) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_Q.prefab using Guid(4b504dde5f72b224e998f5109bc8b772) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cfc550717ca5191085e5cd0e078b6949') in 0.0284835 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Script/Player/Guides/HUONG_DAN_CAP_NHAT_INSPECTOR.md
  artifactKey: Guid(5667a30912316534a855be2cc00ab06c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/HUONG_DAN_CAP_NHAT_INSPECTOR.md using Guid(5667a30912316534a855be2cc00ab06c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3c231f7bab3bb3844930806d9019f586') in 0.0343676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_White.prefab
  artifactKey: Guid(b758abb8dcf12a241bb2a3f268f59400) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_White.prefab using Guid(b758abb8dcf12a241bb2a3f268f59400) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '657cafbc68cd0c5ac7f23fe742ff6c55') in 0.0337407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_F.fbx
  artifactKey: Guid(0a33e0661355c8c4abac74da2c75c5b4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_F.fbx using Guid(0a33e0661355c8c4abac74da2c75c5b4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7a28a0fe3333e7317c07da5c38d55219') in 0.0413488 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000226 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_B.prefab
  artifactKey: Guid(6105094c528af8342b4192e8194e14ff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_B.prefab using Guid(6105094c528af8342b4192e8194e14ff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '68c705f4157a655d20e737b6df77c7c9') in 0.0363248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_A.fbx
  artifactKey: Guid(2ba314fa5fdff05429c0f7631c3548ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_A.fbx using Guid(2ba314fa5fdff05429c0f7631c3548ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f3142115cb90e985ecc565c2a186bd3') in 0.0333672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_C.prefab
  artifactKey: Guid(26a6917091cf52a4d9c2ccefa20089cd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_C.prefab using Guid(26a6917091cf52a4d9c2ccefa20089cd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '650af1320e729ea8fc2bb25341756fb3') in 0.0308866 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_I.prefab
  artifactKey: Guid(c31b2cc8e2fcb6f418cf5d361339b5d3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_I.prefab using Guid(c31b2cc8e2fcb6f418cf5d361339b5d3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '73c9eac6a2c209653cc57c54f2479666') in 0.0272452 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_E.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_E.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ab543456f007626c8291f0b0d0215d32') in 0.0291611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_H.prefab
  artifactKey: Guid(b5d8524735b835e48b4c020c164fec41) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_H.prefab using Guid(b5d8524735b835e48b4c020c164fec41) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c0cffe3a280114613e0fa4c6c403ec80') in 0.0408103 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_F.fbx
  artifactKey: Guid(8e318d9bd21bc854a890acb13984d2ff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_F.fbx using Guid(8e318d9bd21bc854a890acb13984d2ff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '236b437f27dc08c4e30c806464bc2010') in 0.0384506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000088 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Dish_B.prefab
  artifactKey: Guid(823e639296de42f47a9c28fd128daf46) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Dish_B.prefab using Guid(823e639296de42f47a9c28fd128daf46) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d6fab2d3c7fea608600d416449083f0') in 0.0269245 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bench_A.fbx
  artifactKey: Guid(4e8ff42edf8826e4c8efabee8b0bef3d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bench_A.fbx using Guid(4e8ff42edf8826e4c8efabee8b0bef3d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cebebaf62638806446a6f4703378311d') in 0.0503758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_C.prefab
  artifactKey: Guid(65ac9dabe47805b40921a3bdc94cd197) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_C.prefab using Guid(65ac9dabe47805b40921a3bdc94cd197) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '44e3f5f8f8d00b063d847c892dcf7de0') in 0.0323113 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_J.prefab
  artifactKey: Guid(470c6dcd44a4f5247a4ebb15c9ecf1a5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_J.prefab using Guid(470c6dcd44a4f5247a4ebb15c9ecf1a5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e232bdc0ae497a3ec4e2a5082812a8bc') in 0.0253266 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bucket_Wood_C.fbx
  artifactKey: Guid(0081040fa1b834145ac07e0bd3677a99) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bucket_Wood_C.fbx using Guid(0081040fa1b834145ac07e0bd3677a99) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f7b3840eb5e101ad75592e17909d8642') in 0.0453229 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/CandleChandelier_C.fbx
  artifactKey: Guid(27517233315247040afa6952ccf8858a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/CandleChandelier_C.fbx using Guid(27517233315247040afa6952ccf8858a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c0dff74d6bb99522c073f3b42d78e8c') in 0.0457692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_A.prefab
  artifactKey: Guid(6adb6412bd2ab154397825b72fe9fd62) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_A.prefab using Guid(6adb6412bd2ab154397825b72fe9fd62) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5a4494df075c92b35e9ee75c1c5c1d8b') in 0.0310016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tree_A.fbx
  artifactKey: Guid(8a5f8f0d1e322c540bf7c64833d41b61) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tree_A.fbx using Guid(8a5f8f0d1e322c540bf7c64833d41b61) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '53f6db899e1a8406677df7b952b69957') in 0.03666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_G.fbx
  artifactKey: Guid(1d7dcdb6b9d60124689e80b170ba6fad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_G.fbx using Guid(1d7dcdb6b9d60124689e80b170ba6fad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f4178d888aee266a0fc2dbdca7531561') in 0.040094 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_B.fbx
  artifactKey: Guid(78e1fbedc7f8fac41a677b64374d289d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_B.fbx using Guid(78e1fbedc7f8fac41a677b64374d289d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76c2abd97d1a8ec1e8851f3779c4aabb') in 0.0374547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Anvil_B.prefab
  artifactKey: Guid(1ad2d79931f4ba8428570752d45e50a1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Anvil_B.prefab using Guid(1ad2d79931f4ba8428570752d45e50a1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec9b6ae916532e75f04132ebcf764b20') in 0.0305763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_A.fbx
  artifactKey: Guid(a7f586d66817d9d4b8d96b072eed209a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_A.fbx using Guid(a7f586d66817d9d4b8d96b072eed209a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'acb08fdf6d72ac2685e196b303cef5dd') in 0.0369482 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_D.fbx
  artifactKey: Guid(1f6654b33c24f6f4b83a3d11ab23362a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_D.fbx using Guid(1f6654b33c24f6f4b83a3d11ab23362a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cdae193ee3cd76ab841633c6a164188f') in 0.0366005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_E.prefab
  artifactKey: Guid(c35313a39dae0cd4fae5343609e34d5e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_E.prefab using Guid(c35313a39dae0cd4fae5343609e34d5e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81dad58cfdcddcca83847bc1581255d2') in 0.0325408 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_H.fbx
  artifactKey: Guid(662a0ba3a93f2a741a18c107eb159815) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_H.fbx using Guid(662a0ba3a93f2a741a18c107eb159815) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a33bb1d6db89b4507eae6d689adb05e8') in 0.0349163 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_B.prefab
  artifactKey: Guid(4b54c74a7d892f44e82fad64a0c8b59f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_B.prefab using Guid(4b54c74a7d892f44e82fad64a0c8b59f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e06cfc318bb56fa570bf0adaf4769e8') in 0.0333155 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Rock_A.fbx
  artifactKey: Guid(4b3ec05bd52e87a4b909847952c63fb1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Rock_A.fbx using Guid(4b3ec05bd52e87a4b909847952c63fb1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c70ac7905e6c814ea82a05ae8ca7109') in 0.0823407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Table_E.fbx
  artifactKey: Guid(e5bb2dd92487dc448bfedec686d37d77) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Table_E.fbx using Guid(e5bb2dd92487dc448bfedec686d37d77) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2819fc874a07082d72481237fd0b52d') in 0.0424452 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_J.prefab
  artifactKey: Guid(fba32127fd2140747aae8308d7268ff6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_J.prefab using Guid(fba32127fd2140747aae8308d7268ff6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c465c9173218f933505dcd161dbcb23') in 0.0274941 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_A.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_A.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eea5987a99c48c546996055f7d174b04') in 0.0267586 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_C.prefab
  artifactKey: Guid(e2727f92b0de8474c9524324042c802c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_C.prefab using Guid(e2727f92b0de8474c9524324042c802c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c7a929b77a74297b263515f1b5bbfc22') in 0.0259073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_J.prefab
  artifactKey: Guid(5618744d4fa017e4ca7cab993531a415) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_J.prefab using Guid(5618744d4fa017e4ca7cab993531a415) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b0b0a2eced78c958b0c7da249255fb9') in 0.030925 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_E.prefab
  artifactKey: Guid(b97f5974907c3684ba79e30ee8e695af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_E.prefab using Guid(b97f5974907c3684ba79e30ee8e695af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9258de9b5cee40900b59ad27920f8e81') in 0.0321596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_F.prefab
  artifactKey: Guid(9bfe739cac043884b91a864dce4a3e92) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_F.prefab using Guid(9bfe739cac043884b91a864dce4a3e92) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a4b386c337260d4b0cb970937e1c9870') in 0.0351777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Cup_A.fbx
  artifactKey: Guid(40a1c41df4d7ac641a07a244c57a5a08) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Cup_A.fbx using Guid(40a1c41df4d7ac641a07a244c57a5a08) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89f75baa5a5870dbb54c988b5e784298') in 0.0424506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_E.prefab
  artifactKey: Guid(14e14126775dcd14a9f7f94650899f87) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_E.prefab using Guid(14e14126775dcd14a9f7f94650899f87) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1b9e096a7694b638cb93f560ab85dcf2') in 0.0331105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bench_D.fbx
  artifactKey: Guid(ad552b88ab1ee32408f8dac494506412) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bench_D.fbx using Guid(ad552b88ab1ee32408f8dac494506412) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b2d606e3556846dbe99fc0718c90603') in 0.0476245 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_A.prefab
  artifactKey: Guid(1087e272b56684a49b1c245d1af9d4a9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_A.prefab using Guid(1087e272b56684a49b1c245d1af9d4a9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e916ba6b6b70fc5ae0a1928f2419963') in 0.0316033 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Blue.prefab
  artifactKey: Guid(8076d9e3de371c646a627939bfa655b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Blue.prefab using Guid(8076d9e3de371c646a627939bfa655b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08d76d48a7d7dec7917ad6e3758e9ba5') in 0.0297924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_D.prefab
  artifactKey: Guid(1338d5cb091eae64d97b3dc3e5a76c6a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_D.prefab using Guid(1338d5cb091eae64d97b3dc3e5a76c6a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0dc42a80aa692a4d187805e6c26f4f01') in 0.0389747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000250 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Brown.prefab
  artifactKey: Guid(251f30be14ace6f41bcea5630f6242ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Brown.prefab using Guid(251f30be14ace6f41bcea5630f6242ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc89291774d3e1fc99c2017c98952a7c') in 0.034127 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_D.prefab
  artifactKey: Guid(e20e5c66c7b3867419ef91de2d147d17) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_D.prefab using Guid(e20e5c66c7b3867419ef91de2d147d17) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ca58b60f8ead5a6868e65ffd05c7ea1') in 0.0396681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_G.fbx
  artifactKey: Guid(757801213e453204c9e114ac173a6c34) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_G.fbx using Guid(757801213e453204c9e114ac173a6c34) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ace7c0bf450746dca36c9423a630585') in 0.0499781 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Purple.fbx
  artifactKey: Guid(0c0334b5ac4367e47b83b5f86dc10309) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Purple.fbx using Guid(0c0334b5ac4367e47b83b5f86dc10309) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6f840d041673cdf749cbde02621771e') in 0.0342169 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_A.prefab
  artifactKey: Guid(a4868ae2d09ee9d48b587c9bb55d8936) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_A.prefab using Guid(a4868ae2d09ee9d48b587c9bb55d8936) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3eab2f9c784e2ad1a3ae88b5cd5964f2') in 0.0328884 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000110 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_D.prefab
  artifactKey: Guid(f1baa36c277a622458a42df58ba56aeb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_D.prefab using Guid(f1baa36c277a622458a42df58ba56aeb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b91cc636a30f1cfce7b38967521c245c') in 0.0297732 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Part.fbx
  artifactKey: Guid(c49b8df1f90d3294d8a2d1d9827040fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Part.fbx using Guid(c49b8df1f90d3294d8a2d1d9827040fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd52eaaf57d294fda3f2f46fa39a14a3c') in 0.0554058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_J.prefab
  artifactKey: Guid(3084aa3d03177a944833dfa6ca85c263) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_J.prefab using Guid(3084aa3d03177a944833dfa6ca85c263) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2dd64c1f82a51fa5306f0a156d354914') in 0.0290483 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Sword_G.fbx
  artifactKey: Guid(ed1d7f38a0d7c834790ffd54d3654514) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Sword_G.fbx using Guid(ed1d7f38a0d7c834790ffd54d3654514) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0f1d40c585c2f4a2b6a05904d13f5437') in 0.0437945 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_D.fbx
  artifactKey: Guid(c92afdbe659e9204f86e822fff9ec1b5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_D.fbx using Guid(c92afdbe659e9204f86e822fff9ec1b5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'def9393cadfcf3c81344032722f581f6') in 0.0433373 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Sword_E.fbx
  artifactKey: Guid(95380d84383300b4d9e35047daf41e04) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Sword_E.fbx using Guid(95380d84383300b4d9e35047daf41e04) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb98eb493d2c6d306e2d27948abb4a35') in 0.0366161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_C.fbx
  artifactKey: Guid(735c2c6bd7a310143a7feae085142a12) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_C.fbx using Guid(735c2c6bd7a310143a7feae085142a12) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0aa743219ce958925f8707240ac6ec79') in 0.0576837 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_Part_B.fbx
  artifactKey: Guid(1acf7f189b3254847b5aeb63952516e3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_Part_B.fbx using Guid(1acf7f189b3254847b5aeb63952516e3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08ed39d5b587cb4ff02a9cd9a545fb71') in 0.0424325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_L.prefab
  artifactKey: Guid(f389977473f85ac47bdb277cca621df6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_L.prefab using Guid(f389977473f85ac47bdb277cca621df6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c995bf0c315aab406252e2fec4748189') in 0.0304678 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Script/Economy/ShopManager.cs
  artifactKey: Guid(e580445b77687ad45b8d3d7c88dbc298) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/ShopManager.cs using Guid(e580445b77687ad45b8d3d7c88dbc298) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f75399a3e9aaa07ae2fe74cbeca3854a') in 0.0247694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

Editor requested this worker to shutdown with reason: balancing worker count downwards
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0