Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker11
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker11.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [14648]  Target information:

Player connection [14648]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1591775256 [EditorId] 1591775256 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [14648] Host joined multi-casting on [***********:54997]...
Player connection [14648] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56556
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003706 seconds.
- Loaded All Assemblies, in  1.060 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.936 seconds
Domain Reload Profiling: 1995ms
	BeginReloadAssembly (394ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (106ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (123ms)
	LoadAllAssembliesAndSetupDomain (401ms)
		LoadAssemblies (393ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (393ms)
			TypeCache.Refresh (389ms)
				TypeCache.ScanAssembly (348ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (2ms)
	FinalizeReload (937ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (821ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (61ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (258ms)
			ProcessInitializeOnLoadAttributes (373ms)
			ProcessInitializeOnLoadMethodAttributes (115ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.223 seconds
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.724 seconds
Domain Reload Profiling: 3945ms
	BeginReloadAssembly (318ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (80ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (93ms)
	LoadAllAssembliesAndSetupDomain (1698ms)
		LoadAssemblies (1092ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (751ms)
			TypeCache.Refresh (633ms)
				TypeCache.ScanAssembly (597ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1725ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1292ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (369ms)
			ProcessInitializeOnLoadAttributes (774ms)
			ProcessInitializeOnLoadMethodAttributes (135ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.17 seconds
Refreshing native plugins compatible for Editor in 1.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (10.1 MB). Loaded Objects now: 7294.
Memory consumption went from 163.4 MB to 153.2 MB.
Total: 223.031600 ms (FindLiveObjects: 7.383600 ms CreateObjectMapping: 8.491000 ms MarkObjects: 12.109500 ms  DeleteObjects: 195.044600 ms)

========================================================================
Received Import Request.
  Time since last request: 103330.789789 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Banana_Green.fbx
  artifactKey: Guid(06f2c75804b9a484b9febcce4fffebfc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Banana_Green.fbx using Guid(06f2c75804b9a484b9febcce4fffebfc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f4105b1e5cb49274dd80347ffae0068') in 1.4127771 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_C.prefab
  artifactKey: Guid(651b26b508213d9468d9dfec888ef996) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_C.prefab using Guid(651b26b508213d9468d9dfec888ef996) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '77634f2a0509ed192ffdcc050cd6d815') in 0.0309813 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_C.prefab
  artifactKey: Guid(1e0d4844a1a345543afac4d0fa3f41f3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_C.prefab using Guid(1e0d4844a1a345543afac4d0fa3f41f3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '150a1d9bd6e108c1f4bfcc1ee3b7c7b7') in 0.0529891 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Script/Player/ExampleInteractable.cs
  artifactKey: Guid(28530b75c0be6e64e9fd216423d077f0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/ExampleInteractable.cs using Guid(28530b75c0be6e64e9fd216423d077f0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0cc13d0c96a6c0dc555e47bcb4d76c9') in 0.0233119 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Script/Economy/CurrencyManager.cs
  artifactKey: Guid(57117511543445b46a899dd0674c1f2d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/CurrencyManager.cs using Guid(57117511543445b46a899dd0674c1f2d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08c08f914a396560ee9cd99133b8b2a0') in 0.0262058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Settings/SampleSceneProfile.asset
  artifactKey: Guid(10fc4df2da32a41aaa32d77bc913491c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/SampleSceneProfile.asset using Guid(10fc4df2da32a41aaa32d77bc913491c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9e7cd619ce1775e4bfb8a72781bddaee') in 0.0248001 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_A.prefab
  artifactKey: Guid(3395bd05b77e85f46a98444f3829a3c3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_A.prefab using Guid(3395bd05b77e85f46a98444f3829a3c3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2f32b490f5efe5b7d0f88bdf428e6af4') in 0.0213265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_C.prefab
  artifactKey: Guid(67b30c1f1afb35e40ad6a1dffecc9cf5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_C.prefab using Guid(67b30c1f1afb35e40ad6a1dffecc9cf5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8fbd4e0fdbcefd0310d857c48b46ede5') in 0.0297584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPen_A.prefab
  artifactKey: Guid(0dc4e89f48b296041ae8cd964876c5f7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPen_A.prefab using Guid(0dc4e89f48b296041ae8cd964876c5f7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f6ad3dda905fbcd0f798e0ff70c52bd4') in 0.0311178 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_B.prefab
  artifactKey: Guid(2155bbf3a7539524b9c0ed2418191700) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_B.prefab using Guid(2155bbf3a7539524b9c0ed2418191700) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e71fdc7e1e5e9f6758644348b4665503') in 0.021472 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval PackBundle/Builtin/Built_in.unitypackage
  artifactKey: Guid(a972b1a94ffee314889ae043a3511b68) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval PackBundle/Builtin/Built_in.unitypackage using Guid(a972b1a94ffee314889ae043a3511b68) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1ed2f585f62c94eda1f5a47d3ef515c5') in 0.0196751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_B.prefab
  artifactKey: Guid(9d89a73f46939a04e94c7e39cea0a222) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_B.prefab using Guid(9d89a73f46939a04e94c7e39cea0a222) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6ad9bfb43a3542c3da083f244ad1e16d') in 0.0229124 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Beige_A.prefab
  artifactKey: Guid(aa5bb21f88590ba46a484e56d4138d8b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Beige_A.prefab using Guid(aa5bb21f88590ba46a484e56d4138d8b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c274457e523bfadf89288c9bd4314f04') in 0.0190826 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Script/Player/Editor/PlayerControllerEditor.cs
  artifactKey: Guid(e5a74fda470bd3f458a7876ec983dcce) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Editor/PlayerControllerEditor.cs using Guid(e5a74fda470bd3f458a7876ec983dcce) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f12d883884291c72dfdb49049917818') in 0.022514 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Greenonion.prefab
  artifactKey: Guid(c7bfd9ceee8c4834d9f54579a258387d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Greenonion.prefab using Guid(c7bfd9ceee8c4834d9f54579a258387d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '08d5914ffabb28665a4fc0cc08319fac') in 0.0202882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_G.prefab
  artifactKey: Guid(59e963992a88cc24f86f19271e37699c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_G.prefab using Guid(59e963992a88cc24f86f19271e37699c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4b0579d4bf385ffcd4b70282954fb960') in 0.0245911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_F.prefab
  artifactKey: Guid(86b55ba9212ca2b4eb59052bb32e5b69) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_F.prefab using Guid(86b55ba9212ca2b4eb59052bb32e5b69) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a88c8545c7850d4f0501c6bd4e68695c') in 0.0314381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_F.prefab
  artifactKey: Guid(2baf982870f80d1459180344178b8200) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_F.prefab using Guid(2baf982870f80d1459180344178b8200) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd24f909d2b0e2a5ebd905f66408994ee') in 0.0265462 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_C.prefab
  artifactKey: Guid(cbc7a7d2caa56a54b89502dd88aff94f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_C.prefab using Guid(cbc7a7d2caa56a54b89502dd88aff94f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bf792fdc89f0fbe474456193694167a2') in 0.0234707 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Beerglass_A.fbx
  artifactKey: Guid(f376089feab333c49b156e7b21056d87) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Beerglass_A.fbx using Guid(f376089feab333c49b156e7b21056d87) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75c9bf3c33da320f12f638a4686a545b') in 0.040975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000133 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_F.prefab
  artifactKey: Guid(154cd0487252bb747b3bd0544c1fb578) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_F.prefab using Guid(154cd0487252bb747b3bd0544c1fb578) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ebd74d12c3fb223f08cf270d4bf6a13c') in 0.0263661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_S.prefab
  artifactKey: Guid(c9c44da634456964781530eb46dcb2a7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_S.prefab using Guid(c9c44da634456964781530eb46dcb2a7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0335f7f4b69f3e82cf3c873b55c69550') in 0.0227334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_A.prefab
  artifactKey: Guid(461d999f8325970499548ad6ee9df31a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_A.prefab using Guid(461d999f8325970499548ad6ee9df31a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '118dc5f07288919bba8ed2086ab6f6fb') in 0.0273479 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_L.prefab
  artifactKey: Guid(57e26dc055d854842834c89cc79396e9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_L.prefab using Guid(57e26dc055d854842834c89cc79396e9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b7ccdb4a480a01c0f7d2d63cfec4576c') in 0.0246234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000242 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_A.fbx
  artifactKey: Guid(7c79b1667601d214693cc9fdd42830a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_A.fbx using Guid(7c79b1667601d214693cc9fdd42830a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f7b287c87599b15361ee8096b2405dc9') in 0.0463715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_D.prefab
  artifactKey: Guid(8b7f139a58600f24b9db121e1e24cca9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_D.prefab using Guid(8b7f139a58600f24b9db121e1e24cca9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '34becd329d6569a830247bcfaece34f8') in 0.020886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_A.prefab
  artifactKey: Guid(71d209b3084dd6e4287075fe2febe5f7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_A.prefab using Guid(71d209b3084dd6e4287075fe2febe5f7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dbde406662ddb78a977b2038af78f5fc') in 0.0258204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_E.prefab
  artifactKey: Guid(0808151e53985f041b031a43691eb13d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_E.prefab using Guid(0808151e53985f041b031a43691eb13d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '62d6550c846f8789c8f9760fd23068cc') in 0.0211113 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_C.prefab
  artifactKey: Guid(a98411c3a1e8f34448aad79e6dea5672) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_C.prefab using Guid(a98411c3a1e8f34448aad79e6dea5672) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2d3d1749a76721feb221ed302a4933ff') in 0.0235713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Script/Player/Guides/KHAC_PHUC_LOI_CAMERA.md
  artifactKey: Guid(d264c91609f4683458d7aa4ac5cc03d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/KHAC_PHUC_LOI_CAMERA.md using Guid(d264c91609f4683458d7aa4ac5cc03d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f78fb887ea180c06be41f523b6d85bcc') in 0.0335422 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Sword_I.fbx
  artifactKey: Guid(7d2cff1ca40112f49bb24481f1826ea4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Sword_I.fbx using Guid(7d2cff1ca40112f49bb24481f1826ea4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '97e6b1dd977bfb3deafc079a72da1ea7') in 0.0474272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_G.prefab
  artifactKey: Guid(aa28a0cab50867643992a10b3bdef969) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_G.prefab using Guid(aa28a0cab50867643992a10b3bdef969) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f72d90ca0ae83e230b4c58cde54aafa') in 0.0277158 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_C.prefab
  artifactKey: Guid(3108a1d0d725bf94a8b98efb4a9c57e1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_C.prefab using Guid(3108a1d0d725bf94a8b98efb4a9c57e1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '639a7c6b4c2f4229b2f35841825507ca') in 0.0771902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_B.prefab
  artifactKey: Guid(c96bd2ed91e807742a59c05730874dcb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_B.prefab using Guid(c96bd2ed91e807742a59c05730874dcb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '35adfc90c33d2ad703e956078bf77bcb') in 0.0264843 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Red.prefab
  artifactKey: Guid(b638312a21cccc0489a61c5cd10e8c97) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Red.prefab using Guid(b638312a21cccc0489a61c5cd10e8c97) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f7db60dcd504d447204a6c3c168c28fb') in 0.0287746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_I.prefab
  artifactKey: Guid(234540db91fa10f46b69eae3d087b0f1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_I.prefab using Guid(234540db91fa10f46b69eae3d087b0f1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da8ab1143a18bf932cfd47adc7bc7082') in 0.0386432 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_D.prefab
  artifactKey: Guid(e1691e6940e3f2046b81cbad79e7ad78) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_D.prefab using Guid(e1691e6940e3f2046b81cbad79e7ad78) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c793ea5c7e5430aa6320376722992d55') in 0.0231644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BalanceScale.fbx
  artifactKey: Guid(6f87d981a640ab04291336925a1d9fb0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BalanceScale.fbx using Guid(6f87d981a640ab04291336925a1d9fb0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75887b9445bbd6ccd7b212ce0aca272c') in 0.0489678 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Table_D.fbx
  artifactKey: Guid(21419c2533d986f43b4f2f807c82e3cb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Table_D.fbx using Guid(21419c2533d986f43b4f2f807c82e3cb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '24645c03f673cc5f481d1b8d33ccc095') in 0.0518851 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_C.prefab
  artifactKey: Guid(5683d73783d5565459e3e61d76bca99f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_C.prefab using Guid(5683d73783d5565459e3e61d76bca99f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8da1aa485cbe7b036dc48744bb297182') in 0.1587796 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_E.prefab
  artifactKey: Guid(aa283037a16d54a459b4f80e8ce60c07) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_E.prefab using Guid(aa283037a16d54a459b4f80e8ce60c07) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '53cb4b54a8ee98d1573423bd7a5e57c1') in 0.0297927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Purple.prefab
  artifactKey: Guid(71857e1c4631ccf4eb79dad627b809a3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Purple.prefab using Guid(71857e1c4631ccf4eb79dad627b809a3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f9119ec5ab1b6818c33b5f818f45c034') in 0.0367785 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_G.prefab
  artifactKey: Guid(a7caa8247ab4f7c4abb0a592f3164ee0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_G.prefab using Guid(a7caa8247ab4f7c4abb0a592f3164ee0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e33e53c535f0da9f5f6c10b365e1045a') in 0.0335115 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_M.prefab
  artifactKey: Guid(f9667894b9766334d8ea630f8fec0c15) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_M.prefab using Guid(f9667894b9766334d8ea630f8fec0c15) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '14f4b3ed7ceb9db3a9d0536148aa1d15') in 0.0635941 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPen_A.prefab
  artifactKey: Guid(0dc4e89f48b296041ae8cd964876c5f7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPen_A.prefab using Guid(0dc4e89f48b296041ae8cd964876c5f7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '092a5bd928156f4356e15b40b34037b1') in 0.0375238 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_C.prefab
  artifactKey: Guid(d2c04a7157d558d48822653e4f60f1e3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_C.prefab using Guid(d2c04a7157d558d48822653e4f60f1e3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0a4f16caf272f240d9a9d3016c4f3707') in 0.0377168 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_E.prefab
  artifactKey: Guid(f3bd087e894a5124c98c66a99ed4fc5b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_E.prefab using Guid(f3bd087e894a5124c98c66a99ed4fc5b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cf122a59636a1680e0e06743f10093ca') in 0.0353982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_B.prefab
  artifactKey: Guid(7ae78b65dda913b46935796ea11ff6fd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_B.prefab using Guid(7ae78b65dda913b46935796ea11ff6fd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac17ec2e3919ce742e963e3e8712cf1e') in 0.0304504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Water_Tile_A.prefab
  artifactKey: Guid(70cfaf618292689458d57d1cf8880360) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Water_Tile_A.prefab using Guid(70cfaf618292689458d57d1cf8880360) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ea2fcaa2659fa7b2c6f22e21bb6c8dd3') in 0.0326854 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_A.fbx
  artifactKey: Guid(fe32624dc38105a4fb6248153eb2dacd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_A.fbx using Guid(fe32624dc38105a4fb6248153eb2dacd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '133a60a1a20409cd4cf5779334058657') in 0.044617 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_B.fbx
  artifactKey: Guid(040b839c544d1a142b229ba364530a24) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_B.fbx using Guid(040b839c544d1a142b229ba364530a24) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '836093d97131d0e8a7d057575741b6f2') in 0.0391924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Egg_A.prefab
  artifactKey: Guid(9cc65b1ba4e6c7a449366ea7cb87687c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Egg_A.prefab using Guid(9cc65b1ba4e6c7a449366ea7cb87687c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06bed627c7f17f5eb32d11dcd6581ab4') in 0.0318688 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000417 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_K.prefab
  artifactKey: Guid(3a05cd324fd089f438cabeea4b17e363) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_K.prefab using Guid(3a05cd324fd089f438cabeea4b17e363) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd9c82cb9c0bda8aed4c83bec077f8977') in 0.0267524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Ladder.prefab
  artifactKey: Guid(733c1c3299e20b140a05befdccea196b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Ladder.prefab using Guid(733c1c3299e20b140a05befdccea196b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6119a83e64770c71c4ee7368e66536e7') in 0.032144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Script/Player/MATERIAL_PROPERTIES_GUIDE.md
  artifactKey: Guid(e32e3da619483d24bba3abc9605e8df6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/MATERIAL_PROPERTIES_GUIDE.md using Guid(e32e3da619483d24bba3abc9605e8df6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '851e73615b33a3a411194c0c93b10b90') in 0.0835596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_B.fbx
  artifactKey: Guid(0b18059d6f0ba6049a18a0969e87ac8d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_B.fbx using Guid(0b18059d6f0ba6049a18a0969e87ac8d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e57cba2b3a034b16285fc1bc14132584') in 0.041995 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stool_B.fbx
  artifactKey: Guid(8b754bde524dbf64ab5b4f6d17268696) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stool_B.fbx using Guid(8b754bde524dbf64ab5b4f6d17268696) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '56c6e96041d70343af8637b5c30eeb74') in 0.050919 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Cook_C.fbx
  artifactKey: Guid(583c4b261616ba844a17fcaf596fb64c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Cook_C.fbx using Guid(583c4b261616ba844a17fcaf596fb64c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6bb56ec4a8c46fcec34b1e88f8d1e70e') in 0.0386562 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000114 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_A.prefab
  artifactKey: Guid(a08671c32d49c7e4fb578bf543499f1a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_A.prefab using Guid(a08671c32d49c7e4fb578bf543499f1a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '094b478ecc01480ec20b73761cd9ecdd') in 0.0334525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bread_C.fbx
  artifactKey: Guid(bccf6ca5b949ec84da7767fb95fff2de) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bread_C.fbx using Guid(bccf6ca5b949ec84da7767fb95fff2de) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '67876ec93ffc46f207b7513c5bb5ab74') in 0.0350705 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_F.prefab
  artifactKey: Guid(8f95da4b0436b8f40a0f18e1b3c6da1e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_F.prefab using Guid(8f95da4b0436b8f40a0f18e1b3c6da1e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '831b90d99cdfafd3b9c7a1a470fa1fee') in 0.0255189 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Green.prefab
  artifactKey: Guid(4e4609e3409ee2343b6e3a14b246a014) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Green.prefab using Guid(4e4609e3409ee2343b6e3a14b246a014) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '01997fc799b1c356a5182a45daa141ee') in 0.0200898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Stable_B.fbx
  artifactKey: Guid(b2d07b52bf593d846ab63c0c2b13d14d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Stable_B.fbx using Guid(b2d07b52bf593d846ab63c0c2b13d14d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f88134c0b1c43d0807dbb92db6fdbf3a') in 0.0556902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_B.prefab
  artifactKey: Guid(1c4ce969a352d2044b8e2ffedbaf2e0a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_B.prefab using Guid(1c4ce969a352d2044b8e2ffedbaf2e0a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2372724127a4af1d537cc24171c6f6f4') in 0.0340086 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_C.fbx
  artifactKey: Guid(f8b044e54b6a9d14f9829ffff1059506) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_C.fbx using Guid(f8b044e54b6a9d14f9829ffff1059506) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '21f907e7d66edbe59888081384ba5524') in 0.0361498 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_A.prefab
  artifactKey: Guid(4d4bdfb155e4ac844bc527e8a3abfe7e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_A.prefab using Guid(4d4bdfb155e4ac844bc527e8a3abfe7e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '616758cf707bf6f9dd1d365c32d53de4') in 0.0289955 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_H.fbx
  artifactKey: Guid(f64df66787867ed43bc86f0610ccfa03) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_H.fbx using Guid(f64df66787867ed43bc86f0610ccfa03) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3375d961cf64d0e99a0f1e6cfd976073') in 0.0327708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_D.prefab
  artifactKey: Guid(13f95c4c097bbd34fad762466b949f86) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_D.prefab using Guid(13f95c4c097bbd34fad762466b949f86) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e57e9c3411e985ff26f4af5922c02fd6') in 0.029681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_D.fbx
  artifactKey: Guid(849f9ebc3c2de5b45881e62e69c15467) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_D.fbx using Guid(849f9ebc3c2de5b45881e62e69c15467) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1dec05e0b27a2cdf94275b3f138767f') in 0.0542747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_E.prefab
  artifactKey: Guid(c26fcd64f2447c348a6fedc42010b8b0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_E.prefab using Guid(c26fcd64f2447c348a6fedc42010b8b0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '315af92ac9695ac3827b9484bc528d27') in 0.0266832 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_A.prefab
  artifactKey: Guid(6b8661102e852804eb79234ad68eef57) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_A.prefab using Guid(6b8661102e852804eb79234ad68eef57) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '88ad6a00af31d851bf54969b2ddd3d05') in 0.028619 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_E.prefab
  artifactKey: Guid(b7a3ba7d5f5c26e4faa853f8fa2a55af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_E.prefab using Guid(b7a3ba7d5f5c26e4faa853f8fa2a55af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '98e07a935336c683854106b65797fb4e') in 0.0300492 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Flower_D.fbx
  artifactKey: Guid(b81eeb78854081d4f856778fc06766ba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Flower_D.fbx using Guid(b81eeb78854081d4f856778fc06766ba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aff634196ebb9cb8ab590e442fb33cc9') in 0.0451996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ground_Tile_E.fbx
  artifactKey: Guid(ff69ed672b71bdf44adc352b165d315f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ground_Tile_E.fbx using Guid(ff69ed672b71bdf44adc352b165d315f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '88aa72b3caa2bb5820afd636d7298638') in 0.039072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Grey.fbx
  artifactKey: Guid(fa22f6dba3c8a774e83498dcea02c2be) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Grey.fbx using Guid(fa22f6dba3c8a774e83498dcea02c2be) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc63a7b7cd0ce06ae063a3a0008a8e33') in 0.0432512 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_H.prefab
  artifactKey: Guid(14166236bef07bc438774648e556a90a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_H.prefab using Guid(14166236bef07bc438774648e556a90a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06d1e0110f7172c2b395d72abd9065a3') in 0.0249262 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_C.prefab
  artifactKey: Guid(9ba92e76496cfab439d07161152fb64b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_C.prefab using Guid(9ba92e76496cfab439d07161152fb64b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d0f5b319b4178ffc951684e7916b44b') in 0.0224597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_SweetPotato.fbx
  artifactKey: Guid(717f42c5999f32b4890dcbcba02e2769) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_SweetPotato.fbx using Guid(717f42c5999f32b4890dcbcba02e2769) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83966e8fbda85bfebaa4a82de55c1c15') in 0.0356571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_F.fbx
  artifactKey: Guid(f44e82f3fcccef84686d01bb4dda883e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_F.fbx using Guid(f44e82f3fcccef84686d01bb4dda883e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f395f990b58eda1400c3100145c33a4e') in 0.0349872 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_A.prefab
  artifactKey: Guid(a3117c614e1b8054d931b375b4322771) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_A.prefab using Guid(a3117c614e1b8054d931b375b4322771) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f694f894374db3765fa764943f89630') in 0.0274731 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_R.fbx
  artifactKey: Guid(a1fcca29d6c7dca44941453e9f788292) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_R.fbx using Guid(a1fcca29d6c7dca44941453e9f788292) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7ac0dfe930517df5aa29670f4a95a3c') in 0.0436224 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_B.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_B.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e479f58177db35d682ba0740ce3bf4ec') in 0.0286907 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_E.prefab
  artifactKey: Guid(36f8be23c78f8f44ab821740640e6f74) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_E.prefab using Guid(36f8be23c78f8f44ab821740640e6f74) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a47aaa30ff020674a939c8586b70541') in 0.0341423 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Flower_B.fbx
  artifactKey: Guid(cd462e80bc858b94ebb5a15656fb360c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Flower_B.fbx using Guid(cd462e80bc858b94ebb5a15656fb360c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ea4a0b5c6ac592229a51474ef8ecd8df') in 0.0318755 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_B.fbx
  artifactKey: Guid(dd0881d57b82b564cbfe32f7aa6594d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_B.fbx using Guid(dd0881d57b82b564cbfe32f7aa6594d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c4185c4e8495050fbcfa31ffe5785d6') in 0.040206 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_H.fbx
  artifactKey: Guid(5e2b2d3e81308bf4199cc19c360468ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_H.fbx using Guid(5e2b2d3e81308bf4199cc19c360468ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b28e0d0beb878fb9eaf0b53935e92d70') in 0.0404248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_White.fbx
  artifactKey: Guid(ee1aa63e0811ffb4199f5c3aded66f90) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_White.fbx using Guid(ee1aa63e0811ffb4199f5c3aded66f90) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '54374a7e6a00058c310c2327b6ce0767') in 0.035259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_C.fbx
  artifactKey: Guid(6ce33695d2ae88546a8624a84f3cd273) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_C.fbx using Guid(6ce33695d2ae88546a8624a84f3cd273) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3c92c7afff6cdef399356385da5b4769') in 0.0372305 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Parchment_A.fbx
  artifactKey: Guid(8dd13807db2f2e142a325ff26d15f59c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Parchment_A.fbx using Guid(8dd13807db2f2e142a325ff26d15f59c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75e6429ded91ac17bba7d4368cfeeab7') in 0.0354764 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_K.prefab
  artifactKey: Guid(fd5ffb1771de0c546b7d4b16aa257b6b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_K.prefab using Guid(fd5ffb1771de0c546b7d4b16aa257b6b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '314cad479a6cb0eadd6b91f9e481d228') in 0.0304372 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_F.fbx
  artifactKey: Guid(2ef470817e217184c8f7d16fc2e6879a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_F.fbx using Guid(2ef470817e217184c8f7d16fc2e6879a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b53037b969d6b01293ebb8493fc14b09') in 0.0369056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_A.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_A.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '04c6f4f5398f6e05b647b5ce108cf5cf') in 0.0705337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bush_C.fbx
  artifactKey: Guid(33669b851d99b144f889b1c164646725) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bush_C.fbx using Guid(33669b851d99b144f889b1c164646725) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48c0d9c1d1e03ed5170e1464cee316f4') in 0.0580061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_E.prefab
  artifactKey: Guid(a571e66f399af384491d66ffa1936b03) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_E.prefab using Guid(a571e66f399af384491d66ffa1936b03) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '082aea5b703128cd8585903c1cfe27db') in 0.0366131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_F.fbx
  artifactKey: Guid(fe11e00922c95b846b077b5ef006a9e8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_F.fbx using Guid(fe11e00922c95b846b077b5ef006a9e8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aaf3d99a293fe21563d36844bc1ac7ae') in 0.0431745 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Steak.fbx
  artifactKey: Guid(1519f1154e2197f47b85b1df69e00102) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Steak.fbx using Guid(1519f1154e2197f47b85b1df69e00102) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b5cab3bbdde0eedec95a59d72bb0e43b') in 0.0337574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_A.prefab
  artifactKey: Guid(863e2c8d99beb2f45b926f70b34a592d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_A.prefab using Guid(863e2c8d99beb2f45b926f70b34a592d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9eef2e71d55a6e0cc24b6d6f1eb8f15') in 0.0307183 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000122 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bed_B.prefab
  artifactKey: Guid(0efb1c693ee53df4e807dc6a5f1039f5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bed_B.prefab using Guid(0efb1c693ee53df4e807dc6a5f1039f5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d05df06c0d532257939daf1c7218eec') in 0.0348173 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_C.prefab
  artifactKey: Guid(4f818683eba502c4e922739fc8d32cdd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_C.prefab using Guid(4f818683eba502c4e922739fc8d32cdd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c61ad82c257053e71f49eb80200d20b') in 0.0491891 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Red.prefab
  artifactKey: Guid(8140d14ca68b665428789770f9994865) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Red.prefab using Guid(8140d14ca68b665428789770f9994865) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89c97b3ecb48cd74c6499ea5a1aae33c') in 0.0369763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000179 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Beige_B.fbx
  artifactKey: Guid(838d34a43035df342a3dd4b6ba1d406c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Beige_B.fbx using Guid(838d34a43035df342a3dd4b6ba1d406c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd560ddf55b65df77a5c7258cea20cf42') in 0.0419016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_B.prefab
  artifactKey: Guid(34985651cffcb2f46a53e181d2ccd412) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_B.prefab using Guid(34985651cffcb2f46a53e181d2ccd412) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e5570c34981d005ad8aab62e0f12ef5a') in 0.0377518 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_C.fbx
  artifactKey: Guid(987117fcffe9d7d409859ca8af8e578e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_C.fbx using Guid(987117fcffe9d7d409859ca8af8e578e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6201fe909a2aef70a5a12750ba553a62') in 0.0436029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cucumber.prefab
  artifactKey: Guid(9241f67c97e0af24fbc5d59db57e8dcb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cucumber.prefab using Guid(9241f67c97e0af24fbc5d59db57e8dcb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5fa9938d0ceead69fdb17065de5d0cd4') in 0.0330361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_K.fbx
  artifactKey: Guid(3db269844b4e102458ab646cdca49d2f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_K.fbx using Guid(3db269844b4e102458ab646cdca49d2f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06185240b5bd684890a9adaf6505be1d') in 0.049046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_F.fbx
  artifactKey: Guid(287dee4d15dfbd14791400d20dffc86a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_F.fbx using Guid(287dee4d15dfbd14791400d20dffc86a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6c4938faa206fb53cec5bc980ec23f3') in 0.0378763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Steel_B.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Steel_B.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34c403aa926ae98c253317f12a2a885a') in 0.0452019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_C.fbx
  artifactKey: Guid(8f29b8184594a644797a5a8463fb3d81) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_C.fbx using Guid(8f29b8184594a644797a5a8463fb3d81) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c9d4c4e319ee9616eda0eaa48c18561') in 0.0453888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_E.prefab
  artifactKey: Guid(c44adc5c019f1ec4ba9bfc6e0e7ee88c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_E.prefab using Guid(c44adc5c019f1ec4ba9bfc6e0e7ee88c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb75fbbd255912f03d6538edd0e770c1') in 0.0407587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pumpkin_B.fbx
  artifactKey: Guid(56a447eacc8a3844a8d08387ffe05d7d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pumpkin_B.fbx using Guid(56a447eacc8a3844a8d08387ffe05d7d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '38f557a1a785202f4c9b8b4cbe9f19e1') in 0.0401272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Steel_C.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Steel_C.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45b1ef81c0f1ad64d32913bb091a4d69') in 0.0385263 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_A.prefab
  artifactKey: Guid(274833ec1e44e964caa1cdd7aa8f7f6f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_A.prefab using Guid(274833ec1e44e964caa1cdd7aa8f7f6f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fd5a9530aa28b1aa6c80295101ab3eab') in 0.0286127 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_C.prefab
  artifactKey: Guid(69d4c9b575c6f2448a8e49159482982b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_C.prefab using Guid(69d4c9b575c6f2448a8e49159482982b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '29824845f3cc98a9f06a41f45f15b65a') in 0.0242101 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_B.prefab
  artifactKey: Guid(7672131665f1e6148a721f4c88fd5eac) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_B.prefab using Guid(7672131665f1e6148a721f4c88fd5eac) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1f152c6e6ae1a634b413ed482c09f56a') in 0.0347616 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_B.prefab
  artifactKey: Guid(f6f29766e26bc8a408696de615527d83) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_B.prefab using Guid(f6f29766e26bc8a408696de615527d83) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '16a236f1fc1a6eab1f2fb1fbe11cf816') in 0.0244115 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

Editor requested this worker to shutdown with reason: balancing worker count downwards
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0