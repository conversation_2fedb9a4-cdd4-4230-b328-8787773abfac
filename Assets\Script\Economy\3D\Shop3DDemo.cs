using UnityEngine;
using UnityEngine.UI;
using TMPro;
using EconomySystem;

namespace EconomySystem.Shop3D
{
    /// <summary>
    /// Demo script cho hệ thống cửa hàng 3D
    /// Tạo môi trường test và hướng dẫn sử dụng
    /// </summary>
    public class Shop3DDemo : MonoBehaviour
    {
        [Header("Demo Settings")]
        [SerializeField] private bool autoSetupDemo = true;
        [SerializeField] private bool createTestEnvironment = true;
        [SerializeField] private bool spawnTestItems = true;
        [SerializeField] private bool showInstructions = true;

        [Header("Test Environment")]
        [SerializeField] private GameObject playerPrefab;
        [SerializeField] private GameObject itemDisplayPrefab;
        [SerializeField] private Material[] testMaterials;
        [SerializeField] private int numberOfShelves = 4;
        [SerializeField] private int itemsPerShelf = 3;

        [Header("Demo UI")]
        [SerializeField] private GameObject instructionPanel;
        [SerializeField] private TextMeshProUGUI instructionText;
        [SerializeField] private Button nextStepButton;
        [SerializeField] private Button skipDemoButton;

        [Header("Demo Data")]
        [SerializeField] private ItemDatabase testItemDatabase;

        // Private variables
        private int currentDemoStep = 0;
        private GameObject demoPlayer;
        private ShopEnvironment shopEnvironment;
        private string[] demoSteps = {
            "Chào mừng đến với Demo Cửa hàng 3D!\n\nHệ thống này tích hợp hoàn toàn với Economy System hiện có.",
            "Sử dụng WASD để di chuyển, Mouse để nhìn xung quanh.\nShift để chạy, Space để nhảy.",
            "Nhìn vào các vật phẩm trên kệ để thấy hiệu ứng highlight.\nCác vật phẩm sẽ có outline màu vàng.",
            "Nhấn E khi nhìn vào vật phẩm để mở thông tin chi tiết.\nBạn có thể xem giá cả và mô tả.",
            "Trong popup, chọn số lượng và nhấn 'Mua' để mua vật phẩm.\nSố tiền Lea sẽ được trừ tự động.",
            "Nhấn Tab để mở Shop Menu tổng quan.\nEsc để đóng các UI panels.",
            "Thử bán vật phẩm bằng cách nhấn 'Bán' trong popup.\nBạn sẽ nhận được tiền Lea.",
            "Demo hoàn tất! Khám phá thêm các tính năng khác.\nV để chuyển đổi First/Third Person."
        };

        #region Unity Methods
        private void Start()
        {
            if (autoSetupDemo)
            {
                SetupDemo();
            }
        }

        private void Update()
        {
            HandleDemoInput();
        }
        #endregion

        #region Demo Setup
        private void SetupDemo()
        {
            Debug.Log("🎮 Bắt đầu setup Demo Cửa hàng 3D...");

            // Setup Economy System trước
            SetupEconomySystem();

            // Tạo test environment
            if (createTestEnvironment)
            {
                CreateTestEnvironment();
            }

            // Spawn test items
            if (spawnTestItems)
            {
                SpawnTestItems();
            }

            // Setup player
            SetupDemoPlayer();

            // Setup UI
            SetupDemoUI();

            Debug.Log("✅ Demo setup hoàn tất!");
        }

        private void SetupEconomySystem()
        {
            // Tìm hoặc tạo EconomySystemSetup
            var economySetup = FindFirstObjectByType<EconomySystemSetup>();
            if (economySetup == null)
            {
                GameObject setupObj = new GameObject("EconomySystemSetup");
                economySetup = setupObj.AddComponent<EconomySystemSetup>();
            }

            // Tạo test ItemDatabase nếu chưa có
            if (testItemDatabase == null)
            {
                CreateTestItemDatabase();
            }

            // Gán database và khởi tạo
            if (testItemDatabase != null)
            {
                var setupType = typeof(EconomySystemSetup);
                var databaseField = setupType.GetField("itemDatabase", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                databaseField?.SetValue(economySetup, testItemDatabase);

                economySetup.KhoiTaoHeThong();
            }
        }

        private void CreateTestItemDatabase()
        {
            testItemDatabase = ScriptableObject.CreateInstance<ItemDatabase>();
            
            // Tạo test items
            var testItems = new Item[]
            {
                new Item("sword", "Kiếm Thép", "Vũ khí cận chiến mạnh mẽ", 150, 75),
                new Item("shield", "Khiên Gỗ", "Giáp phòng thủ cơ bản", 100, 50),
                new Item("potion", "Thuốc Hồi Máu", "Hồi phục 50 HP", 25, 12),
                new Item("bread", "Bánh Mì", "Thức ăn cơ bản", 5, 2),
                new Item("gem", "Ngọc Quý", "Vật phẩm quý giá", 500, 250),
                new Item("wood", "Gỗ", "Vật liệu xây dựng", 10, 5),
                new Item("iron", "Sắt", "Kim loại cứng", 30, 15),
                new Item("gold", "Vàng", "Kim loại quý", 100, 50)
            };

            foreach (var item in testItems)
            {
                testItemDatabase.ThemVatPham(item);
            }

            Debug.Log($"Đã tạo test ItemDatabase với {testItems.Length} vật phẩm");
        }

        private void CreateTestEnvironment()
        {
            // Tạo ground
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "Ground";
            ground.transform.localScale = new Vector3(5, 1, 5);

            // Tạo walls
            CreateWalls();

            // Tạo shelves
            CreateTestShelves();

            // Tạo lighting
            CreateTestLighting();

            // Tạo ShopEnvironment component
            GameObject envObj = new GameObject("ShopEnvironment");
            shopEnvironment = envObj.AddComponent<ShopEnvironment>();
        }

        private void CreateWalls()
        {
            // Tạo 4 bức tường
            for (int i = 0; i < 4; i++)
            {
                GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
                wall.name = $"Wall_{i}";
                
                switch (i)
                {
                    case 0: // North
                        wall.transform.position = new Vector3(0, 2.5f, 25);
                        wall.transform.localScale = new Vector3(50, 5, 1);
                        break;
                    case 1: // South  
                        wall.transform.position = new Vector3(0, 2.5f, -25);
                        wall.transform.localScale = new Vector3(50, 5, 1);
                        break;
                    case 2: // East
                        wall.transform.position = new Vector3(25, 2.5f, 0);
                        wall.transform.localScale = new Vector3(1, 5, 50);
                        break;
                    case 3: // West
                        wall.transform.position = new Vector3(-25, 2.5f, 0);
                        wall.transform.localScale = new Vector3(1, 5, 50);
                        break;
                }

                // Thêm material
                if (testMaterials != null && testMaterials.Length > 0)
                {
                    wall.GetComponent<Renderer>().material = testMaterials[0];
                }
            }
        }

        private void CreateTestShelves()
        {
            Transform[] shelves = new Transform[numberOfShelves];

            for (int i = 0; i < numberOfShelves; i++)
            {
                GameObject shelf = GameObject.CreatePrimitive(PrimitiveType.Cube);
                shelf.name = $"Shelf_{i}";
                shelf.tag = "Shelf";
                
                // Sắp xếp shelves trong cửa hàng
                float x = (i % 2) * 10 - 5; // -5 hoặc 5
                float z = (i / 2) * 8 - 4;  // -4 hoặc 4
                
                shelf.transform.position = new Vector3(x, 0.5f, z);
                shelf.transform.localScale = new Vector3(3, 1, 1);

                // Thêm material
                if (testMaterials != null && testMaterials.Length > 1)
                {
                    shelf.GetComponent<Renderer>().material = testMaterials[1];
                }

                shelves[i] = shelf.transform;
            }

            // Gán vào ShopEnvironment nếu có
            if (shopEnvironment != null)
            {
                var shelfField = typeof(ShopEnvironment).GetField("shelfPositions",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                shelfField?.SetValue(shopEnvironment, shelves);
            }
        }

        private void CreateTestLighting()
        {
            // Tạo directional light
            GameObject lightObj = new GameObject("Main Light");
            Light light = lightObj.AddComponent<Light>();
            light.type = LightType.Directional;
            light.intensity = 1.2f;
            light.color = new Color(1f, 0.95f, 0.8f);
            lightObj.transform.rotation = Quaternion.Euler(50, -30, 0);

            // Tạo point lights cho atmosphere
            for (int i = 0; i < 4; i++)
            {
                GameObject pointLightObj = new GameObject($"Point Light {i}");
                Light pointLight = pointLightObj.AddComponent<Light>();
                pointLight.type = LightType.Point;
                pointLight.intensity = 0.8f;
                pointLight.range = 8f;
                pointLight.color = new Color(1f, 0.9f, 0.7f);
                
                float x = (i % 2) * 12 - 6;
                float z = (i / 2) * 12 - 6;
                pointLightObj.transform.position = new Vector3(x, 3, z);
            }
        }

        private void SpawnTestItems()
        {
            if (shopEnvironment != null)
            {
                // Delay để đảm bảo Economy System đã sẵn sàng
                Invoke(nameof(DelayedSpawnItems), 0.5f);
            }
        }

        private void DelayedSpawnItems()
        {
            if (shopEnvironment != null)
            {
                shopEnvironment.SpawnShopItems();
            }
        }

        private void SetupDemoPlayer()
        {
            // Tìm player hiện có
            var existingPlayerController = FindFirstObjectByType<PlayerController>();

            if (existingPlayerController != null)
            {
                demoPlayer = existingPlayerController.gameObject;

                // Thêm EconomyPlayerAdapter nếu chưa có
                var economyAdapter = demoPlayer.GetComponent<EconomyPlayerAdapter>();
                if (economyAdapter == null)
                {
                    economyAdapter = demoPlayer.AddComponent<EconomyPlayerAdapter>();
                }

                Debug.Log("✅ Đã tích hợp với PlayerController hiện có");
            }
            else if (playerPrefab != null)
            {
                demoPlayer = Instantiate(playerPrefab);
                demoPlayer.AddComponent<EconomyPlayerAdapter>();
            }
            else
            {
                Debug.LogWarning("Không tìm thấy PlayerController hiện có. Hãy đảm bảo có Player trong scene.");
                return;
            }

            // Đặt vị trí spawn
            if (demoPlayer != null)
            {
                demoPlayer.transform.position = new Vector3(0, 1, -10);
                demoPlayer.transform.rotation = Quaternion.identity;
            }
        }

        // Method này không còn cần thiết vì chúng ta sử dụng PlayerController hiện có

        private void SetupDemoUI()
        {
            if (!showInstructions) return;

            // Tạo instruction UI
            CreateInstructionUI();

            // Tạo Shop3DUI
            GameObject uiObj = new GameObject("Shop3DUI");
            Shop3DUI shop3DUI = uiObj.AddComponent<Shop3DUI>();

            // Bắt đầu demo steps
            if (instructionPanel != null)
            {
                StartDemoSteps();
            }
        }

        private void CreateInstructionUI()
        {
            // Tạo Canvas
            GameObject canvasObj = new GameObject("DemoCanvas");
            Canvas canvas = canvasObj.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
            canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();

            // Tạo instruction panel
            instructionPanel = new GameObject("InstructionPanel");
            instructionPanel.transform.SetParent(canvasObj.transform, false);
            
            UnityEngine.UI.Image panelImage = instructionPanel.AddComponent<UnityEngine.UI.Image>();
            panelImage.color = new Color(0, 0, 0, 0.8f);

            RectTransform panelRect = instructionPanel.GetComponent<RectTransform>();
            panelRect.anchorMin = new Vector2(0.1f, 0.1f);
            panelRect.anchorMax = new Vector2(0.9f, 0.4f);
            panelRect.offsetMin = Vector2.zero;
            panelRect.offsetMax = Vector2.zero;

            // Tạo instruction text
            GameObject textObj = new GameObject("InstructionText");
            textObj.transform.SetParent(instructionPanel.transform, false);
            
            instructionText = textObj.AddComponent<TextMeshProUGUI>();
            instructionText.text = "Loading...";
            instructionText.fontSize = 18;
            instructionText.color = Color.white;
            instructionText.alignment = TextAlignmentOptions.Center;

            RectTransform textRect = textObj.GetComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = new Vector2(20, 60);
            textRect.offsetMax = new Vector2(-20, -20);

            // Tạo buttons
            CreateDemoButtons();
        }

        private void CreateDemoButtons()
        {
            // Next button
            GameObject nextButtonObj = new GameObject("NextButton");
            nextButtonObj.transform.SetParent(instructionPanel.transform, false);
            
            nextStepButton = nextButtonObj.AddComponent<Button>();
            nextButtonObj.AddComponent<UnityEngine.UI.Image>().color = Color.green;
            
            GameObject nextTextObj = new GameObject("Text");
            nextTextObj.transform.SetParent(nextButtonObj.transform, false);
            TextMeshProUGUI nextText = nextTextObj.AddComponent<TextMeshProUGUI>();
            nextText.text = "Tiếp theo";
            nextText.fontSize = 16;
            nextText.color = Color.white;
            nextText.alignment = TextAlignmentOptions.Center;

            RectTransform nextRect = nextButtonObj.GetComponent<RectTransform>();
            nextRect.anchorMin = new Vector2(0.7f, 0f);
            nextRect.anchorMax = new Vector2(0.95f, 0.3f);
            nextRect.offsetMin = Vector2.zero;
            nextRect.offsetMax = Vector2.zero;

            RectTransform nextTextRect = nextTextObj.GetComponent<RectTransform>();
            nextTextRect.anchorMin = Vector2.zero;
            nextTextRect.anchorMax = Vector2.one;
            nextTextRect.offsetMin = Vector2.zero;
            nextTextRect.offsetMax = Vector2.zero;

            nextStepButton.onClick.AddListener(NextDemoStep);

            // Skip button
            GameObject skipButtonObj = new GameObject("SkipButton");
            skipButtonObj.transform.SetParent(instructionPanel.transform, false);
            
            skipDemoButton = skipButtonObj.AddComponent<Button>();
            skipButtonObj.AddComponent<UnityEngine.UI.Image>().color = Color.red;
            
            GameObject skipTextObj = new GameObject("Text");
            skipTextObj.transform.SetParent(skipButtonObj.transform, false);
            TextMeshProUGUI skipText = skipTextObj.AddComponent<TextMeshProUGUI>();
            skipText.text = "Bỏ qua";
            skipText.fontSize = 16;
            skipText.color = Color.white;
            skipText.alignment = TextAlignmentOptions.Center;

            RectTransform skipRect = skipButtonObj.GetComponent<RectTransform>();
            skipRect.anchorMin = new Vector2(0.05f, 0f);
            skipRect.anchorMax = new Vector2(0.3f, 0.3f);
            skipRect.offsetMin = Vector2.zero;
            skipRect.offsetMax = Vector2.zero;

            RectTransform skipTextRect = skipTextObj.GetComponent<RectTransform>();
            skipTextRect.anchorMin = Vector2.zero;
            skipTextRect.anchorMax = Vector2.one;
            skipTextRect.offsetMin = Vector2.zero;
            skipTextRect.offsetMax = Vector2.zero;

            skipDemoButton.onClick.AddListener(SkipDemo);
        }
        #endregion

        #region Demo Steps
        private void StartDemoSteps()
        {
            currentDemoStep = 0;
            UpdateInstructionText();
        }

        private void NextDemoStep()
        {
            currentDemoStep++;
            if (currentDemoStep >= demoSteps.Length)
            {
                FinishDemo();
            }
            else
            {
                UpdateInstructionText();
            }
        }

        private void UpdateInstructionText()
        {
            if (instructionText != null && currentDemoStep < demoSteps.Length)
            {
                instructionText.text = $"Bước {currentDemoStep + 1}/{demoSteps.Length}\n\n{demoSteps[currentDemoStep]}";
            }
        }

        private void SkipDemo()
        {
            FinishDemo();
        }

        private void FinishDemo()
        {
            if (instructionPanel != null)
            {
                instructionPanel.SetActive(false);
            }

            Debug.Log("🎉 Demo hoàn tất! Hãy khám phá cửa hàng 3D!");
        }
        #endregion

        #region Input Handling
        private void HandleDemoInput()
        {
            // Phím tắt cho demo
            if (Input.GetKeyDown(KeyCode.F1))
            {
                ToggleInstructions();
            }

            if (Input.GetKeyDown(KeyCode.F2))
            {
                RestartDemo();
            }

            if (Input.GetKeyDown(KeyCode.F3))
            {
                AddTestMoney();
            }
        }

        private void ToggleInstructions()
        {
            if (instructionPanel != null)
            {
                instructionPanel.SetActive(!instructionPanel.activeSelf);
            }
        }

        private void RestartDemo()
        {
            currentDemoStep = 0;
            StartDemoSteps();
            if (instructionPanel != null)
            {
                instructionPanel.SetActive(true);
            }
        }

        private void AddTestMoney()
        {
            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.Instance.ThemTien(1000, "Demo test money");
                Debug.Log("Đã thêm 1000 Lea cho demo!");
            }
        }
        #endregion

        #region Public Methods
        public void SetupCompleteDemo()
        {
            autoSetupDemo = true;
            createTestEnvironment = true;
            spawnTestItems = true;
            showInstructions = true;
            SetupDemo();
        }

        public void CreateMinimalDemo()
        {
            autoSetupDemo = true;
            createTestEnvironment = false;
            spawnTestItems = false;
            showInstructions = true;
            SetupDemo();
        }
        #endregion

        #region Editor Methods
        #if UNITY_EDITOR
        [ContextMenu("Setup Complete Demo")]
        private void EditorSetupCompleteDemo()
        {
            SetupCompleteDemo();
        }

        [ContextMenu("Create Minimal Demo")]
        private void EditorCreateMinimalDemo()
        {
            CreateMinimalDemo();
        }

        [ContextMenu("Add Test Money")]
        private void EditorAddTestMoney()
        {
            AddTestMoney();
        }
        #endif
        #endregion
    }
}
