Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:24:38Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker0.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [22404]  Target information:

Player connection [22404]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2067022643 [EditorId] 2067022643 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [22404] Host joined multi-casting on [***********:54997]...
Player connection [22404] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 159.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56316
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001347 seconds.
- Loaded All Assemblies, in  2.812 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.407 seconds
Domain Reload Profiling: 3218ms
	BeginReloadAssembly (536ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (463ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (1756ms)
		LoadAssemblies (535ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1754ms)
			TypeCache.Refresh (1752ms)
				TypeCache.ScanAssembly (1237ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (407ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (362ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (66ms)
			ProcessInitializeOnLoadAttributes (185ms)
			ProcessInitializeOnLoadMethodAttributes (81ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.323 seconds
Refreshing native plugins compatible for Editor in 0.58 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.436 seconds
Domain Reload Profiling: 5759ms
	BeginReloadAssembly (181ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (3060ms)
		LoadAssemblies (2655ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (504ms)
			TypeCache.Refresh (421ms)
				TypeCache.ScanAssembly (296ms)
			BuildScriptInfoCaches (67ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (2437ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1219ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (261ms)
			ProcessInitializeOnLoadAttributes (802ms)
			ProcessInitializeOnLoadMethodAttributes (137ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.07 seconds
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (8.5 MB). Loaded Objects now: 7294.
Memory consumption went from 167.1 MB to 158.5 MB.
Total: 25.516900 ms (FindLiveObjects: 1.067700 ms CreateObjectMapping: 1.717300 ms MarkObjects: 14.666100 ms  DeleteObjects: 8.063900 ms)

========================================================================
Received Import Request.
  Time since last request: 103002.741430 seconds.
  path: Assets/Layer Lab/3D Medieval PackBundle/+README+/LayerLab_UserAssetGuide.txt
  artifactKey: Guid(2b268dc4c07a8cf47a45246c75e7c8e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Layer Lab/3D Medieval PackBundle/+README+/LayerLab_UserAssetGuide.txt using Guid(2b268dc4c07a8cf47a45246c75e7c8e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dd402a1e306fda21e62dd47b19c63fc3') in 0.0400659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 81.945155 seconds.
  path: Assets/Layer Lab/3D Medieval PackBundle/Builtin/Built_in.unitypackage
  artifactKey: Guid(a972b1a94ffee314889ae043a3511b68) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Layer Lab/3D Medieval PackBundle/Builtin/Built_in.unitypackage using Guid(a972b1a94ffee314889ae043a3511b68) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b5ba18143be63f3562da68a171a8ef9') in 0.0012836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 32.204169 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX
  artifactKey: Guid(1680c8a2de3fbd74d9ff3f8e9e63a4c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX using Guid(1680c8a2de3fbd74d9ff3f8e9e63a4c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9614934fac0bae3a0e3b07959437518') in 0.0005112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.983829 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_A.fbx
  artifactKey: Guid(da92c76c1b777de49bf718e4c45df2a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_A.fbx using Guid(da92c76c1b777de49bf718e4c45df2a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4efd3fd3db9fda33f9aa06d3e53b54dd') in 0.6346877 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.720617 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_H.fbx
  artifactKey: Guid(5e2b2d3e81308bf4199cc19c360468ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_H.fbx using Guid(5e2b2d3e81308bf4199cc19c360468ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f1c5e6574d655f32f3813b2d02e8d3cb') in 0.032852 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.749137 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_C.fbx
  artifactKey: Guid(755962e6d1f7e064f80bb254f446401b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_C.fbx using Guid(755962e6d1f7e064f80bb254f446401b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d79f8fa6c8345898a7c3dc0eb26e05f') in 0.0373991 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 171.609147 seconds.
  path: Assets/Layer Lab/3D Medieval Pack
  artifactKey: Guid(520ff1aebcc89404cb236d2b863141e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Layer Lab/3D Medieval Pack using Guid(520ff1aebcc89404cb236d2b863141e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb7c918fb74bf5c3fc944e217dc3a94a') in 0.0009856 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 27.883556 seconds.
  path: Assets/InputSystem.inputsettings.asset
  artifactKey: Guid(4ab95b5f72e910e429e23620d1665ebb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/InputSystem.inputsettings.asset using Guid(4ab95b5f72e910e429e23620d1665ebb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8e15e9f9ac98dbcc09e76329ec57c419') in 0.3284377 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000090 seconds.
  path: Assets/Settings/Mobile_RPAsset.asset
  artifactKey: Guid(5e6cbd92db86f4b18aec3ed561671858) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/Mobile_RPAsset.asset using Guid(5e6cbd92db86f4b18aec3ed561671858) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6bc55f722d0e68c9a9ae261014b66044') in 0.1029492 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Cheese_D.fbx
  artifactKey: Guid(ecde5a8e6cc4e524eb6ec55de51c7e04) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Cheese_D.fbx using Guid(ecde5a8e6cc4e524eb6ec55de51c7e04) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b1eaa0f9418d2c408dd5dd83970fd7bd') in 0.1876821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000130 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_D.prefab
  artifactKey: Guid(42b80b98eb55ed442a5961ff219969da) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_D.prefab using Guid(42b80b98eb55ed442a5961ff219969da) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8c8c613b7847eee0162b798ad50052a2') in 0.0241505 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_A.prefab
  artifactKey: Guid(1ba3d1e5a1ddca344aea62a9311b68f1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_A.prefab using Guid(1ba3d1e5a1ddca344aea62a9311b68f1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1ee428b76e7dec9f7b58a191290f65cb') in 0.0237539 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_A.prefab
  artifactKey: Guid(13abd986b4814744da95d88992f89972) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_A.prefab using Guid(13abd986b4814744da95d88992f89972) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a9aa919e9a386352c4716695ac2f970f') in 0.0163808 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval PackBundle/+README+
  artifactKey: Guid(75530e4d199ffcd4aae4f7b635a1fb6f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval PackBundle/+README+ using Guid(75530e4d199ffcd4aae4f7b635a1fb6f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4e3f5e400df6b10d81944519c44bf4ca') in 0.0218013 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Script/Player/PlayerInteraction.cs
  artifactKey: Guid(e8b9caeabcac35041b7cd6dbd30a9629) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/PlayerInteraction.cs using Guid(e8b9caeabcac35041b7cd6dbd30a9629) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'af1bcfcbc17e4183c3eb6838149fedf0') in 0.046991 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000210 seconds.
  path: Assets/Script/Player/Editor
  artifactKey: Guid(8e019742b8e219f43b450c1e34962291) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Editor using Guid(8e019742b8e219f43b450c1e34962291) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4fad1ba2c9c31669b1a342e25736d128') in 0.0340859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval PackBundle
  artifactKey: Guid(a0613ce30398f41459c5a7b28023ad80) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval PackBundle using Guid(a0613ce30398f41459c5a7b28023ad80) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc609392e319d2c06dd3fcf34c4f7e87') in 0.0342146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Script/Economy/UI/ShopUI.cs
  artifactKey: Guid(268d9bce75777004ea9d5d8e3d566203) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/UI/ShopUI.cs using Guid(268d9bce75777004ea9d5d8e3d566203) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71e1cad578bf9fa2544c477fd22c2fd5') in 0.0399611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000117 seconds.
  path: Assets/Script/Player/CameraAutoSetup.cs
  artifactKey: Guid(e9a40781a9eb13a489346933a873b748) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/CameraAutoSetup.cs using Guid(e9a40781a9eb13a489346933a873b748) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd18047db34b36104c39d356819d6eddc') in 0.036182 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_I.prefab
  artifactKey: Guid(a31832b87f370a949a36eae5dd7215a6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_I.prefab using Guid(a31832b87f370a949a36eae5dd7215a6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9e120ce3cb23e2544ad4416295c81476') in 0.0309112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/Script/Player/CameraSetupHelper.cs
  artifactKey: Guid(dcd3ead2de6aa1a4cadd8b0ca62afa53) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/CameraSetupHelper.cs using Guid(dcd3ead2de6aa1a4cadd8b0ca62afa53) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9fd87851c187f4839507a47d2255b70') in 0.028015 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_S.prefab
  artifactKey: Guid(c9c44da634456964781530eb46dcb2a7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_S.prefab using Guid(c9c44da634456964781530eb46dcb2a7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0952f7cf9ddf2dad15d5e6f214681ba9') in 0.0398147 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_C.prefab
  artifactKey: Guid(6799160d531a6b440a90b1d5fdeb4fd7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_C.prefab using Guid(6799160d531a6b440a90b1d5fdeb4fd7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4f5456f394a79a8af6f7af8467804ec1') in 0.0262849 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bed_B.prefab
  artifactKey: Guid(0efb1c693ee53df4e807dc6a5f1039f5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bed_B.prefab using Guid(0efb1c693ee53df4e807dc6a5f1039f5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c4ba1b9ae232a9f30aca7be73dfc92eb') in 0.0269075 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_G.prefab
  artifactKey: Guid(3f5c62ff4ad1dfd408c8c5577da369ec) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_G.prefab using Guid(3f5c62ff4ad1dfd408c8c5577da369ec) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cd7e456564b154eba77861ebc44f6a6d') in 0.0267885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_E.fbx
  artifactKey: Guid(2dfab7556da59d34c819ffea04d9935a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_E.fbx using Guid(2dfab7556da59d34c819ffea04d9935a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '407ee95be7eb42ba88d19221d62c95a1') in 0.0405883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Mountain_B.fbx
  artifactKey: Guid(ab2cfe89c910bae45b353cd2013e8776) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Mountain_B.fbx using Guid(ab2cfe89c910bae45b353cd2013e8776) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '971d2a84c9de105f59821d0ed4e37165') in 0.0468217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_A.fbx
  artifactKey: Guid(6897027d65868ba4e938c86f95cb65ca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_A.fbx using Guid(6897027d65868ba4e938c86f95cb65ca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de65c2e4916584cdc7157822b5b8f2a5') in 0.0456794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_D.prefab
  artifactKey: Guid(5123f2ac6b69c6641a188021662067fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_D.prefab using Guid(5123f2ac6b69c6641a188021662067fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '48d29dd86a8b6814ac6edea861aedd18') in 0.0316151 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_A.prefab
  artifactKey: Guid(fb2c29399c4aca842bae9b6e361b6f03) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_A.prefab using Guid(fb2c29399c4aca842bae9b6e361b6f03) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '744359e724533741573604949f026268') in 0.0433018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Script/Player/Guides/HUONG_DAN_THEM_MODEL.md
  artifactKey: Guid(cf1c862c9a0736a469698c21f4e5a222) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/HUONG_DAN_THEM_MODEL.md using Guid(cf1c862c9a0736a469698c21f4e5a222) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd7b6c816d644682d2fc69d9bc591175a') in 0.0564865 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_G.prefab
  artifactKey: Guid(e01aca851b43ce242a64d94330a0c53c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_G.prefab using Guid(e01aca851b43ce242a64d94330a0c53c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c0612036e477bcf47269651b9b9bce76') in 0.2371489 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_L.prefab
  artifactKey: Guid(79ca67310ac86c244bd6fd275c182391) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_L.prefab using Guid(79ca67310ac86c244bd6fd275c182391) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '30083df0bb0d1716680559b820b8e483') in 0.0241176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_B.prefab
  artifactKey: Guid(81809bc738f2464448cf8bdca45ba8e4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_B.prefab using Guid(81809bc738f2464448cf8bdca45ba8e4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7a8ec5602ce7ca75bcb9b9b5b73c1dd4') in 0.0219157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_D.prefab
  artifactKey: Guid(378c0167555c5e04ab8928b8613d68bb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_D.prefab using Guid(378c0167555c5e04ab8928b8613d68bb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0ab82603268b457bfe6ea8e9fa65d33d') in 0.0234108 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_F.prefab
  artifactKey: Guid(f983798fc5971cd43b37097dbb74fb4b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_F.prefab using Guid(f983798fc5971cd43b37097dbb74fb4b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e3809c87b245f89eadbc4c8f9dde7381') in 0.0324683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_L.prefab
  artifactKey: Guid(503bf02efad4cbf43beeda2249ccbae0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_L.prefab using Guid(503bf02efad4cbf43beeda2249ccbae0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2857d49efb11afb1baa620cedb5eec61') in 0.0293421 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000102 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Open_B.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Open_B.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bda91a729ade225a589674384960695c') in 0.033446 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Apple_Green.fbx
  artifactKey: Guid(a9c72d9cfdc29f84f821d725fdac891f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Apple_Green.fbx using Guid(a9c72d9cfdc29f84f821d725fdac891f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dac5975e62143ebde29d6e908f6d6780') in 0.0398272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BowlMedicine_B.prefab
  artifactKey: Guid(4cd7093e49ac2834ca8ba5f090921f1c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BowlMedicine_B.prefab using Guid(4cd7093e49ac2834ca8ba5f090921f1c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b1072b3a930b04c5ce56bb287a13544b') in 0.0254419 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_E.prefab
  artifactKey: Guid(16bb755f7c294ef45a12beffe1040d07) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_E.prefab using Guid(16bb755f7c294ef45a12beffe1040d07) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a3f2e00e526bb59d8f776a003028998a') in 0.0267784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000091 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_H.prefab
  artifactKey: Guid(79876c7bb996ca94a9ff16ac41a4bc3b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_H.prefab using Guid(79876c7bb996ca94a9ff16ac41a4bc3b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '54792d6354470ed1edf67a5f74dee333') in 0.0313517 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_C.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_C.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a1ce8299658a4c1fd2f951afd11d7d9f') in 0.0289019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/InputSystem.inputsettings.asset
  artifactKey: Guid(4ab95b5f72e910e429e23620d1665ebb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/InputSystem.inputsettings.asset using Guid(4ab95b5f72e910e429e23620d1665ebb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c01f9580614ddb065ec0d9c42826f2e') in 0.0871506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_A.prefab
  artifactKey: Guid(afab7be7eda641640961e5f751b0e9b9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_A.prefab using Guid(afab7be7eda641640961e5f751b0e9b9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6e6f5c438821283a91567a80c1189e0d') in 0.0285979 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000086 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_C.prefab
  artifactKey: Guid(ab50578661df52348a252c3b04230a42) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_C.prefab using Guid(ab50578661df52348a252c3b04230a42) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '86a64a90bb1f02851fcc28de73650e86') in 0.0224067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_A.prefab
  artifactKey: Guid(2eae18acc6a5dd047912b1c30bf2a765) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_A.prefab using Guid(2eae18acc6a5dd047912b1c30bf2a765) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ba4f30b3ecc61fdf89159e3a29da7e37') in 0.019208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_C.prefab
  artifactKey: Guid(7678284f3efea1340a83c6c07c11967e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_C.prefab using Guid(7678284f3efea1340a83c6c07c11967e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b5be426b48e6530ec5632d3588c6fc77') in 0.0349446 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_F.prefab
  artifactKey: Guid(8f95da4b0436b8f40a0f18e1b3c6da1e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_F.prefab using Guid(8f95da4b0436b8f40a0f18e1b3c6da1e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e7b4676c56ea3281ed830dbe002cef4d') in 0.0376759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_F.prefab
  artifactKey: Guid(a28448a815ded8e458b13eb20a6943fe) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_F.prefab using Guid(a28448a815ded8e458b13eb20a6943fe) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c2362e3561f44346c97bd24705717a21') in 0.0400208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_I.prefab
  artifactKey: Guid(4b45e77a8ad15c44b8bab5ba68f0a64d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_I.prefab using Guid(4b45e77a8ad15c44b8bab5ba68f0a64d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd5beb5548f4a3b8f3720bf9b3796603e') in 0.0507713 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000099 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_K.prefab
  artifactKey: Guid(fd5ffb1771de0c546b7d4b16aa257b6b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_K.prefab using Guid(fd5ffb1771de0c546b7d4b16aa257b6b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0a4bcbb90f3e7372a4168cca4f5682cf') in 0.0396948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000108 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Beerglass_A.prefab
  artifactKey: Guid(56e79185ed7af6e43bffefe15dfe30b5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Beerglass_A.prefab using Guid(56e79185ed7af6e43bffefe15dfe30b5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4ab2c607b88979e3c032c2f574ca9d2d') in 0.0350299 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/GunnyBag_Beige.fbx
  artifactKey: Guid(a297a826c44e23e4bbdaa8ea49a220b8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/GunnyBag_Beige.fbx using Guid(a297a826c44e23e4bbdaa8ea49a220b8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0244640dcde6a90af0c35f69f34e02cd') in 0.0722429 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Part_Steel_B.fbx
  artifactKey: Guid(8aa5efca459c1cc4c8600a542d7498ae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Part_Steel_B.fbx using Guid(8aa5efca459c1cc4c8600a542d7498ae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef36ec73a2cf6c4544aecda071c50e8f') in 0.0745941 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000087 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_A.prefab
  artifactKey: Guid(a1012c60a22de2648833e6a4eba62877) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_A.prefab using Guid(a1012c60a22de2648833e6a4eba62877) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b4383c600b2d8c2ca740c12f6ca38317') in 0.0425878 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Blue.prefab
  artifactKey: Guid(8076d9e3de371c646a627939bfa655b0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Blue.prefab using Guid(8076d9e3de371c646a627939bfa655b0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '96aef8d076f91e60b43abcdc7a419753') in 0.0326835 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Script/Player/Guides/HUONG_DAN_TUY_CHINH_MODEL.md
  artifactKey: Guid(56afc88a0c005a643b433181f5c10964) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/HUONG_DAN_TUY_CHINH_MODEL.md using Guid(56afc88a0c005a643b433181f5c10964) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7569464a9f86ad64788ccc0bd80e7ed9') in 0.044548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_B.prefab
  artifactKey: Guid(f30173630e166a745adb7884a8eccafa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_B.prefab using Guid(f30173630e166a745adb7884a8eccafa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b4b6ad44389b02b57eafef171f821a51') in 0.0372612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Yellow.prefab
  artifactKey: Guid(48d004cb989fb60499597c1fc04c5f47) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Yellow.prefab using Guid(48d004cb989fb60499597c1fc04c5f47) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3149bf2564e54504d951e07ff8785c40') in 0.0350185 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_J.prefab
  artifactKey: Guid(fba32127fd2140747aae8308d7268ff6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_J.prefab using Guid(fba32127fd2140747aae8308d7268ff6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c03689ef48feae41290025b04fb31d5d') in 0.0222812 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_E.prefab
  artifactKey: Guid(ae0792bccdea00b459bc2d5232358d37) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_E.prefab using Guid(ae0792bccdea00b459bc2d5232358d37) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '121721bcd90541e88ab16fc2f57f3afb') in 0.023024 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bellows.prefab
  artifactKey: Guid(a64d2416c8766da44b3aad77815357a6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bellows.prefab using Guid(a64d2416c8766da44b3aad77815357a6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4f8c23f1cd7bda264e24b187eadad343') in 0.0223683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Blue.fbx
  artifactKey: Guid(02c95ffc914a2b147975b06d3d439612) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Blue.fbx using Guid(02c95ffc914a2b147975b06d3d439612) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b45da40066ecbf94966b2e3a69b4d86f') in 0.0361396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Brown.prefab
  artifactKey: Guid(29594bb57f313d24f8ed5eef121b57e4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Brown.prefab using Guid(29594bb57f313d24f8ed5eef121b57e4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0a606c06b00f6f04326ab9681fd660f9') in 0.0216416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_E.prefab
  artifactKey: Guid(84649cb73ad3ab44cad9ae0cacfe83ca) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_E.prefab using Guid(84649cb73ad3ab44cad9ae0cacfe83ca) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b77985ff6250974988843d71c62a5421') in 0.0540347 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_SweetPotato.prefab
  artifactKey: Guid(c6c8a250d3f72e84588eafdb50760248) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_SweetPotato.prefab using Guid(c6c8a250d3f72e84588eafdb50760248) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '408c7d17192b7a42f75897e1baf978d3') in 0.0267929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_F.prefab
  artifactKey: Guid(e1ef978698948aa44ba2b5d6c6f90504) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_F.prefab using Guid(e1ef978698948aa44ba2b5d6c6f90504) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1e74036eec81d385efdccbe6844ab01d') in 0.0218629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_B.prefab
  artifactKey: Guid(6bccb00b358e5c140925a86d14b2b7be) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_B.prefab using Guid(6bccb00b358e5c140925a86d14b2b7be) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7d4b35e427d3d27971d246e75c4618ad') in 0.0219753 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Anvil_A.prefab
  artifactKey: Guid(e2b76348f3d1ee446bfa1ba69d789888) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Anvil_A.prefab using Guid(e2b76348f3d1ee446bfa1ba69d789888) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e4156020c452d5c05a8660188b450d6d') in 0.02924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Purple.prefab
  artifactKey: Guid(71857e1c4631ccf4eb79dad627b809a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Purple.prefab using Guid(71857e1c4631ccf4eb79dad627b809a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '55d1ffaded5f8f654d401b88e4480dc4') in 0.0341184 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_B.prefab
  artifactKey: Guid(567fd0cd3b1e93442a7d4c72c1880a1b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_B.prefab using Guid(567fd0cd3b1e93442a7d4c72c1880a1b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fe690c23d39796064255a8d551abbcff') in 0.0328968 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Beige_B.prefab
  artifactKey: Guid(5dcfafc313fe9ec4b8c9ba3c043a2d53) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Beige_B.prefab using Guid(5dcfafc313fe9ec4b8c9ba3c043a2d53) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e685a1d20967008df3fafa719fc6b6fa') in 0.0287217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_C.prefab
  artifactKey: Guid(609dc42943db16a49a3f14a5a6bac380) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_C.prefab using Guid(609dc42943db16a49a3f14a5a6bac380) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff25c0093e07d0230fc60f5a4f90a82d') in 0.2320957 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Script/Economy/README.md
  artifactKey: Guid(b0a776ed0948b4a41a8df012d2070c9d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/README.md using Guid(b0a776ed0948b4a41a8df012d2070c9d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '14794da4b688d48b01fe0050b8553b8a') in 0.0697583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/Layer Lab/3D Medieval PackBundle/Builtin
  artifactKey: Guid(077833e0a09626446b02937726e80882) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval PackBundle/Builtin using Guid(077833e0a09626446b02937726e80882) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4bd90524922f7343e5013447e1b3dd97') in 0.0254297 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Script/Player/Guides/HUONG_DAN_CAMERA_SYSTEM.md
  artifactKey: Guid(ff39f4d8e65cc2847bfcd5569a9e3ebe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/HUONG_DAN_CAMERA_SYSTEM.md using Guid(ff39f4d8e65cc2847bfcd5569a9e3ebe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f2f390bcbd36c7739b85a68bb30a744d') in 0.0734557 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_L.prefab
  artifactKey: Guid(24ffa6701b6ef1b46b0c48084a11fd35) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_L.prefab using Guid(24ffa6701b6ef1b46b0c48084a11fd35) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fe1224fbc6ca44c0e0f565c7bdf4edf0') in 0.0207244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_E.prefab
  artifactKey: Guid(b7a3ba7d5f5c26e4faa853f8fa2a55af) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_E.prefab using Guid(b7a3ba7d5f5c26e4faa853f8fa2a55af) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '46bd53addb7a1d952cf6d00e5dc10f97') in 0.0262192 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_C.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_C.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5effb28a334be6d9684afa509a5f8cc5') in 0.0239932 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_A.prefab
  artifactKey: Guid(75a25c3532820674ba6e62f883baa293) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_A.prefab using Guid(75a25c3532820674ba6e62f883baa293) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7ad4722259676ef187f91a976fa8ea29') in 0.0248253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_E.prefab
  artifactKey: Guid(518655a08f22252489187ca4152da84a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_E.prefab using Guid(518655a08f22252489187ca4152da84a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f47fa294763c14baf17231f593ca391c') in 0.0234491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_K.prefab
  artifactKey: Guid(7b6729477db49a6498c1c035abdbb0ac) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_K.prefab using Guid(7b6729477db49a6498c1c035abdbb0ac) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a73c3bcad2a5c789296e789fa597405a') in 0.0242656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Script/Player/PlayerModelSetup.cs
  artifactKey: Guid(98050471971a4b14a9f5c44c460a3669) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/PlayerModelSetup.cs using Guid(98050471971a4b14a9f5c44c460a3669) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b171ea44e5b035418139c76b2859847b') in 0.0222239 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tent_B.fbx
  artifactKey: Guid(5a2483548310b94429f0a0ca30e2a7df) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tent_B.fbx using Guid(5a2483548310b94429f0a0ca30e2a7df) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '86a1d8ab404f2bb18330be4fec35e457') in 0.0357294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_B.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_B.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fdee7b74f3044818efbaf80414190e31') in 0.022891 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Settings/DefaultVolumeProfile.asset
  artifactKey: Guid(ab09877e2e707104187f6f83e2f62510) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/DefaultVolumeProfile.asset using Guid(ab09877e2e707104187f6f83e2f62510) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c30cb18ec07e82d44326596c9e3658e1') in 0.0192141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_C.prefab
  artifactKey: Guid(d22d75ea99877444dbb933e64ba4016b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_C.prefab using Guid(d22d75ea99877444dbb933e64ba4016b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '20ccb905f5e63329dbb6aec3e6f0fd73') in 0.0396661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Script/Player/KHAC_PHUC_LOI.md
  artifactKey: Guid(aa38a020069a9954cb1ba06ba2eb5b21) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/KHAC_PHUC_LOI.md using Guid(aa38a020069a9954cb1ba06ba2eb5b21) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b70ab53cea00c41149d4e1358a4037c3') in 0.0385133 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000117 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_Beige.prefab
  artifactKey: Guid(fb9a32debb833004594bfdd1e230881d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_Beige.prefab using Guid(fb9a32debb833004594bfdd1e230881d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5c67bd42a5ee3d8f6ae0b23e3a2694ab') in 0.2587134 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Script/Economy/Item.cs
  artifactKey: Guid(fb5688bf97c02244888a118cef0be294) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/Item.cs using Guid(fb5688bf97c02244888a118cef0be294) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17e4ed7640c729ad46e28aee95e96f43') in 0.0928073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_C.prefab
  artifactKey: Guid(4c96ebca674f0a944b5f3a179025ae1f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_C.prefab using Guid(4c96ebca674f0a944b5f3a179025ae1f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '37ac2aa70e661d7fa58b4912d0171235') in 0.1567557 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Script/Economy/EconomySystemSetup.cs
  artifactKey: Guid(94be6db0e54ba1a43a803b1c75d0aeb6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/EconomySystemSetup.cs using Guid(94be6db0e54ba1a43a803b1c75d0aeb6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f707bc5095fe16915744310bbfa35ca') in 0.1380098 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_F.prefab
  artifactKey: Guid(dbf0783442bfd6e4590b669c0e5e5778) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_F.prefab using Guid(dbf0783442bfd6e4590b669c0e5e5778) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ca71775572c7765e7a58c1e6606bdfba') in 0.1646943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Script/Player/Guides/README.md
  artifactKey: Guid(29834de514e7edc468d610ef0b7a12ba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/README.md using Guid(29834de514e7edc468d610ef0b7a12ba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e4d5bfa3db0653ffb8382420dcc27247') in 0.1052788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_A.fbx
  artifactKey: Guid(1a6bc18aea5f82f4095ee043424ff60c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_A.fbx using Guid(1a6bc18aea5f82f4095ee043424ff60c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '24d7d63f9d79628190272c962fd7a594') in 0.0292984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Scenes/SampleScene.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Scenes/SampleScene.unity additively'
Loaded scene 'Assets/Scenes/SampleScene.unity'
	Deserialize:            23.097 ms
	Integration:            9.224 ms
	Integration of assets:  0.034 ms
	Thread Wait Time:       0.079 ms
	Total Operation Time:   32.434 ms
 -> (artifact id: 'e741474caa9b2c48a31b442ca2e5f1ec') in 0.1051836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000088 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_A.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_A.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0e3d1cef942ddc5352b4e366dec9eb3c') in 0.0213701 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_D.prefab
  artifactKey: Guid(cf413120d31deb543b5838e3a6fc9890) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_D.prefab using Guid(cf413120d31deb543b5838e3a6fc9890) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9485ee1ecc2567ddaf8058535a33de59') in 0.0246682 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Egg_A.prefab
  artifactKey: Guid(9cc65b1ba4e6c7a449366ea7cb87687c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Egg_A.prefab using Guid(9cc65b1ba4e6c7a449366ea7cb87687c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '78817e8ff496b8e10a1e15c8008e0736') in 0.0279245 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_B.prefab
  artifactKey: Guid(eca570404962c4343a308d2105a47104) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_B.prefab using Guid(eca570404962c4343a308d2105a47104) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e3f53f8ead58361af9aecd30f804c1a6') in 0.0276898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_A.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_A.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2223cb7b0b3de31018f7faabbffcc739') in 0.0206591 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000131 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_B.prefab
  artifactKey: Guid(f59b4b5ce6651f84393f7958e775bd65) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_B.prefab using Guid(f59b4b5ce6651f84393f7958e775bd65) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'edc1f0eb133c15c3d0198ab2430f2b54') in 0.0218352 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Script/Player/InputSystem_Actions.cs
  artifactKey: Guid(13d1cb7dd1be5d54c9471a7aa727a450) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/InputSystem_Actions.cs using Guid(13d1cb7dd1be5d54c9471a7aa727a450) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4bb41f0b2932a3cb1796b60672eaf341') in 0.0220635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_A.prefab
  artifactKey: Guid(e1b03fc33957bed4eb4c3618fc18af52) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_A.prefab using Guid(e1b03fc33957bed4eb4c3618fc18af52) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6201e86e41a347f13063ed31571dfbf6') in 0.0246023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_A.prefab
  artifactKey: Guid(a08671c32d49c7e4fb578bf543499f1a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_A.prefab using Guid(a08671c32d49c7e4fb578bf543499f1a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ed33f5379c379039520e2cf5c9e27124') in 0.0236237 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000385 seconds.
  path: Assets/Script/Player/KHAC_PHUC_DI_CHUYEN.md
  artifactKey: Guid(36be92b41cde83c49914d9791b16fd85) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/KHAC_PHUC_DI_CHUYEN.md using Guid(36be92b41cde83c49914d9791b16fd85) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab85d62847003488eb8cbff0ad4328d3') in 0.0380007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Blue.fbx
  artifactKey: Guid(1698c98ff43861245a04c0343e17d166) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Blue.fbx using Guid(1698c98ff43861245a04c0343e17d166) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc8c0ac3db54fd0f08f5a82e5b6e22d9') in 0.041086 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval PackBundle/+README+/LayerLab_UserAssetGuide.txt
  artifactKey: Guid(2b268dc4c07a8cf47a45246c75e7c8e1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval PackBundle/+README+/LayerLab_UserAssetGuide.txt using Guid(2b268dc4c07a8cf47a45246c75e7c8e1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'df37e4d0643480590a9483243e440822') in 0.0225875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/CandelStick_B.prefab
  artifactKey: Guid(38a4e775d493bb7408d3c3001ca0804e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/CandelStick_B.prefab using Guid(38a4e775d493bb7408d3c3001ca0804e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5101725508dbb6a7b5fd633ec74094da') in 0.0230784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_E.prefab
  artifactKey: Guid(2f136f6a66182ac4f8bb682a2c4e46fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_E.prefab using Guid(2f136f6a66182ac4f8bb682a2c4e46fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '683444fe50f97105639b63241c33ce06') in 0.0268022 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_A.prefab
  artifactKey: Guid(24af20b58bdc99f48877c7f5956c0c75) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_A.prefab using Guid(24af20b58bdc99f48877c7f5956c0c75) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3ae0b4d4ae2e46282dde9a2be49572f4') in 0.0208039 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_A.prefab
  artifactKey: Guid(ce3c83e3563b1a04aac16e6bf6d6bd6b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_A.prefab using Guid(ce3c83e3563b1a04aac16e6bf6d6bd6b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4aee865bc57f2e2419a0019f22f85fc0') in 0.025903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_B.prefab
  artifactKey: Guid(0e80046ae8097354a89687c471ba3060) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_B.prefab using Guid(0e80046ae8097354a89687c471ba3060) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd4de52190289cf35d2ebe2e446377e96') in 0.0247968 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_J.prefab
  artifactKey: Guid(09ac18436baea7149aecf2268d80707c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_J.prefab using Guid(09ac18436baea7149aecf2268d80707c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '19eb5c1d55ef1a492207fb47371218e6') in 0.0280064 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_B.fbx
  artifactKey: Guid(8406b7eca86597e40a9ce2f46c603ff3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_B.fbx using Guid(8406b7eca86597e40a9ce2f46c603ff3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a45281f4bae8a82340f415f09c88d9eb') in 0.0448905 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_R.prefab
  artifactKey: Guid(f9711398e795d1341988ba26acec254a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_R.prefab using Guid(f9711398e795d1341988ba26acec254a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '63795cc4f67f985d3f0463821c7919f4') in 0.0208165 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Dish_B.fbx
  artifactKey: Guid(d8a23ba308d850349bedcef7930c2cd2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Dish_B.fbx using Guid(d8a23ba308d850349bedcef7930c2cd2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '039d04698ce72165ea0aa30cd1374b3d') in 0.0371329 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_B.prefab
  artifactKey: Guid(a179bdc47b356b34b8c7eda14db709a4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_B.prefab using Guid(a179bdc47b356b34b8c7eda14db709a4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ed8dcb2784460d14b9496e810324be89') in 0.027901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_L.prefab
  artifactKey: Guid(62979646132ec974b87fbd7ee128fbb5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_L.prefab using Guid(62979646132ec974b87fbd7ee128fbb5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d987a8b0437db9ea8d6c9d5a33a30a7') in 0.033275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000145 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_White.prefab
  artifactKey: Guid(1f92c86feee0c7f41a808ac2a1bb7fbf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_White.prefab using Guid(1f92c86feee0c7f41a808ac2a1bb7fbf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2359e356bd8a4cdd1f31a630440a7256') in 0.0290488 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bellows.prefab
  artifactKey: Guid(a64d2416c8766da44b3aad77815357a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bellows.prefab using Guid(a64d2416c8766da44b3aad77815357a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9686716856691de36c816ab0f50f075c') in 0.0282426 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_J.prefab
  artifactKey: Guid(774c92e734dbd0648b8a8bc833a492f3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_J.prefab using Guid(774c92e734dbd0648b8a8bc833a492f3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '120e193cdcb0f3d9726108608d339eda') in 0.022794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lemon.prefab
  artifactKey: Guid(c15791ef484b90a4db4558893929f25f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lemon.prefab using Guid(c15791ef484b90a4db4558893929f25f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8040c09595aa28df7cd563451f07edcd') in 0.0249407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_White.prefab
  artifactKey: Guid(63e8317d1ef3a9e41b831ca2cc6e185f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_White.prefab using Guid(63e8317d1ef3a9e41b831ca2cc6e185f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cf936d5cf427027197beb6823be14b4f') in 0.0194368 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_L.prefab
  artifactKey: Guid(24ffa6701b6ef1b46b0c48084a11fd35) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_L.prefab using Guid(24ffa6701b6ef1b46b0c48084a11fd35) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76537ce626b8ba3e7d304ca03c5ec17a') in 0.0336162 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000817 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_SweetPotato.prefab
  artifactKey: Guid(c6c8a250d3f72e84588eafdb50760248) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_SweetPotato.prefab using Guid(c6c8a250d3f72e84588eafdb50760248) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6cb140438b72ee0d665f5a9a0fd647d0') in 0.0376196 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_B.prefab
  artifactKey: Guid(efb4054f963fd4046aa1edc91917ce48) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_B.prefab using Guid(efb4054f963fd4046aa1edc91917ce48) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6072853196eecdb71ee419629f4a790b') in 0.0298545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000190 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_C.prefab
  artifactKey: Guid(f187fcd4ae71da949be7c6593e2a56ed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_C.prefab using Guid(f187fcd4ae71da949be7c6593e2a56ed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '38f3bd1a3cc18264f2f1922f9389a5bb') in 0.0290041 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_G.prefab
  artifactKey: Guid(a7caa8247ab4f7c4abb0a592f3164ee0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_G.prefab using Guid(a7caa8247ab4f7c4abb0a592f3164ee0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2c94512f9e74f2d7e27f05195c2c6b55') in 0.0279731 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/FeatherQuillPen_A.fbx
  artifactKey: Guid(3184470c22da44741810c30016b53f20) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/FeatherQuillPen_A.fbx using Guid(3184470c22da44741810c30016b53f20) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e4ac2683e6b874588e159b68386cb317') in 0.0423464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_J.prefab
  artifactKey: Guid(410f0d4757ab4034ea796bf5c0ef48b2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_J.prefab using Guid(410f0d4757ab4034ea796bf5c0ef48b2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a87b6675ef7774283056911a35600524') in 0.0325949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_B.prefab
  artifactKey: Guid(9a9d6b811f0c5db4aa6d1f1c6fc3abcb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_B.prefab using Guid(9a9d6b811f0c5db4aa6d1f1c6fc3abcb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '017fc94b401bb6af7543e22344d89456') in 0.0329869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_G.fbx
  artifactKey: Guid(a27ca97742642f249bb8b4cdb35dfba6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_G.fbx using Guid(a27ca97742642f249bb8b4cdb35dfba6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f027da3374fc72879eb65f17142db397') in 0.0363966 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_B.prefab
  artifactKey: Guid(71473546ba57bd74aab8bc5c4b604a38) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_B.prefab using Guid(71473546ba57bd74aab8bc5c4b604a38) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '021e9fbacb1e5118afce97fef9fcae6c') in 0.0227574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Orange.prefab
  artifactKey: Guid(0079d0df83de83b46a18ab3526004216) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Orange.prefab using Guid(0079d0df83de83b46a18ab3526004216) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '584fdfa42e6e89a3a3d122e5e5d4f914') in 0.02582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BowlMedicine_A.fbx
  artifactKey: Guid(1708d62e2c30d254389f23668f1b9b22) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BowlMedicine_A.fbx using Guid(1708d62e2c30d254389f23668f1b9b22) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a35ad185ccbf4c855267afcea9626ce7') in 0.0446594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_E.prefab
  artifactKey: Guid(f0a8d1ebc1669e0449530d440ba4ffe5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_E.prefab using Guid(f0a8d1ebc1669e0449530d440ba4ffe5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e16bdf7cbcdc7603172ad2c7999c1d29') in 0.0296664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_L.prefab
  artifactKey: Guid(57e26dc055d854842834c89cc79396e9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_L.prefab using Guid(57e26dc055d854842834c89cc79396e9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '329ded24c77a1a391d70b170c60db50d') in 0.0410128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Script/Player/Guides/TONG_HOP_CAP_NHAT_CAMERA.md
  artifactKey: Guid(7edc25bcd69d0b24d99a61ba6197904e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/TONG_HOP_CAP_NHAT_CAMERA.md using Guid(7edc25bcd69d0b24d99a61ba6197904e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '180176ca3e61c9a9bea95b63b707f67e') in 0.0412931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_G.prefab
  artifactKey: Guid(94fec1719e190e343be766600ee0ecea) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_G.prefab using Guid(94fec1719e190e343be766600ee0ecea) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f781b08b87e9819848528630ab6d30e0') in 0.0513606 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_H.prefab
  artifactKey: Guid(b5d8524735b835e48b4c020c164fec41) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_H.prefab using Guid(b5d8524735b835e48b4c020c164fec41) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd308cda7239ac3e4c2323084fb6de322') in 0.0332166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Catapult.fbx
  artifactKey: Guid(619da3535cbae914aa1b67f67d4d38b8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Catapult.fbx using Guid(619da3535cbae914aa1b67f67d4d38b8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f9d782cac225d569b99502b60396154b') in 0.0602381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000199 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Flower_G.fbx
  artifactKey: Guid(8b12b9d407482bd4ca3596a2a23ec5eb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Flower_G.fbx using Guid(8b12b9d407482bd4ca3596a2a23ec5eb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e47246d51588bca641b4c7978bd4c8cc') in 0.0386315 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000138 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_D.prefab
  artifactKey: Guid(4679d96fbc9b79c4d93cc4409a42491b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_D.prefab using Guid(4679d96fbc9b79c4d93cc4409a42491b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '00ad6558e0935f02a9f06d29b403e67a') in 0.0341162 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPenInk_B.prefab
  artifactKey: Guid(29e5b75180a515a458237af3eb969094) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPenInk_B.prefab using Guid(29e5b75180a515a458237af3eb969094) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '17e2ebe721c13a77f6308ac8818a0cc5') in 0.0260559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Cheese_A.fbx
  artifactKey: Guid(5a147c55f0deb334598b056964e227a5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Cheese_A.fbx using Guid(5a147c55f0deb334598b056964e227a5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '99f72f26a19538187e9720b5b0e178d2') in 0.0375211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_B.fbx
  artifactKey: Guid(15ecf5ffdb3a6e34e871c39c9ad8f300) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_B.fbx using Guid(15ecf5ffdb3a6e34e871c39c9ad8f300) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f0271abe090c53c444a51ce399b1be87') in 0.0506856 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Mushroom_Green.fbx
  artifactKey: Guid(395506d3cda21e14e82ed3a031ce35a1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Mushroom_Green.fbx using Guid(395506d3cda21e14e82ed3a031ce35a1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f5a8a7c09a82f942f8d9c474996447a5') in 0.052222 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Signpost.prefab
  artifactKey: Guid(5db3b84674f6dfa4da406f4f6d905725) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Signpost.prefab using Guid(5db3b84674f6dfa4da406f4f6d905725) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efd0e32d6c8fd8b96bb5e56c299abc4d') in 0.0390114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_E.prefab
  artifactKey: Guid(b880f3052200d2b409b92092d09b2efb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_E.prefab using Guid(b880f3052200d2b409b92092d09b2efb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4f6adae552d47778b3db5965538a7a2d') in 0.0498836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_C.prefab
  artifactKey: Guid(295a8c7738d85ef49988563e40bc31cd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_C.prefab using Guid(295a8c7738d85ef49988563e40bc31cd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '526946fe493674663c6b6c12af8c3873') in 0.0230951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_E.fbx
  artifactKey: Guid(493a219217d6adc47bf12dfcd5f057c1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_E.fbx using Guid(493a219217d6adc47bf12dfcd5f057c1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '64e7f3533d420092e305e73ac29fa02c') in 0.0402429 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_C.fbx
  artifactKey: Guid(a2655c9eca1c2ed44ab50c80ed726f42) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_C.fbx using Guid(a2655c9eca1c2ed44ab50c80ed726f42) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5e4b58613fd2882b58a5189ed88a7466') in 0.0344409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_D.prefab
  artifactKey: Guid(cf413120d31deb543b5838e3a6fc9890) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_D.prefab using Guid(cf413120d31deb543b5838e3a6fc9890) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '158a7fc5750600fbbedee37376ca0a82') in 0.026648 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_A.prefab
  artifactKey: Guid(2eae18acc6a5dd047912b1c30bf2a765) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_A.prefab using Guid(2eae18acc6a5dd047912b1c30bf2a765) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ca2ac00165008922b84ed9dbd74fcbe4') in 0.029701 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_L.fbx
  artifactKey: Guid(33fca19fab0128a409c18b2a42df300c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_L.fbx using Guid(33fca19fab0128a409c18b2a42df300c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c4c27c66c22ec75c009fe74dd8e42e4b') in 0.0408075 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_C.prefab
  artifactKey: Guid(5683d73783d5565459e3e61d76bca99f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_C.prefab using Guid(5683d73783d5565459e3e61d76bca99f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8573383866407eed1b90827c7d745bbf') in 0.0410659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_C.prefab
  artifactKey: Guid(6799160d531a6b440a90b1d5fdeb4fd7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_C.prefab using Guid(6799160d531a6b440a90b1d5fdeb4fd7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8eda0d80877b28f7b8a65d7db1f008f') in 0.0277835 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch.prefab
  artifactKey: Guid(14affc4b0bf136045afa699ec132c943) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch.prefab using Guid(14affc4b0bf136045afa699ec132c943) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '63ab36b697f2ca3325d6f23ac940ff58') in 0.0267482 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials
  artifactKey: Guid(01da33cbb32df524e975c0a53c5c55ba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials using Guid(01da33cbb32df524e975c0a53c5c55ba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '399596485bbb50e430a39def0b2d377d') in 0.0214804 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_C.prefab
  artifactKey: Guid(2daa8b7bdb5112743a7816fc13aef758) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_C.prefab using Guid(2daa8b7bdb5112743a7816fc13aef758) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eea103e1eb26d9bb210a78e9fab8a5f0') in 0.0325653 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_C.prefab
  artifactKey: Guid(d3756bbc267910243920407c724a4328) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_C.prefab using Guid(d3756bbc267910243920407c724a4328) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '39180e9ff4de48e870fcf2b907a9760f') in 0.0283441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_L.prefab
  artifactKey: Guid(62979646132ec974b87fbd7ee128fbb5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_L.prefab using Guid(62979646132ec974b87fbd7ee128fbb5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a8c1ee4d363d0e8382d793cbd57068e0') in 0.0280789 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_R.prefab
  artifactKey: Guid(f9711398e795d1341988ba26acec254a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_R.prefab using Guid(f9711398e795d1341988ba26acec254a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8905838f3a6f2a82064b0348f50f55f1') in 0.0339784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_A.prefab
  artifactKey: Guid(7fecedaa3e5944a4b9deea930b0ff8da) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_A.prefab using Guid(7fecedaa3e5944a4b9deea930b0ff8da) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b1f10bcdf11beea79faa0dd93670bb2') in 0.0303794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_A.prefab
  artifactKey: Guid(fb2c29399c4aca842bae9b6e361b6f03) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_A.prefab using Guid(fb2c29399c4aca842bae9b6e361b6f03) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe2c1cc7aa629e55b7908f1a8f503011') in 0.0318448 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Frypan.fbx
  artifactKey: Guid(a072eec92d3769e4fbab3090e4f2c225) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Frypan.fbx using Guid(a072eec92d3769e4fbab3090e4f2c225) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9b0e7a431613f65c7c1386768a088753') in 0.0418483 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_B.fbx
  artifactKey: Guid(908e4fa8481d3924c8b62fd62a679b4e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_B.fbx using Guid(908e4fa8481d3924c8b62fd62a679b4e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '13e33b541eb377a6a162a12545c21549') in 0.0513625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Brown_A.fbx
  artifactKey: Guid(b826bf6ba8c38de40987c682d287bf4d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Brown_A.fbx using Guid(b826bf6ba8c38de40987c682d287bf4d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bd0dddae22b0e6ad5b4b17563a0cd420') in 0.0357129 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_C.fbx
  artifactKey: Guid(f7593cd7fa141ab4a8aeac6b28b4c25e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_C.fbx using Guid(f7593cd7fa141ab4a8aeac6b28b4c25e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8c876876e46c21da979f96ebac10f8e') in 0.0399535 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_J.fbx
  artifactKey: Guid(b94150523b2d5404daf1379fc1342f9e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_J.fbx using Guid(b94150523b2d5404daf1379fc1342f9e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2f14735def3fa7b3c7e193c9656227ef') in 0.0442902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Dish_A.prefab
  artifactKey: Guid(5461ae445fb61e843bf48440aa5e858e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Dish_A.prefab using Guid(5461ae445fb61e843bf48440aa5e858e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0eecba79304d7e5b823bcdc2343ac2db') in 0.0365581 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_E.prefab
  artifactKey: Guid(ed92e1eee8219e740a542cb16dc7bc6d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_E.prefab using Guid(ed92e1eee8219e740a542cb16dc7bc6d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '254fa9c89f03127ef11049350107bb4b') in 0.0344255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_O.prefab
  artifactKey: Guid(b7aee3d129cec3b46956b3ac917cb919) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_O.prefab using Guid(b7aee3d129cec3b46956b3ac917cb919) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e3f558a99ab6e220319dd6debf84559b') in 0.0426324 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Steel_E.fbx
  artifactKey: Guid(739fcc9ebc58fd14bb332dc32931e7b9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Steel_E.fbx using Guid(739fcc9ebc58fd14bb332dc32931e7b9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '916d76813031a676527a79ba376eb3fd') in 0.036025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_L.fbx
  artifactKey: Guid(98c653f000aa8184d8f6e31c1a05834b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_L.fbx using Guid(98c653f000aa8184d8f6e31c1a05834b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3f4b100fcb7848283ea930cb72e3e65') in 0.0357345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_D.prefab
  artifactKey: Guid(6a22363de62f0d14a9b93bc67e14bb79) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_D.prefab using Guid(6a22363de62f0d14a9b93bc67e14bb79) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c5deb346f481bfb5cf9f58ad0e51ac1c') in 0.0376256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Beerglass_A.prefab
  artifactKey: Guid(56e79185ed7af6e43bffefe15dfe30b5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Beerglass_A.prefab using Guid(56e79185ed7af6e43bffefe15dfe30b5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0ef9f65738da4cf2cecd93b0ee664e8f') in 0.0291542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_A.prefab
  artifactKey: Guid(1b2263e3fee7832408b6139c673cfab7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_A.prefab using Guid(1b2263e3fee7832408b6139c673cfab7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e23834dbc697feb02884d945aa74963') in 0.0312612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_B.prefab
  artifactKey: Guid(9a9d6b811f0c5db4aa6d1f1c6fc3abcb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_B.prefab using Guid(9a9d6b811f0c5db4aa6d1f1c6fc3abcb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eea41c00e28ec8ecb58f0309fbad8d6a') in 0.0446073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Watermelon_B.fbx
  artifactKey: Guid(aadd1abfae3ca034eb3516a45bb67e9f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Watermelon_B.fbx using Guid(aadd1abfae3ca034eb3516a45bb67e9f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7885ef760e33a38fedc3dd4109be25a0') in 0.0348507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_C.prefab
  artifactKey: Guid(a53975610aaccb74da71e55390097a77) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_C.prefab using Guid(a53975610aaccb74da71e55390097a77) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ae573ec692042e24973d9cd8f43a960f') in 0.0320456 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_Part_A.prefab
  artifactKey: Guid(130dcbb0cab492d49863701249d27bb3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_Part_A.prefab using Guid(130dcbb0cab492d49863701249d27bb3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab38c53e5c13726b278a0527522fbb44') in 0.0268504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_D.prefab
  artifactKey: Guid(378c0167555c5e04ab8928b8613d68bb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_D.prefab using Guid(378c0167555c5e04ab8928b8613d68bb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ae919011dd7daad4ea36697568c8a474') in 0.0288585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Orange.fbx
  artifactKey: Guid(b75787d933b905c4bb8b7d519daa7685) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Orange.fbx using Guid(b75787d933b905c4bb8b7d519daa7685) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '80ce7ae9053a7adfcb0e39bbf16d2ad6') in 0.032126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_A.prefab
  artifactKey: Guid(53d6a15837fee4b4da9cad56253677f4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_A.prefab using Guid(53d6a15837fee4b4da9cad56253677f4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2dbde8298b15795aa37e0e3eae475a18') in 0.0306569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_C.prefab
  artifactKey: Guid(c76dece1fb8d33c4ab80e35943b7d7d9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_C.prefab using Guid(c76dece1fb8d33c4ab80e35943b7d7d9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '337706ce9f1602ca5563dc73c3adb44f') in 0.0320568 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_A.fbx
  artifactKey: Guid(f247cbe4495e46845bab168e7399ca07) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_A.fbx using Guid(f247cbe4495e46845bab168e7399ca07) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '73ebaf148dff4c5a0a481784a8c93ee9') in 0.0478405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_J.prefab
  artifactKey: Guid(774c92e734dbd0648b8a8bc833a492f3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_J.prefab using Guid(774c92e734dbd0648b8a8bc833a492f3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ee06bd054e9dcf860800dbfc2a093d4') in 0.032195 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_K.prefab
  artifactKey: Guid(b6c1f3c25e5f21c45a2509c51d895734) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_K.prefab using Guid(b6c1f3c25e5f21c45a2509c51d895734) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3bc9a6e46b24d15422670c2d34ac7bdc') in 0.027329 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Egg_B.prefab
  artifactKey: Guid(a2f546d1a3e8fcd49aabf5471512146e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Egg_B.prefab using Guid(a2f546d1a3e8fcd49aabf5471512146e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2216d4bc4eb1d4bcd6f00d8e1c920a1f') in 0.0256967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_B.prefab
  artifactKey: Guid(f6f29766e26bc8a408696de615527d83) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_B.prefab using Guid(f6f29766e26bc8a408696de615527d83) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a1943c3ab56bd849e116a0d9c48eb7ec') in 0.0314517 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Brazier_B.fbx
  artifactKey: Guid(5b0982800601fa74fb33f02881d60021) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Brazier_B.fbx using Guid(5b0982800601fa74fb33f02881d60021) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e2bbe763bdc0334508c16bc64c87aca0') in 0.043356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000125 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Table_H.fbx
  artifactKey: Guid(31e3b56c1e2b9b846a84bcd1d8f9acf7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Table_H.fbx using Guid(31e3b56c1e2b9b846a84bcd1d8f9acf7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b7248e1fc09cb3aaa23410cd3a540154') in 0.0508597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ground_Tile_D.fbx
  artifactKey: Guid(53cef83b4657753498251b1bb039bc83) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ground_Tile_D.fbx using Guid(53cef83b4657753498251b1bb039bc83) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bafd051ffa16f3b32faafa9186f34ec1') in 0.0459436 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_E.fbx
  artifactKey: Guid(70286c4da744ef240a86d796a87bfbc2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_E.fbx using Guid(70286c4da744ef240a86d796a87bfbc2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd81eb9bfe8729652434256f8337bd3d7') in 0.0395575 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_H.fbx
  artifactKey: Guid(5c1612d8d8eef7841b5407daa6a3c97f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_H.fbx using Guid(5c1612d8d8eef7841b5407daa6a3c97f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4138a9a1a17b68c75a09efaf97b4a366') in 0.0437657 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Radish.fbx
  artifactKey: Guid(288bb17dc11e5804f8a5275f423676f7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Radish.fbx using Guid(288bb17dc11e5804f8a5275f423676f7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '45efb829ae9bc0d88c17605561a7f63a') in 0.0450667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_G.fbx
  artifactKey: Guid(1589be58362f1c74fa99348bae4003c8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_G.fbx using Guid(1589be58362f1c74fa99348bae4003c8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e1a9dc6419d2def905401a986b285f3d') in 0.0375396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_G.fbx
  artifactKey: Guid(a125795f8ce789e478eb190f69e0e4ec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_G.fbx using Guid(a125795f8ce789e478eb190f69e0e4ec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a99d94d3017e0d91f19b29f261eea07e') in 0.046878 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Mountain_A.fbx
  artifactKey: Guid(221ac9827d5fbfc459f4f21f60ff99ec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Mountain_A.fbx using Guid(221ac9827d5fbfc459f4f21f60ff99ec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7abb5441cc6c62d5f39294e35ec2c8f9') in 0.0410159 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_E.prefab
  artifactKey: Guid(035301fbe2361454e8e528b8f529f41b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_E.prefab using Guid(035301fbe2361454e8e528b8f529f41b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a34e5c70aec0c8bb380ecda111c7aa3') in 0.0297467 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ink.prefab
  artifactKey: Guid(56fd174b834921940b8aec1b03c37d13) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ink.prefab using Guid(56fd174b834921940b8aec1b03c37d13) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8befe84463fa590f895b339b9d1c1014') in 0.0411897 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_A.prefab
  artifactKey: Guid(23945059dcc2918449ccdc9741e0f4b9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_A.prefab using Guid(23945059dcc2918449ccdc9741e0f4b9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa1fe1b3e91bcb7d9d3968bbdd154278') in 0.0324922 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Rock_E.fbx
  artifactKey: Guid(646aab078f1a7fd409bc895bdaed4622) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Rock_E.fbx using Guid(646aab078f1a7fd409bc895bdaed4622) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79325c6494b34bc0702f54d6a3f02929') in 0.0377549 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_C.prefab
  artifactKey: Guid(189e84ff372d3254e9ab89cd5a5255a7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Cabinet_C.prefab using Guid(189e84ff372d3254e9ab89cd5a5255a7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6ae3ad4fd0e90af5b7ed37effcb0a74a') in 0.0485233 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_A.prefab
  artifactKey: Guid(b1f8717edec570a47b6058dbffa24248) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_A.prefab using Guid(b1f8717edec570a47b6058dbffa24248) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '987ac7021c0ddedbecaa280d8c372aa9') in 0.0538269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_E.fbx
  artifactKey: Guid(f99c0e718052ca245ac1d459700a176e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_E.fbx using Guid(f99c0e718052ca245ac1d459700a176e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '982351e25e6e53c40d07cccae99f6ab7') in 0.0624708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_K.fbx
  artifactKey: Guid(7f30079cefafb694fa61fc3debcf29f8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_K.fbx using Guid(7f30079cefafb694fa61fc3debcf29f8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5784214ec7a5f472f7c521b369bd39a2') in 0.0432338 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

Editor requested this worker to shutdown with reason: balancing worker count downwards
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0