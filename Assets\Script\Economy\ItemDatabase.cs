using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace EconomySystem
{
    /// <summary>
    /// Class chứa thông tin vật phẩm trong cửa hàng
    /// </summary>
    [System.Serializable]
    public class ShopItemData
    {
        [SerializeField] private Item item;
        [SerializeField] private bool coTrongCuaHang = true;
        [SerializeField] private int thuTuHienThi = 0;
        [SerializeField] private bool vatPhamMoi = false;
        [SerializeField] private bool vatPhamNoiBat = false;

        public Item Item => item;
        public bool CoTrongCuaHang => coTrongCuaHang;
        public int ThuTuHienThi => thuTuHienThi;
        public bool VatPhamMoi => vatPhamMoi;
        public bool VatPhamNoiBat => vatPhamNoiBat;

        public ShopItemData(Item item)
        {
            this.item = item;
            this.coTrongCuaHang = true;
            this.thuTuHienThi = 0;
            this.vatPhamMoi = false;
            this.vatPhamNoiBat = false;
        }
    }

    /// <summary>
    /// ScriptableObject chứa database tất cả vật phẩm trong game
    /// </summary>
    [CreateAssetMenu(fileName = "ItemDatabase", menuName = "Economy System/Item Database")]
    public class ItemDatabase : ScriptableObject
    {
        [Header("Cài Đặt Database")]
        [SerializeField] private List<ShopItemData> danhSachVatPham = new List<ShopItemData>();
        [SerializeField] private bool tuDongTaoID = true;

        public List<ShopItemData> DanhSachVatPham => danhSachVatPham;

        /// <summary>
        /// Lấy vật phẩm theo ID
        /// </summary>
        public Item LayVatPhamTheoID(string id)
        {
            var shopItem = danhSachVatPham.FirstOrDefault(x => x.Item.ID == id);
            return shopItem?.Item;
        }

        /// <summary>
        /// Lấy tất cả vật phẩm có trong cửa hàng
        /// </summary>
        public List<Item> LayDanhSachVatPhamBan()
        {
            return danhSachVatPham
                .Where(x => x.CoTrongCuaHang)
                .OrderBy(x => x.ThuTuHienThi)
                .Select(x => x.Item)
                .ToList();
        }

        /// <summary>
        /// Lấy vật phẩm theo loại
        /// </summary>
        public List<Item> LayVatPhamTheoLoai(ItemType loai)
        {
            return danhSachVatPham
                .Where(x => x.Item.LoaiVatPham == loai && x.CoTrongCuaHang)
                .OrderBy(x => x.ThuTuHienThi)
                .Select(x => x.Item)
                .ToList();
        }

        /// <summary>
        /// Lấy vật phẩm theo độ hiếm
        /// </summary>
        public List<Item> LayVatPhamTheoDoHiem(ItemRarity doHiem)
        {
            return danhSachVatPham
                .Where(x => x.Item.DoHiem == doHiem && x.CoTrongCuaHang)
                .OrderBy(x => x.ThuTuHienThi)
                .Select(x => x.Item)
                .ToList();
        }

        /// <summary>
        /// Tìm kiếm vật phẩm theo tên
        /// </summary>
        public List<Item> TimKiemVatPham(string tuKhoa)
        {
            if (string.IsNullOrEmpty(tuKhoa))
                return LayDanhSachVatPhamBan();

            tuKhoa = tuKhoa.ToLower();
            return danhSachVatPham
                .Where(x => x.CoTrongCuaHang && 
                           (x.Item.TenVatPham.ToLower().Contains(tuKhoa) || 
                            x.Item.MoTa.ToLower().Contains(tuKhoa)))
                .OrderBy(x => x.ThuTuHienThi)
                .Select(x => x.Item)
                .ToList();
        }

        /// <summary>
        /// Lấy vật phẩm mới
        /// </summary>
        public List<Item> LayVatPhamMoi()
        {
            return danhSachVatPham
                .Where(x => x.VatPhamMoi && x.CoTrongCuaHang)
                .OrderBy(x => x.ThuTuHienThi)
                .Select(x => x.Item)
                .ToList();
        }

        /// <summary>
        /// Lấy vật phẩm nổi bật
        /// </summary>
        public List<Item> LayVatPhamNoiBat()
        {
            return danhSachVatPham
                .Where(x => x.VatPhamNoiBat && x.CoTrongCuaHang)
                .OrderBy(x => x.ThuTuHienThi)
                .Select(x => x.Item)
                .ToList();
        }

        /// <summary>
        /// Kiểm tra vật phẩm có tồn tại không
        /// </summary>
        public bool KiemTraVatPhamTonTai(string id)
        {
            return danhSachVatPham.Any(x => x.Item.ID == id);
        }

        /// <summary>
        /// Thêm vật phẩm mới vào database
        /// </summary>
        public void ThemVatPham(Item item)
        {
            if (item == null || KiemTraVatPhamTonTai(item.ID))
                return;

            danhSachVatPham.Add(new ShopItemData(item));
            
            #if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            #endif
        }

        /// <summary>
        /// Xóa vật phẩm khỏi database
        /// </summary>
        public bool XoaVatPham(string id)
        {
            var shopItem = danhSachVatPham.FirstOrDefault(x => x.Item.ID == id);
            if (shopItem != null)
            {
                danhSachVatPham.Remove(shopItem);
                
                #if UNITY_EDITOR
                UnityEditor.EditorUtility.SetDirty(this);
                #endif
                return true;
            }
            return false;
        }

        /// <summary>
        /// Lấy tổng số vật phẩm trong database
        /// </summary>
        public int LayTongSoVatPham()
        {
            return danhSachVatPham.Count;
        }

        /// <summary>
        /// Lấy tổng số vật phẩm có trong cửa hàng
        /// </summary>
        public int LayTongSoVatPhamBan()
        {
            return danhSachVatPham.Count(x => x.CoTrongCuaHang);
        }

        /// <summary>
        /// Validate database
        /// </summary>
        public void KiemTraDatabase()
        {
            for (int i = danhSachVatPham.Count - 1; i >= 0; i--)
            {
                if (danhSachVatPham[i].Item == null || !danhSachVatPham[i].Item.KiemTraHopLe())
                {
                    Debug.LogWarning($"Vật phẩm không hợp lệ tại index {i}, đã xóa khỏi database");
                    danhSachVatPham.RemoveAt(i);
                }
            }

            // Kiểm tra ID trùng lặp
            var duplicateIDs = danhSachVatPham
                .GroupBy(x => x.Item.ID)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key);

            foreach (var duplicateID in duplicateIDs)
            {
                Debug.LogError($"Phát hiện ID trùng lặp: {duplicateID}");
            }

            #if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
            #endif
        }

        /// <summary>
        /// Tạo ID tự động cho vật phẩm mới
        /// </summary>
        public string TaoIDTuDong()
        {
            if (!tuDongTaoID)
                return System.Guid.NewGuid().ToString();

            int maxNumber = 0;
            foreach (var shopItem in danhSachVatPham)
            {
                if (int.TryParse(shopItem.Item.ID, out int number))
                {
                    maxNumber = Mathf.Max(maxNumber, number);
                }
            }

            return (maxNumber + 1).ToString("D2");
        }

        #if UNITY_EDITOR
        /// <summary>
        /// Context menu để kiểm tra database
        /// </summary>
        [UnityEditor.MenuItem("CONTEXT/ItemDatabase/Kiểm Tra Database")]
        private static void KiemTraDatabaseMenu(UnityEditor.MenuCommand command)
        {
            ItemDatabase database = (ItemDatabase)command.context;
            database.KiemTraDatabase();
            Debug.Log("Đã kiểm tra database xong!");
        }

        /// <summary>
        /// Context menu để tạo vật phẩm mẫu
        /// </summary>
        [UnityEditor.MenuItem("CONTEXT/ItemDatabase/Tạo Vật Phẩm Mẫu")]
        private static void TaoVatPhamMauMenu(UnityEditor.MenuCommand command)
        {
            ItemDatabase database = (ItemDatabase)command.context;
            database.TaoVatPhamMau();
            Debug.Log("Đã tạo vật phẩm mẫu!");
        }
        #endif

        /// <summary>
        /// Tạo một số vật phẩm mẫu để test
        /// </summary>
        private void TaoVatPhamMau()
        {
            // Tạo vật phẩm mẫu
            var vatPhamMau = new List<Item>
            {
                new Item("wood", "Gỗ", "Vật liệu cơ bản để xây dựng", 5, 2),
                new Item("stone", "Đá", "Vật liệu bền chắc", 8, 3),
                new Item("iron", "Sắt", "Kim loại quý giá", 15, 7),
                new Item("sword", "Kiếm", "Vũ khí cận chiến", 50, 25),
                new Item("potion", "Thuốc Hồi Máu", "Hồi phục sức khỏe", 20, 10)
            };

            foreach (var item in vatPhamMau)
            {
                if (!KiemTraVatPhamTonTai(item.ID))
                {
                    ThemVatPham(item);
                }
            }
        }
    }
}
