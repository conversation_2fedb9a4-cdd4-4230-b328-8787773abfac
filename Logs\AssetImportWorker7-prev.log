Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker7
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker7.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20660]  Target information:

Player connection [20660]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2185486485 [EditorId] 2185486485 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20660] Host joined multi-casting on [***********:54997]...
Player connection [20660] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56028
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004750 seconds.
- Loaded All Assemblies, in  1.258 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.046 seconds
Domain Reload Profiling: 2304ms
	BeginReloadAssembly (539ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (4ms)
	RebuildCommonClasses (128ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (98ms)
	LoadAllAssembliesAndSetupDomain (466ms)
		LoadAssemblies (532ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (460ms)
			TypeCache.Refresh (458ms)
				TypeCache.ScanAssembly (424ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1046ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (957ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (49ms)
			SetLoadedEditorAssemblies (35ms)
			BeforeProcessingInitializeOnLoad (388ms)
			ProcessInitializeOnLoadAttributes (376ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.241 seconds
Refreshing native plugins compatible for Editor in 1.64 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.794 seconds
Domain Reload Profiling: 4033ms
	BeginReloadAssembly (358ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (89ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (80ms)
	LoadAllAssembliesAndSetupDomain (1684ms)
		LoadAssemblies (1077ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (768ms)
			TypeCache.Refresh (650ms)
				TypeCache.ScanAssembly (613ms)
			BuildScriptInfoCaches (95ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1795ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1390ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (455ms)
			ProcessInitializeOnLoadAttributes (808ms)
			ProcessInitializeOnLoadMethodAttributes (115ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.17 seconds
Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (8.4 MB). Loaded Objects now: 7294.
Memory consumption went from 165.4 MB to 156.9 MB.
Total: 105.994500 ms (FindLiveObjects: 1.767800 ms CreateObjectMapping: 2.189000 ms MarkObjects: 13.447400 ms  DeleteObjects: 88.588200 ms)

========================================================================
Received Import Request.
  Time since last request: 103331.841734 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bed_A.prefab
  artifactKey: Guid(0143f16482e5ffc4d93d326679b1306c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bed_A.prefab using Guid(0143f16482e5ffc4d93d326679b1306c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '392d62ef234bc183f4d6dc8ade5ca9ec') in 0.4912078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Dish_A.prefab
  artifactKey: Guid(5461ae445fb61e843bf48440aa5e858e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Dish_A.prefab using Guid(5461ae445fb61e843bf48440aa5e858e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a6f9cc0e8750f3d57746f4bfbb97c554') in 0.0315916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Torch_Steel_C.fbx
  artifactKey: Guid(ea9671a7bff6fbd469dbbc3d8214bcc2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Torch_Steel_C.fbx using Guid(ea9671a7bff6fbd469dbbc3d8214bcc2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa308e5b2846befb9d68b903220e81b6') in 0.2298378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_C.prefab
  artifactKey: Guid(67b30c1f1afb35e40ad6a1dffecc9cf5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_C.prefab using Guid(67b30c1f1afb35e40ad6a1dffecc9cf5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a31856b6e0371ab29430febf63f07954') in 0.0321662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_H.prefab
  artifactKey: Guid(fbda5d97dae8649408b547b269d97700) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_H.prefab using Guid(fbda5d97dae8649408b547b269d97700) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9ed08ba3a3d618f9937e63bddafe943a') in 0.0253715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_A.fbx
  artifactKey: Guid(b317e5398cbaaa144a9702375fef0c78) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_A.fbx using Guid(b317e5398cbaaa144a9702375fef0c78) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ce195444848e9615dbd43d8a6e66fb11') in 0.0350197 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Candelabrum_B.prefab
  artifactKey: Guid(187b76555bbf99c438b00e687b7f99a9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Candelabrum_B.prefab using Guid(187b76555bbf99c438b00e687b7f99a9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dd0778aab11626b5972dbf4549304ba3') in 0.0204358 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_H.fbx
  artifactKey: Guid(f6fc7c54d04110f40ac5aeb120363314) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_H.fbx using Guid(f6fc7c54d04110f40ac5aeb120363314) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'db22b67706f85cb9175f848602150832') in 0.0326917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Brazier_A.prefab
  artifactKey: Guid(a0a7382262a53914581c916ec0a0270a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Brazier_A.prefab using Guid(a0a7382262a53914581c916ec0a0270a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'eb41edee307960defb3ef2f6035bfbed') in 0.0248944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPenInk_A.prefab
  artifactKey: Guid(7900cf281ccfe9648a3dc235f84bc3df) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPenInk_A.prefab using Guid(7900cf281ccfe9648a3dc235f84bc3df) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bab508b606d7b7d12cc712b3e62dc886') in 0.0240349 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_E.prefab
  artifactKey: Guid(035301fbe2361454e8e528b8f529f41b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_E.prefab using Guid(035301fbe2361454e8e528b8f529f41b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2321213b9ca88d687cbda1fd6a900b7c') in 0.0240108 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_A.prefab
  artifactKey: Guid(c1348a9d1518f554ab6ace3495c54d68) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_A.prefab using Guid(c1348a9d1518f554ab6ace3495c54d68) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6026ff13b34774b0841bf84ab59abed5') in 0.0205878 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_Q.fbx
  artifactKey: Guid(f579aed46ab119246b63d038c6ad6f07) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_Q.fbx using Guid(f579aed46ab119246b63d038c6ad6f07) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e7af406b6b750f95fb5054d5b25f234d') in 0.0401431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Frypan.prefab
  artifactKey: Guid(07b9b03bb5d838446bf1a3b77513debc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Frypan.prefab using Guid(07b9b03bb5d838446bf1a3b77513debc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9e0cdde87b7ff9b37a645885d2f3254a') in 0.0280563 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_C.prefab
  artifactKey: Guid(31f591ae96f0c9846b6739c494ff4167) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_C.prefab using Guid(31f591ae96f0c9846b6739c494ff4167) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a65b4536c2f623b78b4378454a4fe807') in 0.0350071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_D.prefab
  artifactKey: Guid(c3cb99ce1a3619641b4e065bfba8447a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_D.prefab using Guid(c3cb99ce1a3619641b4e065bfba8447a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '482c8f4729207db5f01c52f62b22385a') in 0.034659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_N.prefab
  artifactKey: Guid(93311290ea7e1954a8a3148f27395a2a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_N.prefab using Guid(93311290ea7e1954a8a3148f27395a2a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '918be22b3ecc0a8219b35ad82c51c0a4') in 0.0228779 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_C.prefab
  artifactKey: Guid(a40a0c1c91b73e44e85aa90673e26f44) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_C.prefab using Guid(a40a0c1c91b73e44e85aa90673e26f44) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4f813599125308d0777b2610f68340ee') in 0.0219652 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ground_Tile_B.fbx
  artifactKey: Guid(16906d6e7dc83784591ff061edbb4eb8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ground_Tile_B.fbx using Guid(16906d6e7dc83784591ff061edbb4eb8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8007dc3f73b5aeafca418d84a9852f96') in 0.0400792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_K.fbx
  artifactKey: Guid(6f2140a9c0139f44ca9f762aaaea4155) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_K.fbx using Guid(6f2140a9c0139f44ca9f762aaaea4155) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a379a805aff1ab12a547515bada2598f') in 0.028597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_D.prefab
  artifactKey: Guid(8b7f139a58600f24b9db121e1e24cca9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_D.prefab using Guid(8b7f139a58600f24b9db121e1e24cca9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '164d56c6906538f47f92bdd4210f7dee') in 0.0283604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass.prefab
  artifactKey: Guid(83154d7c8aa64c3428059e75ea272d27) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass.prefab using Guid(83154d7c8aa64c3428059e75ea272d27) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '96faf6e4c79772c7b03ef6087af4709d') in 0.0290355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_H.prefab
  artifactKey: Guid(62e6834c0be2815498d662814fdb993b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_H.prefab using Guid(62e6834c0be2815498d662814fdb993b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bdd7d28c4ce7aa5818a6da15becb6c23') in 0.0241076 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_B.prefab
  artifactKey: Guid(a179bdc47b356b34b8c7eda14db709a4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_B.prefab using Guid(a179bdc47b356b34b8c7eda14db709a4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a3eca227446b80fd01d9b3660272f5d3') in 0.0220283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_A.prefab
  artifactKey: Guid(9c710ca8038128a48a7ca2572cf21ed3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_A.prefab using Guid(9c710ca8038128a48a7ca2572cf21ed3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ccc05160c3beb10ee8cd773e2cb81475') in 0.0266782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_A.prefab
  artifactKey: Guid(228d2a930604eec449ac4d997d1bfb03) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_A.prefab using Guid(228d2a930604eec449ac4d997d1bfb03) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5e81c73cb929eefbaf49425472b4eebc') in 0.0265435 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_B.prefab
  artifactKey: Guid(a9db87a94740b7d48a44dd14c39b1935) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_B.prefab using Guid(a9db87a94740b7d48a44dd14c39b1935) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0338619fe5568fac6bec6c3eaaeec2da') in 0.0296005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_G.fbx
  artifactKey: Guid(916a95f0c8be1db43b71ef9994dae4f0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_G.fbx using Guid(916a95f0c8be1db43b71ef9994dae4f0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fcafe6c0d8e9ce3884b1cbf5f9173039') in 0.0601668 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_C.prefab
  artifactKey: Guid(2f7df7c24910f3541ab7411ecbdf6fed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_C.prefab using Guid(2f7df7c24910f3541ab7411ecbdf6fed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7ab58fa86cc3df260ee7de9f9bef7389') in 0.0667961 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_C.prefab
  artifactKey: Guid(a227505c56765be4cabdc802c8d5e969) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_C.prefab using Guid(a227505c56765be4cabdc802c8d5e969) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b4cd585f28e1baa17067753f1166c9ab') in 0.0214878 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_A.prefab
  artifactKey: Guid(e0f920db2ed60524caafc6b6ed1c7e26) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_A.prefab using Guid(e0f920db2ed60524caafc6b6ed1c7e26) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ed7253541b8aa202268e3f5bafc73dc1') in 0.0330082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_A.prefab
  artifactKey: Guid(86e8f1324766f5343ae7e930a7782c2f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_A.prefab using Guid(86e8f1324766f5343ae7e930a7782c2f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '33e0926affdd45032e6b152876d594f2') in 0.0247323 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_F.prefab
  artifactKey: Guid(6d888d1b7f74c9a44acb3604c1830aeb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_F.prefab using Guid(6d888d1b7f74c9a44acb3604c1830aeb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '55f2f38f32c94739bb97c51139b54747') in 0.0277642 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_C.prefab
  artifactKey: Guid(f187fcd4ae71da949be7c6593e2a56ed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_C.prefab using Guid(f187fcd4ae71da949be7c6593e2a56ed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '00e31aca8da76ff15174276f4b39d57b') in 0.028944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_D.prefab
  artifactKey: Guid(9a83dd3d977a04148858ceb5a38eed3c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_D.prefab using Guid(9a83dd3d977a04148858ceb5a38eed3c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8c6dbdbb2d791ff4f220dd9684428ac0') in 0.0304974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_J.fbx
  artifactKey: Guid(ea89658c621e7c7418d55253106d4d94) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_J.fbx using Guid(ea89658c621e7c7418d55253106d4d94) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab5ee92eecfd2fe6f5d9a05cd83cb101') in 0.0369734 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000116 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bucket_Wood_A.fbx
  artifactKey: Guid(36ac36fdff140b64daaf7ef0d958af46) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bucket_Wood_A.fbx using Guid(36ac36fdff140b64daaf7ef0d958af46) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6aa321b161bcc12107a75e43120f4e1e') in 0.0453123 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_E.fbx
  artifactKey: Guid(72ed151fe7c863c44807da7ee6534845) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_E.fbx using Guid(72ed151fe7c863c44807da7ee6534845) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '30a774e6246d60a1dcf91ad0e3598758') in 0.0527501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_B.prefab
  artifactKey: Guid(7672131665f1e6148a721f4c88fd5eac) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_B.prefab using Guid(7672131665f1e6148a721f4c88fd5eac) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '275ed0b9d35bd3646295b613a4102edb') in 0.0299056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Mushroom_Red.fbx
  artifactKey: Guid(84c0ef54550756340bd463285a325140) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Mushroom_Red.fbx using Guid(84c0ef54550756340bd463285a325140) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '443b5955907bb6fea5986792b892c0b2') in 0.039808 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Signpost.prefab
  artifactKey: Guid(5db3b84674f6dfa4da406f4f6d905725) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Signpost.prefab using Guid(5db3b84674f6dfa4da406f4f6d905725) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '097d26db0710c477d0385948b1800362') in 0.0336385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Cup_B.fbx
  artifactKey: Guid(e38679716da73b54ab4342463e825707) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Cup_B.fbx using Guid(e38679716da73b54ab4342463e825707) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bacf173c37b2f1f13b09b8ea549afbe4') in 0.0456234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_E.prefab
  artifactKey: Guid(c35313a39dae0cd4fae5343609e34d5e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_E.prefab using Guid(c35313a39dae0cd4fae5343609e34d5e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0118959696066d14f87719a88882198d') in 0.0399112 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_B.prefab
  artifactKey: Guid(b852dcb096fb36443923c97dafb2dcd0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_B.prefab using Guid(b852dcb096fb36443923c97dafb2dcd0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad508806bb240efe58e70bfd78cfea06') in 0.0444725 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_D.prefab
  artifactKey: Guid(23d4ba00641b2da41a09dad7fe0176d8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_D.prefab using Guid(23d4ba00641b2da41a09dad7fe0176d8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'eaabdbf1ce488c365dd4ae7fb108945e') in 0.0212946 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_C.prefab
  artifactKey: Guid(654b16543fc7e304980b8fda8c8b5a4c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_C.prefab using Guid(654b16543fc7e304980b8fda8c8b5a4c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dcd135ae31226e11c38323560019cca5') in 0.03259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Black.prefab
  artifactKey: Guid(3ffad265b71c05d458b846b5f8472a46) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Black.prefab using Guid(3ffad265b71c05d458b846b5f8472a46) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '130bfd4d1a8ccf9d1389ebe670475dab') in 0.0259146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_A.prefab
  artifactKey: Guid(5579bf428dcf7a247be2d5ecacaf212d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_A.prefab using Guid(5579bf428dcf7a247be2d5ecacaf212d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '753f6ee4c71d359d16ac0caaaa224d1b') in 0.0334951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_B.prefab
  artifactKey: Guid(0e80046ae8097354a89687c471ba3060) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_B.prefab using Guid(0e80046ae8097354a89687c471ba3060) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a32ca8ce88f60dcaf0d7577710462fd3') in 0.0363295 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fire_C.fbx
  artifactKey: Guid(786efd142b48eaa4ab431330828184e0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fire_C.fbx using Guid(786efd142b48eaa4ab431330828184e0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d88efda5c6dfa543b43f4d856e602a2') in 0.0418211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000176 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_E.fbx
  artifactKey: Guid(bb8ff87994f3fe2439fc6969d91a10c5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_E.fbx using Guid(bb8ff87994f3fe2439fc6969d91a10c5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a87775a22382ed8c83d47555a6870611') in 0.0398747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Well.prefab
  artifactKey: Guid(e24cfae59a3c17d489b0a6e65e74a149) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Well.prefab using Guid(e24cfae59a3c17d489b0a6e65e74a149) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '10e67031bc2de73494e698deaa41428d') in 0.0257686 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_E.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_E.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '731aec1a2fd6163b37bbafb4a7452b35') in 0.0262836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Salt.prefab
  artifactKey: Guid(7ae105c058533e540b99e2261b05ae4c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Salt.prefab using Guid(7ae105c058533e540b99e2261b05ae4c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fbd1cc927d7e757e1b7ab53a6a0a573c') in 0.028194 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bellows.fbx
  artifactKey: Guid(40604ebc1feac804ebd9e52a6d37bcc2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bellows.fbx using Guid(40604ebc1feac804ebd9e52a6d37bcc2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7e7aa9e3f5d7c22cd7a929a2296cea49') in 0.0331624 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_H.fbx
  artifactKey: Guid(01fbc059998a3c84c8cb1c1abe49efae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_H.fbx using Guid(01fbc059998a3c84c8cb1c1abe49efae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '99071eafb31478d7858046d5e1391c4e') in 0.0393741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000081 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Greenonion.prefab
  artifactKey: Guid(c7bfd9ceee8c4834d9f54579a258387d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Greenonion.prefab using Guid(c7bfd9ceee8c4834d9f54579a258387d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'db85b29a0d742bccfb3b4c5fcd709efa') in 0.0366071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Chaff_B.fbx
  artifactKey: Guid(f7bd66a04e79e6749bd16f7a8627c629) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Chaff_B.fbx using Guid(f7bd66a04e79e6749bd16f7a8627c629) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e919ff78ae2ecf37440f1da97d612a1c') in 0.0373272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_B.prefab
  artifactKey: Guid(81809bc738f2464448cf8bdca45ba8e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_B.prefab using Guid(81809bc738f2464448cf8bdca45ba8e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '726b19ea88293e0b72286b3e86676621') in 0.0300186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_E.prefab
  artifactKey: Guid(14e14126775dcd14a9f7f94650899f87) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_E.prefab using Guid(14e14126775dcd14a9f7f94650899f87) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ddbf1c1919011f4d9509d51be82d9ee4') in 0.0249208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_A.prefab
  artifactKey: Guid(25d5f42405738fb43947296a1e0ed7ad) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_A.prefab using Guid(25d5f42405738fb43947296a1e0ed7ad) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3497c17f2d723d0eaa4da176f8732843') in 0.0344987 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Salt.prefab
  artifactKey: Guid(7ae105c058533e540b99e2261b05ae4c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Salt.prefab using Guid(7ae105c058533e540b99e2261b05ae4c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cbb1e7eaa45afea0c24929a4f16db714') in 0.0282196 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_G.fbx
  artifactKey: Guid(b249e472bbb1a9448b71cdbc9c4590df) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_G.fbx using Guid(b249e472bbb1a9448b71cdbc9c4590df) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bee32bf8e5f8f9acf149e8cfde0f93fb') in 0.0403935 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Brown.fbx
  artifactKey: Guid(b596cceb22f3c5549aaf7ba244507270) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Brown.fbx using Guid(b596cceb22f3c5549aaf7ba244507270) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '79e48c76a23863ebb174363518b84e70') in 0.0444168 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bed_A.prefab
  artifactKey: Guid(0143f16482e5ffc4d93d326679b1306c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bed_A.prefab using Guid(0143f16482e5ffc4d93d326679b1306c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '14da7d51fc4596cb5062ca14f8fc9d57') in 0.026234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Hourglass_A.fbx
  artifactKey: Guid(cada6118bb9695c4580850ea67f899c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Hourglass_A.fbx using Guid(cada6118bb9695c4580850ea67f899c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '180fa9cef3377ce056126a995de71699') in 0.0384697 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_H.fbx
  artifactKey: Guid(8ecac2c118ded4541a8a0089b13144fe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_H.fbx using Guid(8ecac2c118ded4541a8a0089b13144fe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b60a5c47f3750dc686c8718b649dfb4b') in 0.048604 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_E.prefab
  artifactKey: Guid(f3bd087e894a5124c98c66a99ed4fc5b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_E.prefab using Guid(f3bd087e894a5124c98c66a99ed4fc5b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '491dd77b1f411817dc0b7e46964dda10') in 0.0294417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Frypan.prefab
  artifactKey: Guid(07b9b03bb5d838446bf1a3b77513debc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Frypan.prefab using Guid(07b9b03bb5d838446bf1a3b77513debc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad0ed53c6d0da25b0f8a4b08fae665a3') in 0.0370622 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_B.prefab
  artifactKey: Guid(22433c843850954439a7ff47a3c83111) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_B.prefab using Guid(22433c843850954439a7ff47a3c83111) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ccad4747117bbc8fc1ebf799ce122e24') in 0.0432121 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Rock_D.fbx
  artifactKey: Guid(a3bd97c738d68d942b174cfa805a1d4f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Rock_D.fbx using Guid(a3bd97c738d68d942b174cfa805a1d4f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd33c11b56053c544291094e40e4661cd') in 0.043818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_G.prefab
  artifactKey: Guid(35834a89c5743454fab1db36463ee40e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_G.prefab using Guid(35834a89c5743454fab1db36463ee40e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70f5c65da9c4e612f1f0702d1b9a59ef') in 0.0290649 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_B.prefab
  artifactKey: Guid(76fbc51f1958792499113412ca396ff7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_B.prefab using Guid(76fbc51f1958792499113412ca396ff7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17b96a4818715e0006bf64da5bffccbf') in 0.0263572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_Table_B.fbx
  artifactKey: Guid(f19c245532631314b9c869c75c6cd76a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_Table_B.fbx using Guid(f19c245532631314b9c869c75c6cd76a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5d0e87dce63374e856b84bb8c4def354') in 0.0319497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_B.prefab
  artifactKey: Guid(aa5d3031a928b194694da7084baa15c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_B.prefab using Guid(aa5d3031a928b194694da7084baa15c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87e6c18661aa1baced3c2baf3ae7669c') in 0.0276506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Salt.fbx
  artifactKey: Guid(dfc0a5725f357f04caadaec760e1a2ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Salt.fbx using Guid(dfc0a5725f357f04caadaec760e1a2ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d677f2471328cc87a16703c812041cd') in 0.0470986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_E.fbx
  artifactKey: Guid(c9cc36e390f77c0408b533a87618479c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_E.fbx using Guid(c9cc36e390f77c0408b533a87618479c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d5981b7c96f6eebae6e8784964a5171') in 0.0378048 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_B.prefab
  artifactKey: Guid(7ae78b65dda913b46935796ea11ff6fd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_B.prefab using Guid(7ae78b65dda913b46935796ea11ff6fd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '54f553b5b22daf187d8f69f03fffab99') in 0.0209366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_H.prefab
  artifactKey: Guid(f1ed631a750e7434f9e744021e8d3668) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_H.prefab using Guid(f1ed631a750e7434f9e744021e8d3668) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab5d23cd36a782f47faf7c49a25e6539') in 0.0315556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Cabinet_C.fbx
  artifactKey: Guid(25c75de62ec9a29438410a8b24a1623f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Cabinet_C.fbx using Guid(25c75de62ec9a29438410a8b24a1623f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '179042e40f03a45116976d9a32787cd8') in 0.0359539 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Paprika_Yellow.fbx
  artifactKey: Guid(b85652e136d00d846a6b40c34bde61cb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Paprika_Yellow.fbx using Guid(b85652e136d00d846a6b40c34bde61cb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7732584b6ed913617787114a4fa1ea52') in 0.0474748 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Onion_Yellow.fbx
  artifactKey: Guid(001dfc66aef73774f92599bab6379eb6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Onion_Yellow.fbx using Guid(001dfc66aef73774f92599bab6379eb6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '297d2b5db8bcbe06e30b961594aa71bb') in 0.0488792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_A.prefab
  artifactKey: Guid(9fdff433abb1c52498f4a03ae2421ba6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_A.prefab using Guid(9fdff433abb1c52498f4a03ae2421ba6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2317c625acf3fad263bc51ca2962925a') in 0.0477831 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_G.prefab
  artifactKey: Guid(3f5c62ff4ad1dfd408c8c5577da369ec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_G.prefab using Guid(3f5c62ff4ad1dfd408c8c5577da369ec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '841a2f9333513587cc82ae95ac6fe924') in 0.0392899 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000247 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_D.fbx
  artifactKey: Guid(8ce0cadb4d0ea0646af955ecb8012a7a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_D.fbx using Guid(8ce0cadb4d0ea0646af955ecb8012a7a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7b1da4fa9a763e787f351e6aaaa27f9d') in 0.0403025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ground_Tile_C.fbx
  artifactKey: Guid(0da5a1a71ac4ce34b8dee1fca432016f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ground_Tile_C.fbx using Guid(0da5a1a71ac4ce34b8dee1fca432016f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '055778cb05a31c094471e45e85b643a5') in 0.0464224 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Meat_A.fbx
  artifactKey: Guid(74ed89518c7b76e4d86375cce1f639a5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Meat_A.fbx using Guid(74ed89518c7b76e4d86375cce1f639a5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ea3bc686d582b73ded9be6a50790c720') in 0.0371817 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_G.prefab
  artifactKey: Guid(7c553f4edf2df5e43b6aa40532fe9dc7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_G.prefab using Guid(7c553f4edf2df5e43b6aa40532fe9dc7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8ffc74d66f7bfd83b7897f6bdd978fcd') in 0.0234785 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_B.prefab
  artifactKey: Guid(c96bd2ed91e807742a59c05730874dcb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_B.prefab using Guid(c96bd2ed91e807742a59c05730874dcb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '73a8dba13461836668d1172a96ab43b3') in 0.0352649 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fire_D.fbx
  artifactKey: Guid(1f4f66bd765d0834b86602654ab22ac1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fire_D.fbx using Guid(1f4f66bd765d0834b86602654ab22ac1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f3d4e282ab462a076a3b8ed86f1a4df1') in 0.0434772 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_F.prefab
  artifactKey: Guid(e7a15ec4b35b14e4a926cb52efc116d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_F.prefab using Guid(e7a15ec4b35b14e4a926cb52efc116d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a7e511fafa452ec315113a224215cec6') in 0.0432411 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_A.fbx
  artifactKey: Guid(b342724cf48613b4c86e916feed665e3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_A.fbx using Guid(b342724cf48613b4c86e916feed665e3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d95dc9a865724bac4c7333769eba112') in 0.0390763 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_E.prefab
  artifactKey: Guid(518655a08f22252489187ca4152da84a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_E.prefab using Guid(518655a08f22252489187ca4152da84a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7cf717a1d4718ff0b1b7a8b3810bcf54') in 0.0342259 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_P.fbx
  artifactKey: Guid(802b7c353165eb2438abe900a27b9722) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_P.fbx using Guid(802b7c353165eb2438abe900a27b9722) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '193a880460d9d435acfa70adef16b42a') in 0.0355189 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_H.fbx
  artifactKey: Guid(5b32b24787b70b44f906d373c5488130) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_H.fbx using Guid(5b32b24787b70b44f906d373c5488130) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc39c0cfea6bb24f1d72680fd4f9bec8') in 0.0446489 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/CandleChandelier_A.fbx
  artifactKey: Guid(c05e53a80c34ba04d8a8cb52cbd1aae2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/CandleChandelier_A.fbx using Guid(c05e53a80c34ba04d8a8cb52cbd1aae2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b42ed9fe217f14aa4a4e346f52ef018e') in 0.0397139 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_D.fbx
  artifactKey: Guid(eae2d81bdb2c51046b70e53bdedd29af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_D.fbx using Guid(eae2d81bdb2c51046b70e53bdedd29af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1604a3a76d5ffa51a022f717314dac33') in 0.0426929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_A.prefab
  artifactKey: Guid(71d209b3084dd6e4287075fe2febe5f7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_A.prefab using Guid(71d209b3084dd6e4287075fe2febe5f7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '150559187d6a307ebf8614d4829f33ff') in 0.0356861 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_B.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_B.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b781816fe6368b7f4f89820a6749de56') in 0.031577 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_A.prefab
  artifactKey: Guid(bd7a59239ce6dcb4884350f8b44cfcdd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_A.prefab using Guid(bd7a59239ce6dcb4884350f8b44cfcdd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e120e3518cfc51f82314da2b69947a7c') in 0.0356926 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_C.fbx
  artifactKey: Guid(755962e6d1f7e064f80bb254f446401b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_C.fbx using Guid(755962e6d1f7e064f80bb254f446401b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f7422910205998bb6c538b8d8909d3cc') in 0.0374078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Plane.mat
  artifactKey: Guid(ae2d639a39e167541a12c4ef4620c2db) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Plane.mat using Guid(ae2d639a39e167541a12c4ef4620c2db) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6908eaa978f19dfe7fc194ee27005891') in 0.05867 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_Part_A.fbx
  artifactKey: Guid(4d58dcb3bbb6e7d48935f2c07faa1e77) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_Part_A.fbx using Guid(4d58dcb3bbb6e7d48935f2c07faa1e77) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0661c3143517819fb0e9897d656d504b') in 0.0378996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_G.fbx
  artifactKey: Guid(ccfa23f1223c8ec49a48805bfb3cadb8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_G.fbx using Guid(ccfa23f1223c8ec49a48805bfb3cadb8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6468a9dac8e880df15b35c644c8c18cd') in 0.0397066 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_N.prefab
  artifactKey: Guid(93311290ea7e1954a8a3148f27395a2a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_N.prefab using Guid(93311290ea7e1954a8a3148f27395a2a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fa7bb55c689fe241f39b7f8fbcc8b4d6') in 0.027022 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tree_C.fbx
  artifactKey: Guid(2f23f572ee0c83a4fbfd5707d3d2cc2a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tree_C.fbx using Guid(2f23f572ee0c83a4fbfd5707d3d2cc2a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f80612264e3aa2532ed34f776aa9e380') in 0.0517512 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000517 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_G.fbx
  artifactKey: Guid(7fb4a923528b83e4fba0f170622364c5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_G.fbx using Guid(7fb4a923528b83e4fba0f170622364c5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd6897938cbb4d82dd70e69e7ec81dbf8') in 0.0471484 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_C.prefab
  artifactKey: Guid(3b42da1841446314b98ed56397abe1af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_C.prefab using Guid(3b42da1841446314b98ed56397abe1af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '210a7229448bdaa14f119b9d974b4151') in 0.029951 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_D.prefab
  artifactKey: Guid(3f7d2ea433dcfdb49ae575af6a323c9a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_D.prefab using Guid(3f7d2ea433dcfdb49ae575af6a323c9a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '710a65b344b5f029fabbdb195f3b65de') in 0.0309513 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_C.prefab
  artifactKey: Guid(31f591ae96f0c9846b6739c494ff4167) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_C.prefab using Guid(31f591ae96f0c9846b6739c494ff4167) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '58ea0b4be85df285f3ca0b8f93dbbd07') in 0.028717 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_F.prefab
  artifactKey: Guid(531e0a6f1a330f04a81dd53cf86ef68f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_F.prefab using Guid(531e0a6f1a330f04a81dd53cf86ef68f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b1fb7874ec2c9009390d7277eb50779a') in 0.0220635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_A.prefab
  artifactKey: Guid(c7a6ae6a0287d5648a3bbe692ffe016b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_A.prefab using Guid(c7a6ae6a0287d5648a3bbe692ffe016b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e97e98ed6bd06fdb98e9a91a21d9e265') in 0.0227065 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_I.fbx
  artifactKey: Guid(6b4e6641144ae1749a302052008d041f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_I.fbx using Guid(6b4e6641144ae1749a302052008d041f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '91d2805d62755d0d23652bc5b8aea482') in 0.0467939 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 83.759088 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Emission.mat
  artifactKey: Guid(fb133731a320524458604654a88de236) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Emission.mat using Guid(fb133731a320524458604654a88de236) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f6e5634a3e6b79e0fa1445e224f0b07f') in 0.0405055 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Window.mat
  artifactKey: Guid(43ea1f0fe66b5a64eac755138f7fd0de) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Window.mat using Guid(43ea1f0fe66b5a64eac755138f7fd0de) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '369dabd68508d3b20ca1e06c24ecaea8') in 0.0219165 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Plane.mat
  artifactKey: Guid(ae2d639a39e167541a12c4ef4620c2db) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Plane.mat using Guid(ae2d639a39e167541a12c4ef4620c2db) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2327a7149bd0cd75efc5b539cb6e44c6') in 0.0285895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0