using UnityEngine;
using UnityEngine.Events;
using EconomySystem;

namespace EconomySystem.Shop3D
{
    /// <summary>
    /// Component cho vật phẩm 3D có thể tương tác trong cửa hàng
    /// Tích hợp với hệ thống Economy hiện có
    /// </summary>
    [RequireComponent(typeof(Collider))]
    public class InteractableItem3D : MonoBehaviour
    {
        [Header("Item Configuration")]
        [SerializeField] private string itemID;
        [SerializeField] private Item itemData;
        [SerializeField] private int stockQuantity = 10;
        [SerializeField] private bool isForSale = true;
        [SerializeField] private bool canBuyBack = true;

        [Header("3D Display")]
        [SerializeField] private Transform displayModel;
        [SerializeField] private Transform priceTag;
        [SerializeField] private TextMesh priceText;
        [SerializeField] private bool rotateDisplay = true;
        [SerializeField] private float rotationSpeed = 30f;

        [Header("Interaction")]
        [SerializeField] private float interactionDistance = 3f;
        [SerializeField] private LayerMask playerLayer = 1;
        [SerializeField] private KeyCode interactionKey = KeyCode.E;

        [Header("Visual Effects")]
        [SerializeField] private GameObject highlightEffect;
        [SerializeField] private Outline outlineComponent;
        [SerializeField] private ParticleSystem purchaseEffect;
        [SerializeField] private AudioClip hoverSound;
        [SerializeField] private AudioClip purchaseSound;

        [Header("Events")]
        public UnityEvent OnItemHovered;
        public UnityEvent OnItemUnhovered;
        public UnityEvent OnItemInteracted;
        public UnityEvent<Item, int> OnItemPurchased;

        // Private variables
        private bool isHighlighted = false;
        private bool playerInRange = false;
        private AudioSource audioSource;
        private Renderer[] renderers;
        private Material[] originalMaterials;
        private Material highlightMaterial;

        #region Unity Methods
        private void Awake()
        {
            InitializeComponents();
            LoadItemData();
        }

        private void Start()
        {
            SetupVisualEffects();
            UpdatePriceDisplay();
        }

        private void Update()
        {
            if (rotateDisplay && displayModel != null)
            {
                displayModel.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
            }

            HandleInput();
        }

        private void OnTriggerEnter(Collider other)
        {
            if (IsPlayer(other))
            {
                playerInRange = true;
                OnPlayerEnterRange();
            }
        }

        private void OnTriggerExit(Collider other)
        {
            if (IsPlayer(other))
            {
                playerInRange = false;
                OnPlayerExitRange();
            }
        }
        #endregion

        #region Initialization
        private void InitializeComponents()
        {
            // Tạo AudioSource nếu chưa có
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.spatialBlend = 1f; // 3D sound
            }

            // Lấy tất cả Renderer components
            renderers = GetComponentsInChildren<Renderer>();
            
            // Tạo Outline component nếu chưa có
            if (outlineComponent == null)
            {
                outlineComponent = gameObject.AddComponent<Outline>();
                outlineComponent.enabled = false;
            }

            // Đảm bảo Collider là trigger
            var collider = GetComponent<Collider>();
            collider.isTrigger = true;
        }

        private void LoadItemData()
        {
            if (string.IsNullOrEmpty(itemID)) return;

            // Lấy item data từ ShopManager
            if (ShopManager.Instance?.ItemDatabase != null)
            {
                itemData = ShopManager.Instance.ItemDatabase.LayVatPhamTheoID(itemID);
                
                if (itemData == null)
                {
                    Debug.LogWarning($"Không tìm thấy item với ID: {itemID}");
                }
            }
        }

        private void SetupVisualEffects()
        {
            // Lưu materials gốc
            if (renderers.Length > 0)
            {
                originalMaterials = new Material[renderers.Length];
                for (int i = 0; i < renderers.Length; i++)
                {
                    originalMaterials[i] = renderers[i].material;
                }
            }

            // Tạo highlight material
            CreateHighlightMaterial();

            // Ẩn highlight effect ban đầu
            if (highlightEffect != null)
            {
                highlightEffect.SetActive(false);
            }
        }

        private void CreateHighlightMaterial()
        {
            // Tạo material highlight đơn giản
            highlightMaterial = new Material(Shader.Find("Standard"));
            highlightMaterial.color = Color.yellow;
            highlightMaterial.SetFloat("_Metallic", 0.5f);
            highlightMaterial.SetFloat("_Glossiness", 0.8f);
            highlightMaterial.EnableKeyword("_EMISSION");
            highlightMaterial.SetColor("_EmissionColor", Color.yellow * 0.3f);
        }
        #endregion

        #region Interaction Logic
        private void HandleInput()
        {
            if (!playerInRange || !isHighlighted) return;

            if (Input.GetKeyDown(interactionKey))
            {
                InteractWithItem();
            }
        }

        public void InteractWithItem()
        {
            if (itemData == null)
            {
                Debug.LogWarning("Item data is null!");
                return;
            }

            OnItemInteracted?.Invoke();

            // Mở UI shop 3D
            Shop3DUI.Instance?.ShowItemDetails(this);

            // Play sound effect
            PlaySound(hoverSound);
        }

        public bool PurchaseItem(int quantity = 1)
        {
            if (!CanPurchase(quantity)) return false;

            // Sử dụng ShopManager để mua
            bool success = ShopManager.Instance.MuaVatPham(itemData, quantity);

            if (success)
            {
                OnPurchaseSuccess(quantity);
                return true;
            }

            return false;
        }

        public bool SellItem(int quantity = 1)
        {
            if (!CanSell(quantity)) return false;

            // Sử dụng ShopManager để bán
            bool success = ShopManager.Instance.BanVatPham(itemData, quantity);

            if (success)
            {
                OnSellSuccess(quantity);
                return true;
            }

            return false;
        }

        private void OnPurchaseSuccess(int quantity)
        {
            stockQuantity -= quantity;
            
            // Play effects
            PlayPurchaseEffects();
            
            // Update display
            UpdatePriceDisplay();
            
            // Trigger events
            OnItemPurchased?.Invoke(itemData, quantity);

            // Ẩn item nếu hết hàng
            if (stockQuantity <= 0)
            {
                StartCoroutine(HideItemTemporarily());
            }
        }

        private void OnSellSuccess(int quantity)
        {
            stockQuantity += quantity;
            
            // Play effects
            PlayPurchaseEffects();
            
            // Update display
            UpdatePriceDisplay();
        }

        private System.Collections.IEnumerator HideItemTemporarily()
        {
            // Ẩn model trong 3 giây
            if (displayModel != null)
            {
                displayModel.gameObject.SetActive(false);
            }

            yield return new WaitForSeconds(3f);

            // Hiện lại và reset stock
            if (displayModel != null)
            {
                displayModel.gameObject.SetActive(true);
            }
            
            stockQuantity = 10; // Reset stock
            UpdatePriceDisplay();
        }
        #endregion

        #region Visual Effects
        public void HighlightItem()
        {
            if (isHighlighted) return;

            isHighlighted = true;

            // Enable outline
            if (outlineComponent != null)
            {
                outlineComponent.enabled = true;
                outlineComponent.OutlineColor = Color.yellow;
                outlineComponent.OutlineWidth = 5f;
            }

            // Show highlight effect
            if (highlightEffect != null)
            {
                highlightEffect.SetActive(true);
            }

            // Play hover sound
            PlaySound(hoverSound);

            OnItemHovered?.Invoke();
        }

        public void UnhighlightItem()
        {
            if (!isHighlighted) return;

            isHighlighted = false;

            // Disable outline
            if (outlineComponent != null)
            {
                outlineComponent.enabled = false;
            }

            // Hide highlight effect
            if (highlightEffect != null)
            {
                highlightEffect.SetActive(false);
            }

            OnItemUnhovered?.Invoke();
        }

        private void PlayPurchaseEffects()
        {
            // Play particle effect
            if (purchaseEffect != null)
            {
                purchaseEffect.Play();
            }

            // Play purchase sound
            PlaySound(purchaseSound);

            // Screen shake effect (nếu có)
            // CameraShake.Instance?.Shake(0.1f, 0.5f);
        }

        private void PlaySound(AudioClip clip)
        {
            if (audioSource != null && clip != null)
            {
                audioSource.clip = clip;
                audioSource.Play();
            }
        }

        private void UpdatePriceDisplay()
        {
            if (priceText != null && itemData != null)
            {
                priceText.text = $"{itemData.GiaMua:N0} Lea";
                
                // Đổi màu nếu hết hàng
                if (stockQuantity <= 0)
                {
                    priceText.color = Color.red;
                    priceText.text = "Hết hàng";
                }
                else
                {
                    priceText.color = Color.white;
                }
            }
        }
        #endregion

        #region Player Detection
        private void OnPlayerEnterRange()
        {
            // Có thể hiển thị prompt UI ở đây
            // EconomyPlayerAdapter sẽ tự động detect qua raycast
        }

        private void OnPlayerExitRange()
        {
            UnhighlightItem();
            // EconomyPlayerAdapter sẽ tự động handle
        }

        private bool IsPlayer(Collider other)
        {
            return ((1 << other.gameObject.layer) & playerLayer) != 0;
        }
        #endregion

        #region Validation
        public bool CanPurchase(int quantity = 1)
        {
            if (!isForSale || itemData == null || stockQuantity < quantity)
                return false;

            return ShopManager.Instance?.CoTheMuaVatPham(itemData, quantity) ?? false;
        }

        public bool CanSell(int quantity = 1)
        {
            if (!canBuyBack || itemData == null)
                return false;

            return ShopManager.Instance?.CoTheBanVatPham(itemData, quantity) ?? false;
        }
        #endregion

        #region Public Properties
        public Item ItemData => itemData;
        public int StockQuantity => stockQuantity;
        public bool IsForSale => isForSale;
        public bool CanBuyBack => canBuyBack;
        public bool IsHighlighted => isHighlighted;
        public bool PlayerInRange => playerInRange;
        public float InteractionDistance => interactionDistance;
        #endregion

        #region Editor Methods
        #if UNITY_EDITOR
        private void OnDrawGizmosSelected()
        {
            // Vẽ sphere để hiển thị interaction range
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, interactionDistance);
        }

        [ContextMenu("Test Purchase")]
        private void TestPurchase()
        {
            PurchaseItem(1);
        }

        [ContextMenu("Test Sell")]
        private void TestSell()
        {
            SellItem(1);
        }
        #endif
        #endregion
    }
}
