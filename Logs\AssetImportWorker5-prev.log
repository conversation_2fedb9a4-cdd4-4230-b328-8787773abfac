Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker5
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker5.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [13548]  Target information:

Player connection [13548]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3558704283 [EditorId] 3558704283 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [13548] Host joined multi-casting on [***********:54997]...
Player connection [13548] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56548
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003724 seconds.
- Loaded All Assemblies, in  1.319 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.992 seconds
Domain Reload Profiling: 2313ms
	BeginReloadAssembly (585ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (136ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (107ms)
	LoadAllAssembliesAndSetupDomain (464ms)
		LoadAssemblies (580ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (459ms)
			TypeCache.Refresh (457ms)
				TypeCache.ScanAssembly (423ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (995ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (900ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (41ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (271ms)
			ProcessInitializeOnLoadAttributes (467ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.288 seconds
Refreshing native plugins compatible for Editor in 1.23 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.794 seconds
Domain Reload Profiling: 4079ms
	BeginReloadAssembly (361ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (75ms)
	LoadAllAssembliesAndSetupDomain (1761ms)
		LoadAssemblies (995ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (924ms)
			TypeCache.Refresh (790ms)
				TypeCache.ScanAssembly (732ms)
			BuildScriptInfoCaches (103ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (1795ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1348ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (14ms)
			BeforeProcessingInitializeOnLoad (437ms)
			ProcessInitializeOnLoadAttributes (776ms)
			ProcessInitializeOnLoadMethodAttributes (115ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.16 seconds
Refreshing native plugins compatible for Editor in 4.50 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (9.5 MB). Loaded Objects now: 7294.
Memory consumption went from 167.2 MB to 157.7 MB.
Total: 89.257500 ms (FindLiveObjects: 1.569200 ms CreateObjectMapping: 2.883700 ms MarkObjects: 14.996500 ms  DeleteObjects: 69.805500 ms)

========================================================================
Received Import Request.
  Time since last request: 103331.033979 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Beerglass_B.prefab
  artifactKey: Guid(a989399f82b998b49ae798b4529abafd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Beerglass_B.prefab using Guid(a989399f82b998b49ae798b4529abafd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6a2c42dfbf91ab2182c34f6822b0a462') in 1.0809349 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_Table_C.fbx
  artifactKey: Guid(8e0b6489ef7aff44285ba9cb8cf184a4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_Table_C.fbx using Guid(8e0b6489ef7aff44285ba9cb8cf184a4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f106a1f643648a68c7ae24f69b6b64dd') in 0.2519309 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval PackBundle/URP/URP+.unitypackage
  artifactKey: Guid(fefe61ce2c1278c47b2bf05beceb5ae7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval PackBundle/URP/URP+.unitypackage using Guid(fefe61ce2c1278c47b2bf05beceb5ae7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '444e56951e7b7c8dd23a3beca3fde288') in 0.0210182 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000835 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_H.prefab
  artifactKey: Guid(79876c7bb996ca94a9ff16ac41a4bc3b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_H.prefab using Guid(79876c7bb996ca94a9ff16ac41a4bc3b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '31ca0dc9420a473d3468628ed5bb907d') in 0.0370227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_A.prefab
  artifactKey: Guid(80a141f289ed7ac4c9cbeda1cff72177) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_A.prefab using Guid(80a141f289ed7ac4c9cbeda1cff72177) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4c6ec35d894cbb77a42816cb784f6903') in 0.0243683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Chimney.prefab
  artifactKey: Guid(ea58dc86329f77944a623bb9fa6a713b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Chimney.prefab using Guid(ea58dc86329f77944a623bb9fa6a713b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '576ab66e38263dc5c2cb5b108e0251b0') in 0.0226405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_I.fbx
  artifactKey: Guid(25b6c98de88bdba46887caac9405f1f0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_I.fbx using Guid(25b6c98de88bdba46887caac9405f1f0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81746aa4ef4ba90bf1725edb03c4d3ec') in 0.0411145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Orange.prefab
  artifactKey: Guid(0079d0df83de83b46a18ab3526004216) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Orange.prefab using Guid(0079d0df83de83b46a18ab3526004216) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08b17f7e8fc0633f4993d0c413ca185a') in 0.0302255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_G.fbx
  artifactKey: Guid(42308c6a9ea67f647a8b76a9f5234fff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_G.fbx using Guid(42308c6a9ea67f647a8b76a9f5234fff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '788c7b5a715ae3ecd463f4dbc72c6df3') in 0.040286 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_B.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_B.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8ad89f9522eccfb9c194638fe7dbfe3b') in 0.0274174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_J.prefab
  artifactKey: Guid(d4e4057c0f99ef74ea3cb7c006ec2eae) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_J.prefab using Guid(d4e4057c0f99ef74ea3cb7c006ec2eae) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1b03b2fc905909ee8ba59ecf4dbae42f') in 0.0227079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Script/Economy/ItemDatabase.cs
  artifactKey: Guid(3bf0e5c8da8aec840850ca2937064a37) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/ItemDatabase.cs using Guid(3bf0e5c8da8aec840850ca2937064a37) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de489af7166c765ff5ee4eae337ec344') in 0.0213986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_E.fbx
  artifactKey: Guid(9cd653cf628955d4e99a5ccaf24c16c8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_E.fbx using Guid(9cd653cf628955d4e99a5ccaf24c16c8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '344613b4cacf67b7c65e05bd6c369dd1') in 0.0417006 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000122 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_E.prefab
  artifactKey: Guid(bee69969654bab2419ca1b9ba00ca9d9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_E.prefab using Guid(bee69969654bab2419ca1b9ba00ca9d9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0ba8a2f935987643e502210ea1dae29c') in 0.0249009 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_A.prefab
  artifactKey: Guid(f5011b0dc69dbb74a88845ddb732641f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_A.prefab using Guid(f5011b0dc69dbb74a88845ddb732641f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '24b8dcae14038dceff12bc4a6ff32342') in 0.0239196 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ground_Tile_A.fbx
  artifactKey: Guid(38d673e872d8b27438dfaae043c063bf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ground_Tile_A.fbx using Guid(38d673e872d8b27438dfaae043c063bf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '823335f8defc903df56ba7c156df8255') in 0.048721 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_C.prefab
  artifactKey: Guid(d582de53372d40a4ca3ec77bafe8fdb4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_C.prefab using Guid(d582de53372d40a4ca3ec77bafe8fdb4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c4c000555025766cb6e2ee83e20313e3') in 0.0248831 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_L.fbx
  artifactKey: Guid(36234d814117f0c408baa556d344e4ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_L.fbx using Guid(36234d814117f0c408baa556d344e4ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b305d9e2924520dfe808255a55661fb9') in 0.0356459 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_E.fbx
  artifactKey: Guid(303d1b479cec74746a9a87482e01e18b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_E.fbx using Guid(303d1b479cec74746a9a87482e01e18b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '02dca367f4c2772471bf2b037584179e') in 0.0385633 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_B.prefab
  artifactKey: Guid(c1929576f46dbc84d92418a9d2c1fac1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_B.prefab using Guid(c1929576f46dbc84d92418a9d2c1fac1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c4e7cb5a63813344ed68e80e8dec9833') in 0.0221473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_H.prefab
  artifactKey: Guid(acfad7292fb21154db3fdd1a10ee8a42) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_H.prefab using Guid(acfad7292fb21154db3fdd1a10ee8a42) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b504f51ea2c21e352ecbbabb3273adc9') in 0.0218779 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_J.prefab
  artifactKey: Guid(3084aa3d03177a944833dfa6ca85c263) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_J.prefab using Guid(3084aa3d03177a944833dfa6ca85c263) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3ad9215a31e2a7e1a5a776746b83ab84') in 0.0203147 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000088 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_C.prefab
  artifactKey: Guid(2f95738b352668a40ab4da2ee5fdf9ed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_C.prefab using Guid(2f95738b352668a40ab4da2ee5fdf9ed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '71b3927d0e1ef371162e169bd377a53e') in 0.0306425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_C.prefab
  artifactKey: Guid(ef8fb51cbf896b24ebd858bf6b1bc5ed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_C.prefab using Guid(ef8fb51cbf896b24ebd858bf6b1bc5ed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '41973781fe0284c25a3bfab4332d8c37') in 0.0254699 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_F.prefab
  artifactKey: Guid(86b55ba9212ca2b4eb59052bb32e5b69) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_F.prefab using Guid(86b55ba9212ca2b4eb59052bb32e5b69) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8d4b17b37f29ed09f8f9ca0f79048715') in 0.0240213 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_F.prefab
  artifactKey: Guid(6cff417c87f9f8d46a2e67757a927e6b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_F.prefab using Guid(6cff417c87f9f8d46a2e67757a927e6b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '91f2631077fc4adf7a949079b06e0924') in 0.0226572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_C.prefab
  artifactKey: Guid(9ba92e76496cfab439d07161152fb64b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_C.prefab using Guid(9ba92e76496cfab439d07161152fb64b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '26c286d18b9760cfa4c4939a6f2170af') in 0.0310654 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_C.prefab
  artifactKey: Guid(75088b9a3503cb242aa439290c54b3fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_C.prefab using Guid(75088b9a3503cb242aa439290c54b3fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '82cb0d00447a6c2d34c18cb511eb13d6') in 0.0267637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_D.fbx
  artifactKey: Guid(374905558dd74e44ea3e30b285e0c7b2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_D.fbx using Guid(374905558dd74e44ea3e30b285e0c7b2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e9c101c63e22beb5557faf9e9373d330') in 0.0400861 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_J.fbx
  artifactKey: Guid(bbed3121c71e3e443ac48427180bed7a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_J.fbx using Guid(bbed3121c71e3e443ac48427180bed7a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '969168886349f03cc391fa092a064ec8') in 0.0483411 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pear_Yellow.prefab
  artifactKey: Guid(7de01e3f92e2b1b4e9d570ec3d429449) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pear_Yellow.prefab using Guid(7de01e3f92e2b1b4e9d570ec3d429449) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'caae8d8b0ea384607edb14b4dd5409f3') in 0.0263493 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Onion_Purple.prefab
  artifactKey: Guid(e5934cdb9642e1d4788ce147da1411d4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Onion_Purple.prefab using Guid(e5934cdb9642e1d4788ce147da1411d4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4bbbf86f5bafdbbc44754db5fdd7d417') in 0.0579075 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Plane.prefab
  artifactKey: Guid(a7ec83fb0a621754facfd7342af4926f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Plane.prefab using Guid(a7ec83fb0a621754facfd7342af4926f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c7e34c769c3ddaf0178f28d6eb8d2ee5') in 0.0261917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000074 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_A.prefab
  artifactKey: Guid(23a0bdd64b4369c4db2f1024b76f5494) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_A.prefab using Guid(23a0bdd64b4369c4db2f1024b76f5494) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '029def274ba606a9d24f1a9f452dbaa5') in 0.0274499 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_C.prefab
  artifactKey: Guid(ab50578661df52348a252c3b04230a42) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_C.prefab using Guid(ab50578661df52348a252c3b04230a42) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '146aa5f2874214f91fd371b321142b4d') in 0.0370487 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pumpkin_B.prefab
  artifactKey: Guid(b2c9990dafe2ca24faf095618fef804e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pumpkin_B.prefab using Guid(b2c9990dafe2ca24faf095618fef804e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ff1874d424e6c35819e9872c1d875d70') in 0.0269982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_Q.prefab
  artifactKey: Guid(4b504dde5f72b224e998f5109bc8b772) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_Q.prefab using Guid(4b504dde5f72b224e998f5109bc8b772) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eb2a21e4e1130907622a197ba24b080b') in 0.0297855 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Hammer_B.fbx
  artifactKey: Guid(698dc40ee273eac4c92549adc25e6aed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Hammer_B.fbx using Guid(698dc40ee273eac4c92549adc25e6aed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e12f66b92dd6d2898c79a9b911f806ee') in 0.046915 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_F.prefab
  artifactKey: Guid(8f978d6fadfddcd44b4ca14ba638346b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_F.prefab using Guid(8f978d6fadfddcd44b4ca14ba638346b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '158afaed61c93113f3c1368e5eb621ce') in 0.0466842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_F.prefab
  artifactKey: Guid(048d859f818449e42b2e362680d414fd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_F.prefab using Guid(048d859f818449e42b2e362680d414fd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5a5fef1ebddfc7a2805de50bbf989324') in 0.021538 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_E.prefab
  artifactKey: Guid(799c371d57be540488146a6d5c0ce89f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_E.prefab using Guid(799c371d57be540488146a6d5c0ce89f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '91733d6729d0510f22b82a28eb9b668f') in 0.0301646 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_G.fbx
  artifactKey: Guid(ea0550d390d55554dbf2d19b599baf2a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_G.fbx using Guid(ea0550d390d55554dbf2d19b599baf2a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f4da43e1adbd74e5c2b16fff06c655f3') in 0.0442694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_F.prefab
  artifactKey: Guid(6cff417c87f9f8d46a2e67757a927e6b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_F.prefab using Guid(6cff417c87f9f8d46a2e67757a927e6b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '836fe9c6857263d14e809af269757b48') in 0.0297845 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lemon.prefab
  artifactKey: Guid(c15791ef484b90a4db4558893929f25f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lemon.prefab using Guid(c15791ef484b90a4db4558893929f25f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0434fdbe90ea76da446874bc318c23a8') in 0.0501588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_G.prefab
  artifactKey: Guid(7357a6208e582cb4a93e9305c5d0c17c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_G.prefab using Guid(7357a6208e582cb4a93e9305c5d0c17c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ba8190d59549bd07527ee4613ebf5704') in 0.0463675 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_G.prefab
  artifactKey: Guid(4b9d7b29dff011747acef1149999995e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_G.prefab using Guid(4b9d7b29dff011747acef1149999995e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '910a78b2e2fa53cbb8a8931cb692e0e7') in 0.026441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_B.fbx
  artifactKey: Guid(1c40165dd1d445a4c9719e1dd8c798b4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Gate_B.fbx using Guid(1c40165dd1d445a4c9719e1dd8c798b4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46fbea7438d65b394543d0758e0f5559') in 0.0392614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_G.fbx
  artifactKey: Guid(824989ed396f7094bbbb1dc93e015e65) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_G.fbx using Guid(824989ed396f7094bbbb1dc93e015e65) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a33c22196bd31d7e1024ab3c5c87cbbb') in 0.0559596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch.prefab
  artifactKey: Guid(14affc4b0bf136045afa699ec132c943) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch.prefab using Guid(14affc4b0bf136045afa699ec132c943) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ee58abed3f6b856b8d9d5d8f056217c8') in 0.0290419 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_C.prefab
  artifactKey: Guid(f03f21bc33e4d014fa77a5bb6350cea1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_C.prefab using Guid(f03f21bc33e4d014fa77a5bb6350cea1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ca1f7faa5c6f571182c0b5da96557634') in 0.0384652 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_B.fbx
  artifactKey: Guid(22b33992dee2cc044b0b0b4b7ccb23e3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_B.fbx using Guid(22b33992dee2cc044b0b0b4b7ccb23e3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7acfa6258903712ef2c81b77064f04e3') in 0.0434649 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000160 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_G.fbx
  artifactKey: Guid(c3f73d6a7b0317e45a3a9f45685f296b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_G.fbx using Guid(c3f73d6a7b0317e45a3a9f45685f296b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '327d0925cda1b50eefb3563ca135c2a4') in 0.0354358 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Yellow.prefab
  artifactKey: Guid(635cde54173d1d848b6f4cc62898584c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Yellow.prefab using Guid(635cde54173d1d848b6f4cc62898584c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f6e78a8947890eb19fc6fb649708a838') in 0.0421187 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_E.fbx
  artifactKey: Guid(41daa22e89d5fad4d9810958f1658558) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wagon_E.fbx using Guid(41daa22e89d5fad4d9810958f1658558) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '600b57997076bfd1c87f202015a8c141') in 0.0574702 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_T.prefab
  artifactKey: Guid(b4a85e25ebd23aa4b94f627a80b3bd4d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_T.prefab using Guid(b4a85e25ebd23aa4b94f627a80b3bd4d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c4bb784682683cec2d18813716f16e91') in 0.0478126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Eggplant.prefab
  artifactKey: Guid(43d2a44f56178634cb4d95f57c802ea4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Eggplant.prefab using Guid(43d2a44f56178634cb4d95f57c802ea4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17472371398cd01feb53f2b09aea86c0') in 0.0301853 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_K.prefab
  artifactKey: Guid(b36eb152245a5b04db85284ae46354bf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_K.prefab using Guid(b36eb152245a5b04db85284ae46354bf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b43230803c84fa97c81bf17cb3644632') in 0.036515 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_F.prefab
  artifactKey: Guid(c11064492f6e1114595925710b6825ed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_F.prefab using Guid(c11064492f6e1114595925710b6825ed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3cbf34a1a6e37f3e5ad7bc8fe09773b6') in 0.0305463 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Candelabrum_B.prefab
  artifactKey: Guid(187b76555bbf99c438b00e687b7f99a9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Candelabrum_B.prefab using Guid(187b76555bbf99c438b00e687b7f99a9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0a8e4a7aaf1942567bd28682e7c743db') in 0.0334249 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Brazier_B.prefab
  artifactKey: Guid(83682936f26a7e34d9096161bf469b7e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Brazier_B.prefab using Guid(83682936f26a7e34d9096161bf469b7e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6784216eb0e1a30fec5e13bb0dbe8fe') in 0.0300012 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_B.prefab
  artifactKey: Guid(dc82ce884131ddb4c9873829df12d61f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_B.prefab using Guid(dc82ce884131ddb4c9873829df12d61f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f577df418b19dc7302e9359962e13ed0') in 0.0226366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_F.prefab
  artifactKey: Guid(594478582566f234e89959fcfd3f2b51) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_F.prefab using Guid(594478582566f234e89959fcfd3f2b51) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1b5fe06bee7099fac07f42f667048cdd') in 0.0392188 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Open_A.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Open_A.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab44695c9ac3f59023dcedaeb0eaa7ae') in 0.0438473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_A.prefab
  artifactKey: Guid(ce3c83e3563b1a04aac16e6bf6d6bd6b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_A.prefab using Guid(ce3c83e3563b1a04aac16e6bf6d6bd6b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '876db4e34e0243b20241ec077eb0ace9') in 0.0361576 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_E.prefab
  artifactKey: Guid(2f136f6a66182ac4f8bb682a2c4e46fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_E.prefab using Guid(2f136f6a66182ac4f8bb682a2c4e46fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '96ed4373050e5359c0aa4c36f6e14f03') in 0.0299814 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_F.prefab
  artifactKey: Guid(3ce40b7f85353da4d84da50026f013a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_F.prefab using Guid(3ce40b7f85353da4d84da50026f013a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f8d70bb42691df591e64c694ed49dbb7') in 0.0282451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/CandelStick_A.fbx
  artifactKey: Guid(08004ae369e9e034d9f5610cd3c64d27) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/CandelStick_A.fbx using Guid(08004ae369e9e034d9f5610cd3c64d27) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac9e8707a76d31ab61e5b0634c09a34a') in 0.0376587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_A.prefab
  artifactKey: Guid(a903833e8c6d31f418dcfedde9a99b71) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_A.prefab using Guid(a903833e8c6d31f418dcfedde9a99b71) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '915700ab24599aa9c21c6beaf28f7c98') in 0.0273965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_B.fbx
  artifactKey: Guid(8cbd2acb3114294479367ff2f7db213d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_B.fbx using Guid(8cbd2acb3114294479367ff2f7db213d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ca09744026e9e3629233b7f54540871c') in 0.0461211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/GunnyBag_White.fbx
  artifactKey: Guid(989f0a3465989d0469f10d0c65651cf0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/GunnyBag_White.fbx using Guid(989f0a3465989d0469f10d0c65651cf0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e510f884137bd91252ce20867e5a5a65') in 0.0465126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_G.prefab
  artifactKey: Guid(afb05f76f5d21dc4a82d4d65b6096657) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_G.prefab using Guid(afb05f76f5d21dc4a82d4d65b6096657) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a10214d58c4ef8258ae13c3d2f889a61') in 0.0279981 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Candelabrum_A.prefab
  artifactKey: Guid(12adfc3cf6795f54d91ba3c2445a89e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Candelabrum_A.prefab using Guid(12adfc3cf6795f54d91ba3c2445a89e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6df444a17f4b822a02b3b78f50c12373') in 0.0298947 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_F.prefab
  artifactKey: Guid(c5e0e0f8fb9aafe40aa541c049e00bcb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_F.prefab using Guid(c5e0e0f8fb9aafe40aa541c049e00bcb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c2f5a819957d18273db595b2875b072c') in 0.0385589 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_D.prefab
  artifactKey: Guid(114f56ec46fc7f048a208f0a2a151fe3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_D.prefab using Guid(114f56ec46fc7f048a208f0a2a151fe3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a78009e675f3bac1b664513832c41629') in 0.0273798 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_K.fbx
  artifactKey: Guid(e606f9cad3828c54e8085479461d4799) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_K.fbx using Guid(e606f9cad3828c54e8085479461d4799) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3c16042e5d89fd21788452495c00f612') in 0.032291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Chimney.prefab
  artifactKey: Guid(ea58dc86329f77944a623bb9fa6a713b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Chimney.prefab using Guid(ea58dc86329f77944a623bb9fa6a713b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a195339a5c4266c3c316021c0eba1e16') in 0.0285523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_A.prefab
  artifactKey: Guid(e2ee60e2dad25c94f86aa4f998e2a4d4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_A.prefab using Guid(e2ee60e2dad25c94f86aa4f998e2a4d4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6969fe87e9dc31415ebee460c5ff49bb') in 0.033567 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_A.prefab
  artifactKey: Guid(3395bd05b77e85f46a98444f3829a3c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_A.prefab using Guid(3395bd05b77e85f46a98444f3829a3c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f6766e6097a7bd18322d55b40e0230b') in 0.037298 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_D.prefab
  artifactKey: Guid(8459d01ad38d81e4cacee79763be4e41) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_D.prefab using Guid(8459d01ad38d81e4cacee79763be4e41) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c5aa0540a51e7f4a51dd584c1ed87be9') in 0.0291949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Black.prefab
  artifactKey: Guid(3ffad265b71c05d458b846b5f8472a46) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Black.prefab using Guid(3ffad265b71c05d458b846b5f8472a46) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c5c771bcfd5080cc9a6816e7d4bfd531') in 0.0275917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tent_D.fbx
  artifactKey: Guid(de5ec0514c611604a894e6cebfd49ec1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tent_D.fbx using Guid(de5ec0514c611604a894e6cebfd49ec1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cde9f53cad4cdf4582820d2c75417f81') in 0.0361388 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Table_C.fbx
  artifactKey: Guid(53986a6ff6c42ad439c6e114358d1ffa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Table_C.fbx using Guid(53986a6ff6c42ad439c6e114358d1ffa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '738256a7c2e38e9fdd48c59585ddf951') in 0.0359361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_N.fbx
  artifactKey: Guid(0d96fda4fc6c53a478642e213025afd3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_N.fbx using Guid(0d96fda4fc6c53a478642e213025afd3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '77a2c5993b60d9b5127e1bd37c1bff8a') in 0.0481071 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_G.fbx
  artifactKey: Guid(2a3ee6d0f0f4638478c851ea5c62a3c5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_G.fbx using Guid(2a3ee6d0f0f4638478c851ea5c62a3c5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9bbcf4ebf59cfb069911e8419979fe3c') in 0.0448412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_I.fbx
  artifactKey: Guid(4d078acfc5dd862458053859506ce65c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_I.fbx using Guid(4d078acfc5dd862458053859506ce65c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ded43adcdb04f3772037310cca6565b') in 0.0483649 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Water_Tile_A.prefab
  artifactKey: Guid(70cfaf618292689458d57d1cf8880360) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Water_Tile_A.prefab using Guid(70cfaf618292689458d57d1cf8880360) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc429171cd9cd49c1d609c7887d75400') in 0.0329235 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_C.prefab
  artifactKey: Guid(dae6be95f674e484a9deacc3a8ffc93a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_C.prefab using Guid(dae6be95f674e484a9deacc3a8ffc93a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ca89622ff5811e41dc6b1632ad1832b5') in 0.0310281 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_D.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_D.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd5cfa078dfbdc6b46dbb8202770abb64') in 0.0284731 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_J.fbx
  artifactKey: Guid(f9d3f400d99174b4f981639a9911fe8c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_J.fbx using Guid(f9d3f400d99174b4f981639a9911fe8c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '55cbbd7f4cd8fda4f0f2af89fd94c515') in 0.0398385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_A.prefab
  artifactKey: Guid(228d2a930604eec449ac4d997d1bfb03) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_A.prefab using Guid(228d2a930604eec449ac4d997d1bfb03) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '74476007d949228baedd578fbd8d822d') in 0.0290923 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_B.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_B.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '005d7731bfe05f1a2d9663f0d0407e46') in 0.0332519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_H.prefab
  artifactKey: Guid(c6ea47c7ffb4a29418846dd981c4cfa7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_H.prefab using Guid(c6ea47c7ffb4a29418846dd981c4cfa7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '00641df07db8faa9136953f41837104e') in 0.0301656 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_L.prefab
  artifactKey: Guid(a20b372cd1a4a334e875c26c57612cdb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_L.prefab using Guid(a20b372cd1a4a334e875c26c57612cdb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ba263c102b8cb9ed1772d28d51c1185') in 0.0296892 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_A.prefab
  artifactKey: Guid(bffc626bb005fbd4789e4fbba4bc4900) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_A.prefab using Guid(bffc626bb005fbd4789e4fbba4bc4900) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '024957bdca775e4366f1308abe886e8b') in 0.0361321 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_F.fbx
  artifactKey: Guid(f7a170fa19c407a4db09b61e99ea597e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_F.fbx using Guid(f7a170fa19c407a4db09b61e99ea597e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'abc93b1b616dd18ea0eece820e037824') in 0.0437792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_A.prefab
  artifactKey: Guid(8dd1e83924fc837499b313ada93265cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_A.prefab using Guid(8dd1e83924fc837499b313ada93265cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '93988c847a50e3c31870fa6c388d5468') in 0.0331688 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lemon.fbx
  artifactKey: Guid(8faf1f29bbf25c541ac527b94cfd5943) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lemon.fbx using Guid(8faf1f29bbf25c541ac527b94cfd5943) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f26875cb3237113e5029bb0f25627b11') in 0.045276 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_E.prefab
  artifactKey: Guid(de27c75054ff3124999b0f9632537d15) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_E.prefab using Guid(de27c75054ff3124999b0f9632537d15) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a48e30160b2e441305b563b73c5813de') in 0.0550795 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Green.prefab
  artifactKey: Guid(77b41cc5500a00344b8ea44b6fd251d7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Green.prefab using Guid(77b41cc5500a00344b8ea44b6fd251d7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a56298ae49084537b465cf83f06a2bfb') in 0.0444035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_E.prefab
  artifactKey: Guid(b97f5974907c3684ba79e30ee8e695af) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_E.prefab using Guid(b97f5974907c3684ba79e30ee8e695af) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a34a130e28dd54fb38960322bef22329') in 0.0349932 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000114 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_K.fbx
  artifactKey: Guid(e3993558ee0ea954fa4407a8a9d28c02) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_K.fbx using Guid(e3993558ee0ea954fa4407a8a9d28c02) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '914e86a40f29414a1688c8065cf5104a') in 0.0416141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bed_A.fbx
  artifactKey: Guid(92f283ce5bfb94e4da0d21aa4ec1560f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bed_A.fbx using Guid(92f283ce5bfb94e4da0d21aa4ec1560f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3975d7c617b4d4d59250ced5ea3e030b') in 0.0510356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_K.prefab
  artifactKey: Guid(c7b59dda060e2bb4c90ae4cd61e938d6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_K.prefab using Guid(c7b59dda060e2bb4c90ae4cd61e938d6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc81e2975ea0e77c2e22aaab55f95ee7') in 0.0296309 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_H.prefab
  artifactKey: Guid(f5e6f817b40a9524d8b38f590f162c4b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_H.prefab using Guid(f5e6f817b40a9524d8b38f590f162c4b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e018b4e9114cf51f007ab57b18259d7a') in 0.0300081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_Table_A.fbx
  artifactKey: Guid(69600c80dc9a3e644b5098b99a69e191) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_Table_A.fbx using Guid(69600c80dc9a3e644b5098b99a69e191) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b3adef756be9ac55740996a098d4d10b') in 0.0463341 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_A.fbx
  artifactKey: Guid(579b63b52d6b24344a4acac7dd34dc05) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_A.fbx using Guid(579b63b52d6b24344a4acac7dd34dc05) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '46ba71c17c1835ea125f39ddeb21de11') in 0.0384437 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BatteringRam.prefab
  artifactKey: Guid(9fbec1d124250b842a0459b626fcbc31) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BatteringRam.prefab using Guid(9fbec1d124250b842a0459b626fcbc31) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '923c5d292dfdcd7c49fa0892fae02e0d') in 0.0398983 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_D.prefab
  artifactKey: Guid(076974b3e7208204282f49426c616193) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_D.prefab using Guid(076974b3e7208204282f49426c616193) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '05d764b7e61ec3341ad39d9984f15e21') in 0.0295802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_Sign_D.fbx
  artifactKey: Guid(97dd66ce17377564d9c1f1dfe7d2dbb8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_Sign_D.fbx using Guid(97dd66ce17377564d9c1f1dfe7d2dbb8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'add3d200d7aebc3693d304681f6d2f21') in 0.0364519 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_I.prefab
  artifactKey: Guid(b6b6e8c9b9f27864cb8352814cde1a4d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_I.prefab using Guid(b6b6e8c9b9f27864cb8352814cde1a4d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9136199540cb83b28a4b7c43fa83b868') in 0.0333991 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Orange.fbx
  artifactKey: Guid(2f2a2ec31f1d1e94e9e9bafd9ef49dd7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Orange.fbx using Guid(2f2a2ec31f1d1e94e9e9bafd9ef49dd7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '80d1b056ba18a29f5dab1f960a73785e') in 0.0417185 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000750 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_A.prefab
  artifactKey: Guid(a4868ae2d09ee9d48b587c9bb55d8936) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_A.prefab using Guid(a4868ae2d09ee9d48b587c9bb55d8936) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e7d8637f9adc43b5bd21d69e1132d783') in 0.0282431 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_E.prefab
  artifactKey: Guid(f4d5f6c106589014f92cc4c5a04c623f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_E.prefab using Guid(f4d5f6c106589014f92cc4c5a04c623f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3811e5fe52284ad6027f4db07dd1565f') in 0.0230952 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Banana_Green.prefab
  artifactKey: Guid(ee9e1a1ade333ef45a7b03d63d20ccc3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Banana_Green.prefab using Guid(ee9e1a1ade333ef45a7b03d63d20ccc3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ec6428b2fe07cd09c756393d3a8c1fd9') in 0.0273496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Sword_B.fbx
  artifactKey: Guid(9f6b2a4754b75964298bdfc3cfbff068) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Sword_B.fbx using Guid(9f6b2a4754b75964298bdfc3cfbff068) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '41288538cb943b286be7b25bd9cd4c9a') in 0.0402528 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0