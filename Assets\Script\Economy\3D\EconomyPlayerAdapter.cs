using UnityEngine;
using UnityEngine.InputSystem;

namespace EconomySystem.Shop3D
{
    /// <summary>
    /// Adapter để tích hợp hệ thống Economy 3D với PlayerController và PlayerInteraction hiện có
    /// Không thay thế mà mở rộng chức năng cho Economy System
    /// </summary>
    public class EconomyPlayerAdapter : MonoBehaviour
    {
        #region Singleton
        private static EconomyPlayerAdapter _instance;
        public static EconomyPlayerAdapter Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<EconomyPlayerAdapter>();
                }
                return _instance;
            }
        }
        #endregion

        [Header("Player References")]
        [SerializeField] private PlayerController playerController;
        [SerializeField] private PlayerInteraction playerInteraction;
        [SerializeField] private Camera playerCamera;

        [Header("Economy Interaction Settings")]
        [SerializeField] private float economyRaycastDistance = 5f;
        [SerializeField] private LayerMask economyInteractableLayer = -1;
        [SerializeField] private bool showDebugRay = true;

        [Header("UI Elements")]
        [SerializeField] private GameObject economyInteractionPrompt;
        [SerializeField] private TMPro.TextMeshProUGUI promptText;
        [SerializeField] private TMPro.TextMeshProUGUI itemNameText;
        [SerializeField] private TMPro.TextMeshProUGUI itemPriceText;

        [Header("Visual Feedback")]
        [SerializeField] private UnityEngine.UI.Image crosshair;
        [SerializeField] private Color normalCrosshairColor = Color.white;
        [SerializeField] private Color economyCrosshairColor = Color.yellow;

        // Private variables
        private InteractableItem3D currentEconomyInteractable;
        private RaycastHit economyRaycastHit;
        private bool isEconomyInteractionMode = false;

        // Input Actions
        private InputAction shopMenuAction;
        private InputAction economyInteractAction;

        #region Unity Methods
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
                return;
            }

            InitializeComponents();
        }

        private void Start()
        {
            SetupInputActions();
            SetupUI();
        }

        private void Update()
        {
            HandleEconomyRaycast();
            UpdateUI();
        }

        private void OnEnable()
        {
            EnableInputActions();
        }

        private void OnDisable()
        {
            DisableInputActions();
        }

        private void OnDestroy()
        {
            DisableInputActions();
        }

        private void OnDrawGizmos()
        {
            if (showDebugRay && playerCamera != null)
            {
                Gizmos.color = currentEconomyInteractable != null ? Color.green : Color.red;
                Vector3 rayOrigin = playerCamera.transform.position;
                Vector3 rayDirection = playerCamera.transform.forward;
                Gizmos.DrawRay(rayOrigin, rayDirection * economyRaycastDistance);
            }
        }
        #endregion

        #region Initialization
        private void InitializeComponents()
        {
            // Tự động tìm components nếu chưa gán
            if (playerController == null)
                playerController = FindFirstObjectByType<PlayerController>();

            if (playerInteraction == null)
                playerInteraction = FindFirstObjectByType<PlayerInteraction>();

            if (playerCamera == null)
            {
                playerCamera = Camera.main;
                if (playerCamera == null && playerController != null)
                {
                    playerCamera = playerController.GetComponentInChildren<Camera>();
                }
            }

            if (playerController == null || playerInteraction == null)
            {
                Debug.LogError("EconomyPlayerAdapter: Không tìm thấy PlayerController hoặc PlayerInteraction!");
            }
        }

        private void SetupInputActions()
        {
            // Tạo input actions cho Economy system
            var inputActions = new InputActionMap("EconomyActions");

            shopMenuAction = inputActions.AddAction("ShopMenu", InputActionType.Button);
            shopMenuAction.AddBinding("<Keyboard>/tab");

            economyInteractAction = inputActions.AddAction("EconomyInteract", InputActionType.Button);
            economyInteractAction.AddBinding("<Keyboard>/f"); // Sử dụng F thay vì E để không trung với hệ thống hiện có

            inputActions.Enable();

            // Bind callbacks
            shopMenuAction.performed += OnShopMenuPressed;
            economyInteractAction.performed += OnEconomyInteractPressed;
        }

        private void SetupUI()
        {
            // Ẩn economy interaction prompt ban đầu
            if (economyInteractionPrompt != null)
            {
                economyInteractionPrompt.SetActive(false);
            }

            // Setup crosshair
            if (crosshair != null)
            {
                crosshair.color = normalCrosshairColor;
            }
        }

        private void EnableInputActions()
        {
            shopMenuAction?.Enable();
            economyInteractAction?.Enable();
        }

        private void DisableInputActions()
        {
            shopMenuAction?.Disable();
            economyInteractAction?.Disable();
        }
        #endregion

        #region Economy Raycast
        private void HandleEconomyRaycast()
        {
            if (playerCamera == null) return;

            Vector3 rayOrigin = playerCamera.transform.position;
            Vector3 rayDirection = playerCamera.transform.forward;

            // Thực hiện raycast cho Economy items
            bool hitEconomyItem = Physics.Raycast(rayOrigin, rayDirection, out economyRaycastHit, 
                economyRaycastDistance, economyInteractableLayer);

            InteractableItem3D hitInteractable = null;

            if (hitEconomyItem)
            {
                hitInteractable = economyRaycastHit.collider.GetComponent<InteractableItem3D>();
            }

            // Cập nhật current interactable
            UpdateCurrentEconomyInteractable(hitInteractable);
        }

        private void UpdateCurrentEconomyInteractable(InteractableItem3D newInteractable)
        {
            // Nếu không có thay đổi, return
            if (currentEconomyInteractable == newInteractable) return;

            // Unhighlight interactable cũ
            if (currentEconomyInteractable != null)
            {
                currentEconomyInteractable.UnhighlightItem();
                OnStopLookingAtEconomyItem();
            }

            // Cập nhật current interactable
            currentEconomyInteractable = newInteractable;

            // Highlight interactable mới
            if (currentEconomyInteractable != null)
            {
                currentEconomyInteractable.HighlightItem();
                OnStartLookingAtEconomyItem();
            }
        }

        private void OnStartLookingAtEconomyItem()
        {
            isEconomyInteractionMode = true;
            
            // Update crosshair
            if (crosshair != null)
            {
                crosshair.color = economyCrosshairColor;
            }

            // Show economy interaction prompt
            ShowEconomyInteractionPrompt(true);
        }

        private void OnStopLookingAtEconomyItem()
        {
            isEconomyInteractionMode = false;
            
            // Update crosshair
            if (crosshair != null)
            {
                crosshair.color = normalCrosshairColor;
            }

            // Hide economy interaction prompt
            ShowEconomyInteractionPrompt(false);
        }
        #endregion

        #region Input Handlers
        private void OnShopMenuPressed(InputAction.CallbackContext context)
        {
            Shop3DUI.Instance?.ToggleShopMenu();
        }

        private void OnEconomyInteractPressed(InputAction.CallbackContext context)
        {
            if (currentEconomyInteractable != null)
            {
                currentEconomyInteractable.InteractWithItem();
            }
        }
        #endregion

        #region UI Updates
        private void UpdateUI()
        {
            UpdateEconomyInteractionPrompt();
        }

        private void UpdateEconomyInteractionPrompt()
        {
            if (economyInteractionPrompt == null) return;

            bool shouldShow = currentEconomyInteractable != null;
            
            if (economyInteractionPrompt.activeSelf != shouldShow)
            {
                economyInteractionPrompt.SetActive(shouldShow);
            }

            if (shouldShow && currentEconomyInteractable != null)
            {
                UpdatePromptText();
            }
        }

        private void UpdatePromptText()
        {
            var item = currentEconomyInteractable.ItemData;
            
            if (promptText != null)
            {
                promptText.text = "Nhấn [F] để tương tác với vật phẩm";
            }

            if (itemNameText != null && item != null)
            {
                itemNameText.text = item.TenVatPham;
            }

            if (itemPriceText != null && item != null)
            {
                itemPriceText.text = $"{item.GiaMua:N0} Lea";
            }
        }

        private void ShowEconomyInteractionPrompt(bool show)
        {
            if (economyInteractionPrompt != null)
            {
                economyInteractionPrompt.SetActive(show);
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Lấy current economy interactable
        /// </summary>
        public InteractableItem3D GetCurrentEconomyInteractable()
        {
            return currentEconomyInteractable;
        }

        /// <summary>
        /// Kiểm tra có đang trong economy interaction mode không
        /// </summary>
        public bool IsInEconomyInteractionMode()
        {
            return isEconomyInteractionMode;
        }

        /// <summary>
        /// Force interact với current economy item
        /// </summary>
        public void ForceEconomyInteract()
        {
            if (currentEconomyInteractable != null)
            {
                currentEconomyInteractable.InteractWithItem();
            }
        }

        /// <summary>
        /// Lấy player camera
        /// </summary>
        public Camera GetPlayerCamera()
        {
            return playerCamera;
        }

        /// <summary>
        /// Lấy player controller
        /// </summary>
        public PlayerController GetPlayerController()
        {
            return playerController;
        }

        /// <summary>
        /// Lấy player interaction
        /// </summary>
        public PlayerInteraction GetPlayerInteraction()
        {
            return playerInteraction;
        }

        /// <summary>
        /// Bật/tắt economy interaction
        /// </summary>
        public void SetEconomyInteractionEnabled(bool enabled)
        {
            this.enabled = enabled;
        }
        #endregion

        #region Integration Methods
        /// <summary>
        /// Kiểm tra player có đang di chuyển không (tích hợp với PlayerController hiện có)
        /// </summary>
        public bool IsPlayerMoving()
        {
            if (playerController != null)
            {
                // Sử dụng property IsMoving từ PlayerController hiện có
                return playerController.IsMoving;
            }
            return false;
        }

        /// <summary>
        /// Kiểm tra player có đang chạy không
        /// </summary>
        public bool IsPlayerRunning()
        {
            if (playerController != null)
            {
                return playerController.IsRunning;
            }
            return false;
        }

        /// <summary>
        /// Lấy vị trí player
        /// </summary>
        public Vector3 GetPlayerPosition()
        {
            if (playerController != null)
            {
                return playerController.transform.position;
            }
            return Vector3.zero;
        }
        #endregion

        #region Editor Methods
        #if UNITY_EDITOR
        [ContextMenu("Test Economy Interaction")]
        private void TestEconomyInteraction()
        {
            ForceEconomyInteract();
        }

        [ContextMenu("Toggle Shop Menu")]
        private void TestToggleShopMenu()
        {
            Shop3DUI.Instance?.ToggleShopMenu();
        }

        [ContextMenu("Find Player Components")]
        private void FindPlayerComponents()
        {
            InitializeComponents();
            Debug.Log($"Found PlayerController: {playerController != null}");
            Debug.Log($"Found PlayerInteraction: {playerInteraction != null}");
            Debug.Log($"Found Camera: {playerCamera != null}");
        }
        #endif
        #endregion
    }
}
