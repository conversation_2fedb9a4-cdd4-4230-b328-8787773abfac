{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 11852, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 11852, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 11852, "tid": 645, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 11852, "tid": 645, "ts": 1749730894161682, "dur": 12, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 11852, "tid": 645, "ts": 1749730894161716, "dur": 4, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 11852, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 11852, "tid": 1, "ts": 1749730892815595, "dur": 1669, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 11852, "tid": 1, "ts": 1749730892817269, "dur": 30699, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 11852, "tid": 1, "ts": 1749730892847971, "dur": 28937, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 11852, "tid": 645, "ts": 1749730894161722, "dur": 12, "ph": "X", "name": "", "args": {}}, {"pid": 11852, "tid": 124554051584, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892815563, "dur": 17931, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892833496, "dur": 1327683, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892833512, "dur": 47, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892833563, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892833567, "dur": 315, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892833889, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892833925, "dur": 7, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892833934, "dur": 2875, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892836820, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892836824, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892836863, "dur": 2, "ph": "X", "name": "ProcessMessages 1628", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892836866, "dur": 33, "ph": "X", "name": "ReadAsync 1628", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892836904, "dur": 48, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892836956, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892836959, "dur": 44, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837005, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837008, "dur": 48, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837057, "dur": 3, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837061, "dur": 31, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837095, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837097, "dur": 49, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837150, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837203, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837205, "dur": 36, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837245, "dur": 2, "ph": "X", "name": "ProcessMessages 850", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837249, "dur": 44, "ph": "X", "name": "ReadAsync 850", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837296, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837298, "dur": 45, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837347, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837350, "dur": 35, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837387, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837390, "dur": 38, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837431, "dur": 1, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837433, "dur": 46, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837485, "dur": 2, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837492, "dur": 35, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837528, "dur": 2, "ph": "X", "name": "ProcessMessages 921", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837531, "dur": 46, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837580, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837583, "dur": 25, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837612, "dur": 28, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837642, "dur": 42, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837690, "dur": 2, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837693, "dur": 68, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837763, "dur": 2, "ph": "X", "name": "ProcessMessages 1230", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837767, "dur": 55, "ph": "X", "name": "ReadAsync 1230", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837828, "dur": 2, "ph": "X", "name": "ProcessMessages 965", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837832, "dur": 63, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837897, "dur": 2, "ph": "X", "name": "ProcessMessages 1093", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837900, "dur": 36, "ph": "X", "name": "ReadAsync 1093", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837938, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837940, "dur": 44, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837987, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892837989, "dur": 40, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838032, "dur": 1, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838035, "dur": 45, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838080, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838084, "dur": 23, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838110, "dur": 19, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838132, "dur": 15, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838150, "dur": 20, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838172, "dur": 24, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838198, "dur": 19, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838221, "dur": 18, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838242, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838282, "dur": 38, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838323, "dur": 2, "ph": "X", "name": "ProcessMessages 1162", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838326, "dur": 22, "ph": "X", "name": "ReadAsync 1162", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838351, "dur": 20, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838374, "dur": 19, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838395, "dur": 21, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838418, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838436, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838438, "dur": 18, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838458, "dur": 23, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838484, "dur": 21, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838508, "dur": 16, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838526, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838528, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838549, "dur": 18, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838568, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838571, "dur": 20, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838593, "dur": 20, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838616, "dur": 28, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838645, "dur": 8, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838654, "dur": 21, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838677, "dur": 18, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838700, "dur": 20, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838722, "dur": 19, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838744, "dur": 19, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838766, "dur": 19, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838787, "dur": 19, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838809, "dur": 20, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838831, "dur": 17, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838852, "dur": 20, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838874, "dur": 19, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838896, "dur": 34, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838932, "dur": 29, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838964, "dur": 28, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892838994, "dur": 21, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839017, "dur": 22, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839042, "dur": 24, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839069, "dur": 19, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839090, "dur": 61, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839154, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839158, "dur": 35, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839198, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839201, "dur": 36, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839240, "dur": 1, "ph": "X", "name": "ProcessMessages 773", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839241, "dur": 25, "ph": "X", "name": "ReadAsync 773", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839268, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839270, "dur": 45, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839318, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839319, "dur": 27, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839350, "dur": 24, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839377, "dur": 21, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839402, "dur": 24, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839428, "dur": 20, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839451, "dur": 18, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839472, "dur": 71, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839544, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839547, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839572, "dur": 52, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839626, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839647, "dur": 17, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839667, "dur": 24, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839693, "dur": 17, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839711, "dur": 17, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839730, "dur": 9, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839740, "dur": 14, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839759, "dur": 22, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839783, "dur": 16, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839802, "dur": 19, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839822, "dur": 21, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839846, "dur": 21, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839869, "dur": 17, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892839888, "dur": 117, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840007, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840025, "dur": 17, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840044, "dur": 19, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840066, "dur": 24, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840095, "dur": 24, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840121, "dur": 16, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840139, "dur": 16, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840159, "dur": 18, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840179, "dur": 13, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840196, "dur": 27, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840224, "dur": 2, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840227, "dur": 50, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840279, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840299, "dur": 16, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840318, "dur": 18, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840338, "dur": 17, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840357, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840380, "dur": 17, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840400, "dur": 25, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840427, "dur": 18, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840448, "dur": 16, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840466, "dur": 23, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840491, "dur": 17, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840510, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840535, "dur": 20, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840557, "dur": 19, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840579, "dur": 18, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840599, "dur": 15, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840616, "dur": 20, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840638, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840658, "dur": 15, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840676, "dur": 19, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840697, "dur": 30, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840730, "dur": 49, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840783, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840786, "dur": 37, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840825, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840829, "dur": 29, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840860, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840862, "dur": 17, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840881, "dur": 23, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840907, "dur": 30, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840941, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840943, "dur": 26, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840972, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892840998, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841001, "dur": 29, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841032, "dur": 20, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841055, "dur": 25, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841084, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841086, "dur": 30, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841118, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841119, "dur": 27, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841149, "dur": 27, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841178, "dur": 20, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841200, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841202, "dur": 22, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841226, "dur": 22, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841249, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841251, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841275, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841277, "dur": 28, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841308, "dur": 25, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841335, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841337, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841362, "dur": 23, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841386, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841388, "dur": 27, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841418, "dur": 22, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841442, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841444, "dur": 26, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841472, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841496, "dur": 1, "ph": "X", "name": "ProcessMessages 151", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841498, "dur": 22, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841522, "dur": 21, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841544, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841546, "dur": 29, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841579, "dur": 25, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841606, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841630, "dur": 23, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841658, "dur": 24, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841686, "dur": 18, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841706, "dur": 18, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841726, "dur": 19, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841746, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841748, "dur": 20, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841771, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841792, "dur": 18, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841812, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841832, "dur": 31, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841865, "dur": 24, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841892, "dur": 23, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841920, "dur": 27, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841949, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892841983, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842007, "dur": 22, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842032, "dur": 18, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842053, "dur": 19, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842074, "dur": 17, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842094, "dur": 20, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842116, "dur": 25, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842143, "dur": 17, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842163, "dur": 18, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842183, "dur": 18, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842203, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842227, "dur": 19, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842248, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842270, "dur": 20, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842293, "dur": 17, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842313, "dur": 25, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842340, "dur": 28, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842370, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842388, "dur": 26, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842417, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842439, "dur": 25, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842467, "dur": 25, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842496, "dur": 21, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842519, "dur": 145, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842666, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842696, "dur": 17, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842715, "dur": 17, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842734, "dur": 19, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842755, "dur": 14, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842771, "dur": 16, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842791, "dur": 24, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842817, "dur": 20, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842840, "dur": 17, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842860, "dur": 19, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842881, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842904, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842906, "dur": 19, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842928, "dur": 24, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842955, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892842987, "dur": 22, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843011, "dur": 23, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843036, "dur": 21, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843059, "dur": 18, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843080, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843100, "dur": 15, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843118, "dur": 28, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843149, "dur": 23, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843173, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843175, "dur": 20, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843198, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843220, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843221, "dur": 18, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843241, "dur": 19, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843264, "dur": 23, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843289, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843291, "dur": 66, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843359, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843361, "dur": 41, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843403, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843405, "dur": 31, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843438, "dur": 34, "ph": "X", "name": "ReadAsync 1030", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843474, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843476, "dur": 43, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843525, "dur": 2, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843529, "dur": 41, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843572, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843574, "dur": 31, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843609, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843651, "dur": 29, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843683, "dur": 30, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843716, "dur": 34, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843753, "dur": 30, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843784, "dur": 35, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843822, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843825, "dur": 46, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843872, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843876, "dur": 28, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843907, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843909, "dur": 35, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843945, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843947, "dur": 34, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843985, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892843987, "dur": 27, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844015, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844017, "dur": 42, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844062, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844064, "dur": 46, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844113, "dur": 2, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844116, "dur": 44, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844163, "dur": 34, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844200, "dur": 33, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844236, "dur": 23, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844262, "dur": 18, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844282, "dur": 19, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844303, "dur": 46, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844355, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844359, "dur": 32, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844394, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844396, "dur": 39, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844438, "dur": 28, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844469, "dur": 2, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844473, "dur": 46, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844521, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844523, "dur": 39, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844565, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844567, "dur": 40, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844610, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844611, "dur": 36, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844651, "dur": 2, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844655, "dur": 27, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844683, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844685, "dur": 37, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844725, "dur": 37, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844764, "dur": 2, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844768, "dur": 43, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844813, "dur": 1, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844818, "dur": 23, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844845, "dur": 23, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844871, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844872, "dur": 28, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844902, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844903, "dur": 33, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844938, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844943, "dur": 43, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844988, "dur": 1, "ph": "X", "name": "ProcessMessages 803", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892844990, "dur": 21, "ph": "X", "name": "ReadAsync 803", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845015, "dur": 36, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845054, "dur": 3, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845058, "dur": 32, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845094, "dur": 34, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845130, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845155, "dur": 24, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845183, "dur": 27, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845211, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845213, "dur": 18, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845234, "dur": 18, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845254, "dur": 19, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845275, "dur": 24, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845301, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845303, "dur": 29, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845335, "dur": 31, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845369, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845371, "dur": 31, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845406, "dur": 2, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845409, "dur": 42, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845454, "dur": 36, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845493, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845495, "dur": 67, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845565, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845568, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845608, "dur": 2, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845611, "dur": 42, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845657, "dur": 3, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845662, "dur": 35, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845700, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845704, "dur": 22, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845730, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845756, "dur": 20, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845779, "dur": 18, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845800, "dur": 20, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845823, "dur": 19, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845844, "dur": 17, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845864, "dur": 17, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845883, "dur": 18, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845903, "dur": 27, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845934, "dur": 21, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845956, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845957, "dur": 18, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892845977, "dur": 24, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846002, "dur": 9, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846013, "dur": 26, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846041, "dur": 22, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846067, "dur": 19, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846089, "dur": 20, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846111, "dur": 19, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846131, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846134, "dur": 19, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846155, "dur": 19, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846177, "dur": 19, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846200, "dur": 32, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846234, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846236, "dur": 41, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846280, "dur": 2, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846284, "dur": 28, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846315, "dur": 31, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846348, "dur": 1, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846350, "dur": 42, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846394, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846396, "dur": 34, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846433, "dur": 25, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846461, "dur": 27, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846489, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846520, "dur": 20, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846541, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846544, "dur": 20, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846565, "dur": 21, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846588, "dur": 18, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846608, "dur": 32, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846642, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846665, "dur": 28, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846695, "dur": 19, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846718, "dur": 21, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846742, "dur": 17, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846761, "dur": 21, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846785, "dur": 20, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846808, "dur": 17, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846828, "dur": 17, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846863, "dur": 21, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846885, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846887, "dur": 16, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846905, "dur": 17, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846924, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846943, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846965, "dur": 29, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846996, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892846998, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847017, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847038, "dur": 16, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847057, "dur": 24, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847083, "dur": 23, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847112, "dur": 23, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847137, "dur": 24, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847164, "dur": 18, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847184, "dur": 19, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847206, "dur": 18, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847225, "dur": 17, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847245, "dur": 19, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847266, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847295, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847297, "dur": 28, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847327, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847329, "dur": 30, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847361, "dur": 20, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847385, "dur": 19, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847407, "dur": 43, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847452, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847475, "dur": 19, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847497, "dur": 19, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847519, "dur": 20, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847542, "dur": 31, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847574, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847575, "dur": 21, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847599, "dur": 18, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847619, "dur": 20, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847641, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847662, "dur": 25, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847689, "dur": 18, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847710, "dur": 17, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847729, "dur": 18, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847748, "dur": 16, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847766, "dur": 17, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847785, "dur": 19, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847807, "dur": 18, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847827, "dur": 24, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847853, "dur": 18, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847873, "dur": 17, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847892, "dur": 15, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847909, "dur": 19, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847930, "dur": 17, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847949, "dur": 24, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847975, "dur": 17, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892847994, "dur": 19, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848015, "dur": 15, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848032, "dur": 18, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848052, "dur": 17, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848072, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848098, "dur": 25, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848125, "dur": 20, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848147, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848167, "dur": 19, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848188, "dur": 23, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848213, "dur": 15, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848230, "dur": 19, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848251, "dur": 19, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848273, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848292, "dur": 18, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848312, "dur": 17, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848332, "dur": 19, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848353, "dur": 35, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848392, "dur": 2, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848395, "dur": 26, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848422, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848424, "dur": 21, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848449, "dur": 24, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848475, "dur": 22, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848498, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848500, "dur": 22, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848523, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848525, "dur": 34, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848561, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848563, "dur": 43, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848610, "dur": 23, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848635, "dur": 33, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848671, "dur": 36, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848709, "dur": 2, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848712, "dur": 30, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848745, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848747, "dur": 38, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848787, "dur": 29, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848818, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848821, "dur": 33, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848856, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848858, "dur": 33, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848893, "dur": 15, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848910, "dur": 18, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848930, "dur": 17, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848949, "dur": 36, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892848987, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849005, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849026, "dur": 17, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849046, "dur": 13, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849062, "dur": 67, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849130, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849151, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849170, "dur": 17, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849189, "dur": 17, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849208, "dur": 53, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849263, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849289, "dur": 21, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849312, "dur": 15, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849329, "dur": 20, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849350, "dur": 55, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849407, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849432, "dur": 17, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849458, "dur": 21, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849481, "dur": 55, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849538, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849571, "dur": 24, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849598, "dur": 17, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849617, "dur": 63, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849683, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849712, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849713, "dur": 21, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849736, "dur": 68, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849807, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849834, "dur": 21, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849856, "dur": 16, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849874, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849941, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849974, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892849999, "dur": 19, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850019, "dur": 68, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850089, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850108, "dur": 17, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850127, "dur": 16, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850146, "dur": 15, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850163, "dur": 59, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850224, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850243, "dur": 18, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850263, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850282, "dur": 21, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850306, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850308, "dur": 53, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850363, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850382, "dur": 17, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850401, "dur": 16, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850418, "dur": 17, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850437, "dur": 73, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850514, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850554, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850556, "dur": 26, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850585, "dur": 61, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850647, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850668, "dur": 25, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850695, "dur": 40, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850737, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850738, "dur": 38, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850779, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850800, "dur": 16, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850818, "dur": 18, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850839, "dur": 66, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850907, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850930, "dur": 15, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850946, "dur": 23, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850972, "dur": 15, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892850989, "dur": 56, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851047, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851067, "dur": 23, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851092, "dur": 19, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851114, "dur": 83, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851201, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851204, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851240, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851243, "dur": 26, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851273, "dur": 56, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851334, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851371, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851373, "dur": 30, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851405, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851407, "dur": 55, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851464, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851487, "dur": 23, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851514, "dur": 27, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851542, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851544, "dur": 24, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851571, "dur": 66, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851640, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851686, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851688, "dur": 40, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851729, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851732, "dur": 35, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851769, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851771, "dur": 35, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851808, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851810, "dur": 31, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851844, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851898, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851938, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851940, "dur": 36, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851977, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892851979, "dur": 30, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852013, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852063, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852107, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852109, "dur": 22, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852135, "dur": 23, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852160, "dur": 17, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852180, "dur": 17, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852198, "dur": 23, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852224, "dur": 17, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852243, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852304, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852325, "dur": 136, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852463, "dur": 39, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852505, "dur": 1, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852507, "dur": 28, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852537, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852539, "dur": 20, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852563, "dur": 17, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852583, "dur": 18, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852603, "dur": 17, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852622, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852690, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852711, "dur": 20, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852733, "dur": 17, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852752, "dur": 86, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852842, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852891, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852893, "dur": 31, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852926, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852928, "dur": 46, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892852977, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853018, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853020, "dur": 35, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853057, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853059, "dur": 39, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853099, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853101, "dur": 36, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853139, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853142, "dur": 33, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853176, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853178, "dur": 36, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853215, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853218, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853266, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853303, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853305, "dur": 37, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853343, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853345, "dur": 31, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853380, "dur": 50, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853432, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853479, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853481, "dur": 33, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853516, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853518, "dur": 53, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853574, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853615, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853617, "dur": 34, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853653, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853655, "dur": 59, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853717, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853756, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853758, "dur": 19, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853779, "dur": 64, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853845, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853873, "dur": 26, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853902, "dur": 31, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853934, "dur": 29, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853965, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853967, "dur": 19, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892853988, "dur": 48, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854038, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854060, "dur": 22, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854085, "dur": 19, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854106, "dur": 15, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854122, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854178, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854198, "dur": 17, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854218, "dur": 18, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854238, "dur": 15, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854254, "dur": 61, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854317, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854337, "dur": 18, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854357, "dur": 23, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854382, "dur": 33, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854417, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854436, "dur": 64, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854501, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854517, "dur": 24, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854543, "dur": 21, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854566, "dur": 57, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854626, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854649, "dur": 19, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854670, "dur": 17, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854689, "dur": 64, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854756, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854776, "dur": 29, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854807, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854809, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854834, "dur": 49, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854886, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854909, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854911, "dur": 30, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854943, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854945, "dur": 25, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892854972, "dur": 59, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855034, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855067, "dur": 19, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855088, "dur": 17, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855107, "dur": 16, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855125, "dur": 22, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855149, "dur": 120, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855271, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855296, "dur": 19, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855317, "dur": 16, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855335, "dur": 16, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855353, "dur": 23, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855378, "dur": 15, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855395, "dur": 123, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855520, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855541, "dur": 20, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855562, "dur": 15, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855580, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855597, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855599, "dur": 17, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855618, "dur": 16, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855636, "dur": 122, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855759, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855778, "dur": 16, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855795, "dur": 1, "ph": "X", "name": "ProcessMessages 131", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855798, "dur": 16, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855816, "dur": 35, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855853, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855870, "dur": 15, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855887, "dur": 15, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855904, "dur": 15, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892855921, "dur": 112, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856035, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856053, "dur": 19, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856075, "dur": 15, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856092, "dur": 17, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856110, "dur": 16, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856128, "dur": 20, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856150, "dur": 65, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856217, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856236, "dur": 19, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856257, "dur": 19, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856277, "dur": 15, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856294, "dur": 56, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856352, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856371, "dur": 16, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856389, "dur": 16, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856407, "dur": 15, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856424, "dur": 58, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856483, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856519, "dur": 42, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856564, "dur": 2, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856566, "dur": 31, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856599, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856601, "dur": 30, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856634, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856654, "dur": 17, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856673, "dur": 18, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856693, "dur": 16, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856711, "dur": 52, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856764, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856798, "dur": 30, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856830, "dur": 16, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856848, "dur": 50, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856904, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856927, "dur": 15, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856944, "dur": 19, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856965, "dur": 9, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892856975, "dur": 58, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857035, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857059, "dur": 26, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857087, "dur": 17, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857106, "dur": 16, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857124, "dur": 55, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857181, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857207, "dur": 21, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857230, "dur": 16, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857248, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857305, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857334, "dur": 19, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857355, "dur": 16, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857373, "dur": 53, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857428, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857452, "dur": 21, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857475, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857496, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857514, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857537, "dur": 36, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857575, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857577, "dur": 21, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857601, "dur": 38, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857642, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857699, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857701, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857745, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857747, "dur": 38, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857787, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857789, "dur": 37, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857828, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857830, "dur": 26, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857859, "dur": 25, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857886, "dur": 19, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857907, "dur": 48, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857957, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892857986, "dur": 21, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858009, "dur": 25, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858036, "dur": 12, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858050, "dur": 29, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858081, "dur": 37, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858119, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858120, "dur": 18, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858140, "dur": 70, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858212, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858238, "dur": 106, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858347, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858394, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858396, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858434, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858437, "dur": 46, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858487, "dur": 7, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858496, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858545, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858547, "dur": 35, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858585, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858587, "dur": 28, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858617, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858619, "dur": 28, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858650, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858652, "dur": 37, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858692, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858694, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858746, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858748, "dur": 23, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858773, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858802, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858835, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858837, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858874, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858876, "dur": 31, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858909, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858911, "dur": 34, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858948, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858951, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858977, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892858979, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859009, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859037, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859039, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859070, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859072, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859101, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859103, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859129, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859131, "dur": 30, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859164, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859166, "dur": 32, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859201, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859203, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859233, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859235, "dur": 30, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859267, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859270, "dur": 27, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859299, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859302, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859336, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859339, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859368, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859370, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859400, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859402, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859430, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859432, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859461, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859463, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859491, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859493, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859520, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859522, "dur": 26, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859550, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859552, "dur": 24, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859579, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859581, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859610, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859612, "dur": 32, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859647, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859650, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859701, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859707, "dur": 53, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859762, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859766, "dur": 39, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859807, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859810, "dur": 29, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859841, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859843, "dur": 32, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859877, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859879, "dur": 37, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859919, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859922, "dur": 35, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859959, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859961, "dur": 26, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859990, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892859993, "dur": 29, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860023, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860025, "dur": 33, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860061, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860063, "dur": 42, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860108, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860110, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860148, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860151, "dur": 21, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860174, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860265, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860335, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860337, "dur": 28, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860366, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892860368, "dur": 2651, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892863028, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892863033, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892863084, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892863086, "dur": 399, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892863489, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892863518, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892863520, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892863552, "dur": 305, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892863862, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892863889, "dur": 2521, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892866415, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892866417, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892866457, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892866459, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892866493, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892866547, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892866566, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892866824, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892866849, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892866851, "dur": 232, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867087, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867123, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867126, "dur": 23, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867151, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867154, "dur": 150, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867307, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867327, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867428, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867457, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867699, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867722, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867745, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867767, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867787, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867808, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867835, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867837, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867862, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867865, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867891, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867893, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867918, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867920, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867942, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867976, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892867977, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868009, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868039, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868070, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868101, "dur": 120, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868224, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868256, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868275, "dur": 22, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868299, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868363, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868391, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868416, "dur": 23, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868441, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868443, "dur": 25, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868471, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868566, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868592, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868638, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868669, "dur": 55, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868726, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868755, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868778, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868924, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868963, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868965, "dur": 26, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892868994, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869020, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869023, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869129, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869150, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869192, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869211, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869284, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869307, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869352, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869374, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869403, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869430, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869460, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869487, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869513, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869515, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869536, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869561, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869598, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869622, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869647, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869671, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869702, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869720, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869745, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869799, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869826, "dur": 131, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869960, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869984, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892869987, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870030, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870058, "dur": 84, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870144, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870168, "dur": 223, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870395, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870427, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870430, "dur": 142, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870577, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870600, "dur": 24, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870626, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870817, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870834, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870837, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870862, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870897, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870919, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892870983, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871015, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871043, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871067, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871088, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871111, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871131, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871152, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871241, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871259, "dur": 144, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871406, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871431, "dur": 194, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871628, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871648, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871747, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871774, "dur": 143, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871919, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871944, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871967, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892871993, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872064, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872092, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872117, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872140, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872160, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872257, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872276, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872387, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872405, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872496, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872524, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872526, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892872559, "dur": 463, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892873026, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892873049, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892873114, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892873135, "dur": 772, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892873910, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892873934, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892874000, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892874018, "dur": 441, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892874461, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892874500, "dur": 111, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892874613, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892874646, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892874679, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892874700, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892874830, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892874849, "dur": 233, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892875086, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892875113, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892875117, "dur": 48510, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892923638, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892923643, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892923669, "dur": 34, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892923704, "dur": 4137, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892927850, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892927854, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892927904, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892927907, "dur": 280, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892928192, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892928226, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892928268, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892928296, "dur": 79, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892928380, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892928410, "dur": 661, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892929075, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892929077, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892929114, "dur": 188, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892929307, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892929353, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892929355, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892929391, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892929393, "dur": 964, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930361, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930363, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930398, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930400, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930496, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930527, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930529, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930556, "dur": 324, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930885, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930916, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892930918, "dur": 138, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931061, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931090, "dur": 340, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931434, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931436, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931458, "dur": 128, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931589, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931621, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931623, "dur": 66, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931692, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931723, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931879, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931913, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892931914, "dur": 375, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892932292, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892932320, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892932421, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892932450, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892932452, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892932503, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892932535, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892932594, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892932626, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892932628, "dur": 455, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933088, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933122, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933124, "dur": 266, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933395, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933428, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933430, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933507, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933536, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933538, "dur": 336, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933880, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933921, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933923, "dur": 71, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892933998, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934029, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934031, "dur": 431, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934467, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934504, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934506, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934534, "dur": 224, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934763, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934798, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934800, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934954, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892934978, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935254, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935277, "dur": 215, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935496, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935521, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935548, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935571, "dur": 317, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935891, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935917, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935920, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935956, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935995, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892935997, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936033, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936036, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936064, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936097, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936099, "dur": 15, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936117, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936144, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936174, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936176, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936203, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936234, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936235, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936269, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936271, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936297, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936326, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936354, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936356, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936381, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936384, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936417, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936441, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936473, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936501, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936505, "dur": 34, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936543, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936545, "dur": 35, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936582, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936584, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936618, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936620, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936655, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936657, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936686, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936688, "dur": 27, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936717, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936720, "dur": 38, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936761, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936764, "dur": 22, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936788, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936790, "dur": 60, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936853, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936908, "dur": 54, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936965, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892936991, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892937012, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892937034, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892937060, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892937127, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892937157, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892937180, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892937423, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892937447, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730892937472, "dur": 330981, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893268462, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893268466, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893268501, "dur": 23, "ph": "X", "name": "ProcessMessages 3010", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893268526, "dur": 36367, "ph": "X", "name": "ReadAsync 3010", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893304904, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893304909, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893304983, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893304991, "dur": 196927, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893501927, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893501932, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893501961, "dur": 37, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893502000, "dur": 102936, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893604946, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893604950, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893605012, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893605018, "dur": 137351, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893742378, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893742383, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893742409, "dur": 31, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893742441, "dur": 8208, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893750660, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893750665, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893750697, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893750701, "dur": 1506, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893752213, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893752215, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893752253, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893752277, "dur": 73263, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893825548, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893825552, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893825591, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893825594, "dur": 73315, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893898920, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893898925, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893898960, "dur": 28, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893898990, "dur": 6569, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893905569, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893905575, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893905605, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893905609, "dur": 465, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893906081, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893906085, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893906118, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730893906149, "dur": 176050, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894082209, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894082214, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894082266, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894082271, "dur": 763, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894083038, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894083040, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894083059, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894083085, "dur": 69729, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894152829, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894152837, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894152873, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894152877, "dur": 673, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894153555, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894153559, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894153619, "dur": 37, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894153661, "dur": 787, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894154453, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894154479, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 11852, "tid": 124554051584, "ts": 1749730894154480, "dur": 6690, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 11852, "tid": 645, "ts": 1749730894161744, "dur": 2437, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 11852, "tid": 120259084288, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 11852, "tid": 120259084288, "ts": 1749730892815513, "dur": 61406, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 11852, "tid": 120259084288, "ts": 1749730892876920, "dur": 57, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 11852, "tid": 645, "ts": 1749730894164184, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 11852, "tid": 115964116992, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 11852, "tid": 115964116992, "ts": 1749730892811992, "dur": 1349231, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 11852, "tid": 115964116992, "ts": 1749730892812141, "dur": 2865, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 11852, "tid": 115964116992, "ts": 1749730894161232, "dur": 65, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 11852, "tid": 115964116992, "ts": 1749730894161249, "dur": 27, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 11852, "tid": 115964116992, "ts": 1749730894161301, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 11852, "tid": 645, "ts": 1749730894164194, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1749730892832654, "dur": 50, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730892832751, "dur": 1705, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730892834466, "dur": 847, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730892835432, "dur": 71, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1749730892835503, "dur": 482, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730892836012, "dur": 21488, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730892857513, "dur": 1295406, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730894152921, "dur": 399, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730894153321, "dur": 67, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730894153565, "dur": 52, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730894153629, "dur": 70, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730894153729, "dur": 1175, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1749730892836435, "dur": 21114, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892857554, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_1BD15D364A91220E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749730892857707, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_4B4D0A166E0D20DC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749730892858023, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B739A7DF6FD203B7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749730892858478, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749730892858553, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749730892858650, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749730892858715, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749730892858799, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1749730892858982, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1749730892859378, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892859540, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892859965, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892860229, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892860418, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892860612, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892860821, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892860995, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892861174, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892861335, "dur": 479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892861815, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892861994, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892862180, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892862414, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892862614, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892862916, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892863120, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892863422, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892863612, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892863788, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892863998, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892864202, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892864387, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892864579, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892864762, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892864952, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892865136, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892865365, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892865749, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892866165, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749730892866378, "dur": 1098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749730892867513, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892867735, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1749730892867915, "dur": 913, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1749730892868883, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892869282, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892869723, "dur": 4067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892873790, "dur": 51158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892924950, "dur": 3579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749730892928567, "dur": 2173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749730892930742, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892930873, "dur": 1997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749730892932871, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892933273, "dur": 2463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1749730892935847, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730892936034, "dur": 813578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1749730893749646, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749730893749614, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749730893749873, "dur": 1595, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1749730893751471, "dur": 401477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892836372, "dur": 21152, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892857984, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_7D559772BEA705DB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749730892858508, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749730892858698, "dur": 3267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749730892862071, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892862264, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892862529, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892862708, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892862909, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892863108, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892863340, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892863578, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892863804, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892863975, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892864167, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892864355, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892864536, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892864723, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892864942, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892865162, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892865298, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892865739, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892866179, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749730892866398, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749730892866890, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892867171, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892867235, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892867734, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749730892867911, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749730892868341, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892868403, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892868852, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749730892869011, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1749730892869434, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892869718, "dur": 4056, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892873787, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1749730892873955, "dur": 50970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892924928, "dur": 3576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749730892928506, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1749730892928575, "dur": 2247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749730892930866, "dur": 2242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749730892933148, "dur": 3526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1749730892936709, "dur": 1216238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749730892836526, "dur": 21045, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749730892857578, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_1C3753EAD72C002A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892857695, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749730892857881, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_B2FE57DE340CBDFF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892858035, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F75569858C7FEC6C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892858507, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892858725, "dur": 3998, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892862791, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892863129, "dur": 2517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892865739, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892865823, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892866148, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892866302, "dur": 1223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749730892867528, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892868038, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892868225, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892868683, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749730892868850, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892869003, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892869710, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892869871, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892870369, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892870519, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892871245, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892871338, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892872303, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892872382, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892873194, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892873277, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892873783, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1749730892873883, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892874102, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730892874412, "dur": 393332, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730893269421, "dur": 33987, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749730893269123, "dur": 34390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730893303972, "dur": 106, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749730893304566, "dur": 196593, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1749730893502476, "dur": 319525, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1749730893502469, "dur": 321123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749730893824607, "dur": 150, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1749730893825033, "dur": 73129, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1749730893904621, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749730893904612, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749730893904783, "dur": 562, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1749730893905351, "dur": 247557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892836419, "dur": 21119, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892857985, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892858548, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1749730892858625, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892858699, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1749730892858944, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1749730892859412, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892859538, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892859838, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892860117, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892860296, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892860550, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892860780, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892860960, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892861149, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892861326, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892861820, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892861997, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892862365, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@f10877fbf761\\Editor\\Data\\Graphs\\DataStore.cs"}}, {"pid": 12345, "tid": 4, "ts": 1749730892862268, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892862993, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892863187, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892863413, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892863622, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892863826, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892864011, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892864200, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892864632, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892864819, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892865083, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892865344, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892865718, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892866168, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749730892866380, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749730892867655, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1749730892867820, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892868194, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1749730892868658, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892868746, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892868868, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892869278, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892869715, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892870390, "dur": 3398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892873789, "dur": 51112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892924903, "dur": 5625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749730892930530, "dur": 1810, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892932348, "dur": 2211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1749730892934560, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892935377, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892935714, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892935778, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892936006, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1749730892936139, "dur": 1216789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892836467, "dur": 21090, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892857883, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_77C9B2F6CDEA9940.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749730892858029, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_CF814559B1575A99.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749730892858472, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1749730892858884, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1749730892858977, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1749730892859208, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1749730892859397, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892859543, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892860218, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892860427, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892860624, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892860904, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892861089, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892861359, "dur": 864, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Move\\MoveItemModeMix.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749730892861305, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892862380, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892862577, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892862783, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892863024, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892863269, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892863492, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892863693, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892863905, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892864101, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892864290, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892864464, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892864655, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892864908, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@c854d1f7d97f\\Editor\\AssetMenu\\AssetCopyPathOperation.cs"}}, {"pid": 12345, "tid": 5, "ts": 1749730892864849, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892865644, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892865710, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892866150, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749730892866360, "dur": 725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749730892867086, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892867171, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749730892867340, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749730892867736, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892868042, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892868778, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892868859, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892869277, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892869713, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1749730892869875, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1749730892870287, "dur": 3504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892873792, "dur": 51161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892924956, "dur": 2824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749730892927829, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749730892930203, "dur": 754, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892930968, "dur": 1983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749730892932952, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892933177, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1749730892935438, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892935663, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1749730892935723, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892936010, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1749730892936280, "dur": 1216664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892836550, "dur": 21046, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892857603, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3F8B9D82C8E678D4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749730892857698, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892857987, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_1E5BE8A4F6F01B45.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749730892858546, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749730892858767, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749730892858871, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749730892859328, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1749730892859405, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892859571, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892859916, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892860125, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892860317, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892860824, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892861028, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892861198, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892861361, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892861857, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892862051, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892862230, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892862439, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892862618, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892862878, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892863089, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892863321, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892863572, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892863798, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892863974, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892864209, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892864426, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892864598, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892864764, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892864985, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892865072, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892865350, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892865721, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892866373, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749730892866562, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892866705, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749730892867315, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892867741, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892868040, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749730892868219, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749730892868786, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892868858, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892869300, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892869717, "dur": 1536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892871255, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1749730892871402, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1749730892871780, "dur": 2002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892873783, "dur": 51130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892924915, "dur": 4303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749730892929220, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892929635, "dur": 1895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749730892931568, "dur": 2167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749730892933736, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892933795, "dur": 2030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749730892935827, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1749730892935961, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1749730892936051, "dur": 1216854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892836583, "dur": 21028, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892858007, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E3FF4E8988E07430.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749730892858714, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749730892858822, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1749730892859014, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749730892859319, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1749730892859405, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892859570, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892860517, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892860707, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892860887, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892861075, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892861250, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892861735, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892861905, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892862092, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892862289, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892862550, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892862766, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892862985, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892863190, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892863407, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892863608, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892863827, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892863999, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892864257, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892864432, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892864621, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892864803, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892865041, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892865300, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892865737, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892866162, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749730892866390, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749730892867005, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749730892867394, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892868041, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749730892868208, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749730892868780, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892868865, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892869305, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892869715, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749730892869879, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749730892870371, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749730892870521, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749730892871230, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1749730892871340, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1749730892871678, "dur": 2121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892873799, "dur": 51099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892924900, "dur": 3431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749730892928333, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892928613, "dur": 2347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749730892930961, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892931144, "dur": 2232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749730892933377, "dur": 352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1749730892933751, "dur": 2350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1749730892936145, "dur": 1216790, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892836617, "dur": 21017, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892857818, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_0C9A092533558E41.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749730892857974, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_1E95AC98D1BD4446.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749730892858059, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892858825, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1749730892859354, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1749730892859441, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892859566, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892859836, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892860051, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892860245, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892860493, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892860673, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892860903, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892861084, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892861748, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892861936, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892862116, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892862291, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892862571, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892862792, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892863003, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892863192, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892863419, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892863639, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892863815, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892864034, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892864270, "dur": 182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892864452, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892864779, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892865003, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892865295, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892865709, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892866159, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749730892866364, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749730892866963, "dur": 527, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892867555, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749730892867730, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749730892868217, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749730892868465, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749730892868923, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892869279, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892869709, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1749730892869864, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1749730892870190, "dur": 3589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892873780, "dur": 2460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892877147, "dur": 159, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 8, "ts": 1749730892877307, "dur": 675, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/6000.0.50f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 8, "ts": 1749730892876240, "dur": 1790, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892878030, "dur": 46874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892924909, "dur": 3884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749730892928794, "dur": 1343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892930147, "dur": 1956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749730892932103, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892932373, "dur": 2199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1749730892934573, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892934829, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892935612, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1749730892935725, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1749730892936007, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1749730892936270, "dur": 1216670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892836646, "dur": 21005, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892857796, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_94280B12CFD84F1A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749730892857997, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_8D701D583C70E786.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749730892858703, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749730892858905, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1749730892859418, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892859586, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892859826, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892860083, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892860757, "dur": 1458, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\Models\\Operators\\Implementations\\SquareWave.cs"}}, {"pid": 12345, "tid": 9, "ts": 1749730892860571, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892862243, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892862451, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892862642, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892862812, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892863029, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892863263, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892863911, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892864113, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892864306, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892864938, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892865133, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892865418, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892865734, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892866169, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749730892866388, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749730892867006, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892867173, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892867725, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1749730892867922, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1749730892868374, "dur": 344, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892868740, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892868876, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892869284, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892869729, "dur": 4065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892873795, "dur": 51141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892924938, "dur": 2982, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749730892927922, "dur": 657, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892928585, "dur": 2338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749730892930924, "dur": 925, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730892931860, "dur": 3858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749730892936024, "dur": 333104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730893269146, "dur": 332293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749730893269130, "dur": 333821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749730893603994, "dur": 156, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1749730893604485, "dur": 137138, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1749730893749618, "dur": 331769, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749730893749606, "dur": 331783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749730894081420, "dur": 926, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1749730894082351, "dur": 70560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892836723, "dur": 20947, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892857677, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_9F0F250247658205.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749730892857816, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_2877E6341DB76B77.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749730892858000, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_3EE9BB20E930FDFD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749730892858676, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1749730892859063, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749730892859240, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1749730892859413, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892859548, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892859823, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892860427, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892860918, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892861090, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892861747, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892861915, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892862098, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892862273, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892862543, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892862732, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892862952, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892863247, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892863523, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892863749, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892863945, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892864539, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892864751, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892864989, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892865293, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892865711, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892866154, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749730892866318, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892866393, "dur": 1264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749730892867723, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749730892867913, "dur": 1137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749730892869096, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749730892869234, "dur": 993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749730892870282, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1749730892870383, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1749730892870687, "dur": 3121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892873809, "dur": 51125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892924936, "dur": 2939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749730892927876, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730892928640, "dur": 2004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749730892930702, "dur": 2049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749730892932784, "dur": 3137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1749730892936045, "dur": 968574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1749730893904652, "dur": 247307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749730893904621, "dur": 247341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1749730894151992, "dur": 845, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749730892836801, "dur": 20889, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892857699, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_025D68ED9798F53F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749730892858014, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_9CC28255281D0D3B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749730892858576, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749730892858711, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749730892858956, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1749730892859256, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1749730892859434, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892859583, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892859885, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892860095, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892860273, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892860509, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892860704, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892860914, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892861088, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892861262, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892861749, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892861949, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892862128, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892862352, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892862551, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892862731, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892862939, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892863157, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892863383, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892863661, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892863859, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892864075, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892864268, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892864464, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892864650, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892864846, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892865095, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892865372, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892865748, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892866175, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749730892866397, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749730892867083, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892867192, "dur": 534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892867727, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1749730892867836, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892868271, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1749730892868720, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892868869, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892869282, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892869721, "dur": 4090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892873812, "dur": 51097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892924941, "dur": 2651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749730892927652, "dur": 2081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749730892929773, "dur": 5667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1749730892935483, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892935790, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1749730892936014, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1749730892936724, "dur": 1216212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892836849, "dur": 20861, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892857718, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_46081861FEA49A1E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749730892857989, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_136791916CB4C4E9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749730892858623, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892858797, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749730892858936, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749730892859065, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1749730892859414, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892859619, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892860563, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892860862, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892861040, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892861210, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892861709, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892861874, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892862050, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892862232, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892862447, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892862680, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892862928, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892863126, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892863361, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892863593, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892864141, "dur": 186, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892864327, "dur": 176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892864503, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892864687, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892864883, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892865073, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892865399, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892865735, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892866171, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749730892866368, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749730892866582, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749730892867178, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892867728, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892868055, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892868866, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1749730892868979, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1749730892869274, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1749730892869692, "dur": 106, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892870121, "dur": 52760, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1749730892924898, "dur": 2112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749730892927011, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892927453, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749730892929979, "dur": 1789, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892931782, "dur": 2205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749730892934034, "dur": 2167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1749730892936242, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1749730892936313, "dur": 1216651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892836908, "dur": 20815, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892857776, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_89505EFF408B1938.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749730892857991, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892858603, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749730892859009, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749730892859179, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749730892859399, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892859560, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1749730892859630, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892860068, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892860242, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892860438, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892860759, "dur": 1005, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\Models\\Operators\\Implementations\\LoadTexture2DArray.cs"}}, {"pid": 12345, "tid": 13, "ts": 1749730892860642, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892861853, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892862036, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892862404, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892862606, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892862778, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892863382, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892863629, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892863834, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892864011, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892864220, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892864414, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892864607, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892864781, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892865311, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892865742, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892866163, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749730892866356, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749730892867038, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892867172, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892867736, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892868050, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892868853, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749730892868982, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1749730892869299, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892869723, "dur": 4054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892873779, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1749730892873926, "dur": 53115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892927043, "dur": 3288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749730892930333, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892930737, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749730892933145, "dur": 1981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1749730892935192, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892935428, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892935622, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892935955, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1749730892936013, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1749730892936427, "dur": 1216527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892836956, "dur": 20775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892857808, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D65C8B23C6E41D35.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749730892857987, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892858380, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749730892858509, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749730892858568, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749730892858667, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749730892858908, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1749730892859133, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1749730892859423, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892859554, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892860090, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892860269, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892860514, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892860684, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892860960, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892861129, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892861287, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892861876, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892862071, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892862470, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892862676, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892862860, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892863076, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892863299, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892863509, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892863718, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892863908, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892864106, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892864305, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892864499, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892864705, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892864871, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892865294, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892865708, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892866153, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749730892866374, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749730892867039, "dur": 653, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892867736, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749730892867919, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749730892868485, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892868565, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892868628, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892868861, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892869276, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892869721, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1749730892869840, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1749730892870131, "dur": 3650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892873781, "dur": 4255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892878037, "dur": 46879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892924917, "dur": 2093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749730892927077, "dur": 2994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749730892930072, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892930330, "dur": 1952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749730892932283, "dur": 377, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892932668, "dur": 2036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1749730892934705, "dur": 899, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892935649, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1749730892935854, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Searcher.Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1749730892936054, "dur": 1216870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892836994, "dur": 20751, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892857751, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_CDEA261D3E32C133.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749730892857881, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_55EFA22634AA8841.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749730892857992, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_1DB8604BF3C2B15B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749730892858468, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749730892858571, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1749730892858644, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892859010, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749730892859176, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1749730892859435, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892859560, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892859985, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892860124, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892860391, "dur": 1308, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@fe5368074162\\Editor\\LightExplorer.cs"}}, {"pid": 12345, "tid": 15, "ts": 1749730892860305, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892861789, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892861957, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892862137, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892862323, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892862566, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892862727, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892862912, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892863123, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892863341, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892863570, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892863815, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892864021, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892864274, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892864445, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892864654, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892864845, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892865632, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892865715, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892866165, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749730892866366, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892866915, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892867015, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892867347, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749730892867495, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892867906, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892868212, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892868854, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892869099, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749730892869190, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892869246, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892870308, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749730892870416, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892870916, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749730892871015, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892871415, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1749730892871535, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892871806, "dur": 1981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892873787, "dur": 51119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892924908, "dur": 2766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892927676, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892927739, "dur": 2044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892929816, "dur": 1839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892931692, "dur": 2197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892933889, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1749730892934528, "dur": 1848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1749730892936415, "dur": 1216536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892837037, "dur": 20732, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892857770, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_332FA14DD40CDB89.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749730892857836, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_332FA14DD40CDB89.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749730892858023, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C9BF491CE9429A6C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749730892858516, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892858589, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749730892859131, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1749730892859426, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892859557, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892859892, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892860341, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892860775, "dur": 1538, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@e07c4c789cd8\\Editor\\Models\\Operators\\Implementations\\Noise.cs"}}, {"pid": 12345, "tid": 16, "ts": 1749730892860625, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892862384, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892862652, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892862831, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892863009, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892863204, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892863463, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892863697, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892863900, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892864127, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892864310, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892864525, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892864824, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892865045, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892865330, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892865722, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892866156, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749730892866402, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749730892867017, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749730892867210, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892867279, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749730892867582, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892867699, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749730892867836, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749730892868285, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892868863, "dur": 415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892869279, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892869716, "dur": 1205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892870922, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1749730892871031, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1749730892871379, "dur": 2406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892873786, "dur": 51143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892924931, "dur": 2525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749730892927457, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892927537, "dur": 2152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749730892929691, "dur": 460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892930157, "dur": 3384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749730892933541, "dur": 677, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1749730892934227, "dur": 1994, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1749730892936263, "dur": 1216675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1749730894158757, "dur": 1187, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 11852, "tid": 645, "ts": 1749730894164241, "dur": 20637, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 11852, "tid": 645, "ts": 1749730894184904, "dur": 425, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 11852, "tid": 645, "ts": 1749730894161696, "dur": 23661, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}