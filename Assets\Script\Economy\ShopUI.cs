using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace EconomySystem
{
    /// <summary>
    /// UI chính của hệ thống cửa hàng
    /// Quản lý hiển thị danh sách vật phẩm và bộ lọc
    /// </summary>
    public class ShopUI : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Transform containerVatPham;
        [SerializeField] private GameObject prefabShopItem;
        [SerializeField] private ScrollRect scrollRect;

        [Header("Bộ Lọc")]
        [SerializeField] private TMP_Dropdown dropdownLoaiVatPham;
        [SerializeField] private TMP_InputField inputTimKiem;
        [SerializeField] private Toggle toggleVatPhamMoi;
        [SerializeField] private Toggle toggleVatPhamNoiBat;
        [SerializeField] private Button buttonLamMoi;

        [Header("Thông Tin")]
        [SerializeField] private TextMeshProUGUI textTongSoVatPham;
        [SerializeField] private TextMeshProUG<PERSON> textThongBao;

        [Header("Cài Đặt")]
        [SerializeField] private bool tuDongCapNhat = true;
        [SerializeField] private bool hienThiLog = false;
        [SerializeField] private float thoiGianDelayTimKiem = 0.5f;

        private List<ShopItemUI> danhSachShopItemUI = new List<ShopItemUI>();
        private List<Item> danhSachVatPhamHienTai = new List<Item>();
        private Coroutine coroutineTimKiem;

        #region Unity Methods
        private void Start()
        {
            KhoiTaoUI();
            
            if (tuDongCapNhat)
            {
                DangKyEvents();
            }

            CapNhatDanhSachVatPham();
        }

        private void OnDestroy()
        {
            HuyDangKyEvents();
        }
        #endregion

        #region Initialization
        private void KhoiTaoUI()
        {
            // Thiết lập dropdown loại vật phẩm
            if (dropdownLoaiVatPham != null)
            {
                ThietLapDropdownLoaiVatPham();
                dropdownLoaiVatPham.onValueChanged.AddListener(OnLoaiVatPhamThayDoi);
            }

            // Thiết lập input tìm kiếm
            if (inputTimKiem != null)
            {
                inputTimKiem.onValueChanged.AddListener(OnTimKiemThayDoi);
            }

            // Thiết lập toggle
            if (toggleVatPhamMoi != null)
            {
                toggleVatPhamMoi.onValueChanged.AddListener(OnToggleVatPhamMoi);
            }

            if (toggleVatPhamNoiBat != null)
            {
                toggleVatPhamNoiBat.onValueChanged.AddListener(OnToggleVatPhamNoiBat);
            }

            // Thiết lập button làm mới
            if (buttonLamMoi != null)
            {
                buttonLamMoi.onClick.AddListener(LamMoiDanhSach);
            }

            // Kiểm tra prefab
            if (prefabShopItem == null)
            {
                Debug.LogError("Prefab ShopItem chưa được gán!");
            }

            if (containerVatPham == null)
            {
                Debug.LogError("Container vật phẩm chưa được gán!");
            }
        }

        private void ThietLapDropdownLoaiVatPham()
        {
            dropdownLoaiVatPham.ClearOptions();
            
            var options = new List<string> { "Tất Cả" };
            
            // Thêm các loại vật phẩm
            foreach (ItemType loai in System.Enum.GetValues(typeof(ItemType)))
            {
                options.Add(LayTenLoaiVatPham(loai));
            }

            dropdownLoaiVatPham.AddOptions(options);
        }

        private void DangKyEvents()
        {
            ShopManager.OnShopUpdated += OnShopCapNhat;
            ShopManager.OnItemPurchased += OnMuaVatPham;
            ShopManager.OnTransactionFailed += OnGiaoDichThatBai;
        }

        private void HuyDangKyEvents()
        {
            ShopManager.OnShopUpdated -= OnShopCapNhat;
            ShopManager.OnItemPurchased -= OnMuaVatPham;
            ShopManager.OnTransactionFailed -= OnGiaoDichThatBai;
        }
        #endregion

        #region Event Handlers
        private void OnShopCapNhat()
        {
            CapNhatDanhSachVatPham();
        }

        private void OnMuaVatPham(Item item, int soLuong)
        {
            HienThiThongBao($"Đã mua {soLuong}x {item.TenVatPham}!", Color.green);
            
            if (hienThiLog)
                Debug.Log($"Shop UI: Đã mua {soLuong}x {item.TenVatPham}");
        }

        private void OnGiaoDichThatBai(string lyDo)
        {
            HienThiThongBao(lyDo, Color.red);
        }

        private void OnLoaiVatPhamThayDoi(int index)
        {
            ApDungBoLoc();
        }

        private void OnTimKiemThayDoi(string tuKhoa)
        {
            if (coroutineTimKiem != null)
            {
                StopCoroutine(coroutineTimKiem);
            }

            coroutineTimKiem = StartCoroutine(DelayTimKiem(tuKhoa));
        }

        private void OnToggleVatPhamMoi(bool isOn)
        {
            ApDungBoLoc();
        }

        private void OnToggleVatPhamNoiBat(bool isOn)
        {
            ApDungBoLoc();
        }
        #endregion

        #region UI Updates
        /// <summary>
        /// Cập nhật danh sách vật phẩm
        /// </summary>
        public void CapNhatDanhSachVatPham()
        {
            if (ShopManager.Instance == null || ShopManager.Instance.ItemDatabase == null)
            {
                HienThiThongBao("Cửa hàng chưa sẵn sàng!", Color.red);
                return;
            }

            // Lấy danh sách vật phẩm
            danhSachVatPhamHienTai = ShopManager.Instance.LayDanhSachVatPhamBan();

            // Áp dụng bộ lọc
            ApDungBoLoc();
        }

        /// <summary>
        /// Áp dụng bộ lọc
        /// </summary>
        private void ApDungBoLoc()
        {
            if (ShopManager.Instance?.ItemDatabase == null) return;

            var danhSachLoc = new List<Item>(danhSachVatPhamHienTai);

            // Lọc theo loại
            if (dropdownLoaiVatPham != null && dropdownLoaiVatPham.value > 0)
            {
                ItemType loaiDaChon = (ItemType)(dropdownLoaiVatPham.value - 1);
                danhSachLoc = ShopManager.Instance.LayDanhSachVatPhamTheoLoai(loaiDaChon);
            }

            // Lọc theo từ khóa tìm kiếm
            if (inputTimKiem != null && !string.IsNullOrEmpty(inputTimKiem.text))
            {
                danhSachLoc = ShopManager.Instance.TimKiemVatPham(inputTimKiem.text);
            }

            // Lọc vật phẩm mới
            if (toggleVatPhamMoi != null && toggleVatPhamMoi.isOn)
            {
                danhSachLoc = ShopManager.Instance.ItemDatabase.LayVatPhamMoi();
            }

            // Lọc vật phẩm nổi bật
            if (toggleVatPhamNoiBat != null && toggleVatPhamNoiBat.isOn)
            {
                danhSachLoc = ShopManager.Instance.ItemDatabase.LayVatPhamNoiBat();
            }

            // Hiển thị danh sách đã lọc
            HienThiDanhSachVatPham(danhSachLoc);
        }

        /// <summary>
        /// Hiển thị danh sách vật phẩm
        /// </summary>
        /// <param name="danhSach">Danh sách vật phẩm cần hiển thị</param>
        private void HienThiDanhSachVatPham(List<Item> danhSach)
        {
            // Xóa các item UI cũ
            XoaTatCaShopItemUI();

            // Tạo item UI mới
            foreach (var item in danhSach)
            {
                TaoShopItemUI(item);
            }

            // Cập nhật thông tin tổng
            CapNhatThongTinTong(danhSach.Count);

            // Reset scroll về đầu
            if (scrollRect != null)
            {
                scrollRect.verticalNormalizedPosition = 1f;
            }

            if (hienThiLog)
                Debug.Log($"Hiển thị {danhSach.Count} vật phẩm trong shop");
        }

        /// <summary>
        /// Tạo UI cho một vật phẩm
        /// </summary>
        /// <param name="item">Vật phẩm</param>
        private void TaoShopItemUI(Item item)
        {
            if (prefabShopItem == null || containerVatPham == null) return;

            GameObject itemObj = Instantiate(prefabShopItem, containerVatPham);
            ShopItemUI shopItemUI = itemObj.GetComponent<ShopItemUI>();

            if (shopItemUI != null)
            {
                shopItemUI.ThietLapVatPham(item);
                danhSachShopItemUI.Add(shopItemUI);
            }
            else
            {
                Debug.LogError("Prefab ShopItem không có component ShopItemUI!");
                Destroy(itemObj);
            }
        }

        /// <summary>
        /// Xóa tất cả shop item UI
        /// </summary>
        private void XoaTatCaShopItemUI()
        {
            foreach (var shopItemUI in danhSachShopItemUI)
            {
                if (shopItemUI != null)
                {
                    Destroy(shopItemUI.gameObject);
                }
            }

            danhSachShopItemUI.Clear();
        }

        /// <summary>
        /// Cập nhật thông tin tổng
        /// </summary>
        /// <param name="soLuongVatPham">Số lượng vật phẩm hiển thị</param>
        private void CapNhatThongTinTong(int soLuongVatPham)
        {
            if (textTongSoVatPham != null)
            {
                textTongSoVatPham.text = $"Hiển thị: {soLuongVatPham} vật phẩm";
            }
        }

        /// <summary>
        /// Hiển thị thông báo
        /// </summary>
        /// <param name="noiDung">Nội dung thông báo</param>
        /// <param name="mau">Màu sắc</param>
        private void HienThiThongBao(string noiDung, Color mau)
        {
            if (textThongBao != null)
            {
                textThongBao.text = noiDung;
                textThongBao.color = mau;
                textThongBao.gameObject.SetActive(true);

                // Tự động ẩn sau 3 giây
                Invoke(nameof(AnThongBao), 3f);
            }

            if (hienThiLog)
                Debug.Log($"Shop UI Thông báo: {noiDung}");
        }

        /// <summary>
        /// Ẩn thông báo
        /// </summary>
        private void AnThongBao()
        {
            if (textThongBao != null)
            {
                textThongBao.gameObject.SetActive(false);
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Làm mới danh sách
        /// </summary>
        public void LamMoiDanhSach()
        {
            CapNhatDanhSachVatPham();
            HienThiThongBao("Đã làm mới danh sách!", Color.blue);
        }

        /// <summary>
        /// Đặt bộ lọc loại vật phẩm
        /// </summary>
        /// <param name="loai">Loại vật phẩm</param>
        public void DatBoLocLoai(ItemType loai)
        {
            if (dropdownLoaiVatPham != null)
            {
                dropdownLoaiVatPham.value = (int)loai + 1;
            }
        }

        /// <summary>
        /// Đặt từ khóa tìm kiếm
        /// </summary>
        /// <param name="tuKhoa">Từ khóa</param>
        public void DatTuKhoaTimKiem(string tuKhoa)
        {
            if (inputTimKiem != null)
            {
                inputTimKiem.text = tuKhoa;
            }
        }

        /// <summary>
        /// Xóa bộ lọc
        /// </summary>
        public void XoaBoLoc()
        {
            if (dropdownLoaiVatPham != null)
                dropdownLoaiVatPham.value = 0;

            if (inputTimKiem != null)
                inputTimKiem.text = "";

            if (toggleVatPhamMoi != null)
                toggleVatPhamMoi.isOn = false;

            if (toggleVatPhamNoiBat != null)
                toggleVatPhamNoiBat.isOn = false;

            ApDungBoLoc();
        }
        #endregion

        #region Private Methods
        private System.Collections.IEnumerator DelayTimKiem(string tuKhoa)
        {
            yield return new WaitForSeconds(thoiGianDelayTimKiem);
            ApDungBoLoc();
            coroutineTimKiem = null;
        }

        private string LayTenLoaiVatPham(ItemType loai)
        {
            switch (loai)
            {
                case ItemType.VatLieu: return "Vật Liệu";
                case ItemType.CongCu: return "Công Cụ";
                case ItemType.VuKhi: return "Vũ Khí";
                case ItemType.GiapGiap: return "Giáp";
                case ItemType.TieuHao: return "Tiêu Hao";
                case ItemType.QuyGia: return "Quý Giá";
                case ItemType.Khac: return "Khác";
                default: return "Không Xác Định";
            }
        }
        #endregion

        #region Properties
        /// <summary>
        /// Số lượng vật phẩm đang hiển thị
        /// </summary>
        public int SoLuongVatPhamHienThi => danhSachShopItemUI.Count;

        /// <summary>
        /// Danh sách vật phẩm hiện tại
        /// </summary>
        public List<Item> DanhSachVatPhamHienTai => new List<Item>(danhSachVatPhamHienTai);
        #endregion
    }
}
