using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

namespace EconomySystem
{
    /// <summary>
    /// UI component hiển thị số dư tiền tệ Lea
    /// Hỗ trợ hiệu ứng và cập nhật real-time
    /// </summary>
    public class CurrencyUI : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private TextMeshProUGUI textSoTien;
        [SerializeField] private TextMeshProUGUI textTenTienTe;
        [SerializeField] private Image iconTienTe;
        [SerializeField] private Button buttonThongTin;

        [Header("Hiệu Ứng")]
        [SerializeField] private bool coHieuUngThayDoi = true;
        [SerializeField] private float thoiGianHieuUng = 0.5f;
        [SerializeField] private AnimationCurve curveTangGiam = AnimationCurve.EaseInOut(0, 1, 1, 1.2f);
        [SerializeField] private Color mauTang = Color.green;
        [SerializeField] private Color mauGiam = Color.red;
        [SerializeField] private Color mauBinhThuong = Color.white;

        [Header("Định Dạng")]
        [SerializeField] private string dinhDangSoTien = "N0"; // Định dạng số (N0 = 1,000)
        [SerializeField] private string tieuDeTienTe = "Lea";
        [SerializeField] private bool hienThiTieuDe = true;

        [Header("Cài Đặt")]
        [SerializeField] private bool tuDongCapNhat = true;
        [SerializeField] private bool hienThiLog = false;

        private int soTienHienTai = 0;
        private int soTienTruoc = 0;
        private Coroutine coroutineHieuUng;

        #region Unity Methods
        private void Start()
        {
            KhoiTaoUI();
            
            if (tuDongCapNhat)
            {
                DangKyEvents();
            }

            CapNhatHienThi();
        }

        private void OnDestroy()
        {
            HuyDangKyEvents();
        }

        #if UNITY_EDITOR
        private void OnValidate()
        {
            if (Application.isPlaying)
                CapNhatHienThi();
        }
        #endif
        #endregion

        #region Initialization
        private void KhoiTaoUI()
        {
            // Thiết lập tên tiền tệ
            if (textTenTienTe != null && hienThiTieuDe)
            {
                textTenTienTe.text = tieuDeTienTe;
            }
            else if (textTenTienTe != null)
            {
                textTenTienTe.gameObject.SetActive(false);
            }

            // Thiết lập button thông tin
            if (buttonThongTin != null)
            {
                buttonThongTin.onClick.AddListener(HienThiThongTinChiTiet);
            }

            // Lấy số tiền hiện tại
            if (CurrencyManager.Instance != null)
            {
                soTienHienTai = CurrencyManager.Instance.SoTienHienTai;
                soTienTruoc = soTienHienTai;
            }
        }

        private void DangKyEvents()
        {
            CurrencyManager.OnCurrencyChanged += OnSoTienThayDoi;
            CurrencyManager.OnTransactionCompleted += OnGiaoDichHoanThanh;
        }

        private void HuyDangKyEvents()
        {
            CurrencyManager.OnCurrencyChanged -= OnSoTienThayDoi;
            CurrencyManager.OnTransactionCompleted -= OnGiaoDichHoanThanh;
        }
        #endregion

        #region Event Handlers
        private void OnSoTienThayDoi(int soTienMoi)
        {
            soTienTruoc = soTienHienTai;
            soTienHienTai = soTienMoi;

            if (coHieuUngThayDoi)
            {
                BatDauHieuUngThayDoi();
            }
            else
            {
                CapNhatHienThi();
            }

            if (hienThiLog)
                Debug.Log($"UI Currency cập nhật: {soTienTruoc} → {soTienMoi}");
        }

        private void OnGiaoDichHoanThanh(int soTienThayDoi, string lyDo)
        {
            if (hienThiLog)
                Debug.Log($"Giao dịch: {soTienThayDoi:+#;-#;0} Lea - {lyDo}");
        }
        #endregion

        #region UI Updates
        /// <summary>
        /// Cập nhật hiển thị số tiền
        /// </summary>
        public void CapNhatHienThi()
        {
            if (textSoTien != null)
            {
                textSoTien.text = soTienHienTai.ToString(dinhDangSoTien);
                textSoTien.color = mauBinhThuong;
            }
        }

        /// <summary>
        /// Bắt đầu hiệu ứng khi số tiền thay đổi
        /// </summary>
        private void BatDauHieuUngThayDoi()
        {
            if (coroutineHieuUng != null)
            {
                StopCoroutine(coroutineHieuUng);
            }

            coroutineHieuUng = StartCoroutine(HieuUngThayDoiSoTien());
        }

        /// <summary>
        /// Coroutine hiệu ứng thay đổi số tiền
        /// </summary>
        private IEnumerator HieuUngThayDoiSoTien()
        {
            if (textSoTien == null) yield break;

            // Xác định màu sắc
            Color mauHieuUng = soTienHienTai > soTienTruoc ? mauTang : mauGiam;
            
            // Hiệu ứng scale và màu sắc
            Vector3 scaleGoc = textSoTien.transform.localScale;
            Color mauGoc = textSoTien.color;

            float thoiGian = 0f;
            while (thoiGian < thoiGianHieuUng)
            {
                thoiGian += Time.deltaTime;
                float t = thoiGian / thoiGianHieuUng;

                // Hiệu ứng scale
                float scaleMultiplier = curveTangGiam.Evaluate(t);
                textSoTien.transform.localScale = scaleGoc * scaleMultiplier;

                // Hiệu ứng màu sắc
                if (t < 0.5f)
                {
                    textSoTien.color = Color.Lerp(mauGoc, mauHieuUng, t * 2f);
                }
                else
                {
                    textSoTien.color = Color.Lerp(mauHieuUng, mauBinhThuong, (t - 0.5f) * 2f);
                }

                // Cập nhật số tiền với hiệu ứng đếm
                if (soTienTruoc != soTienHienTai)
                {
                    int soTienHienThoi = Mathf.RoundToInt(Mathf.Lerp(soTienTruoc, soTienHienTai, t));
                    textSoTien.text = soTienHienThoi.ToString(dinhDangSoTien);
                }

                yield return null;
            }

            // Đảm bảo kết thúc đúng
            textSoTien.transform.localScale = scaleGoc;
            textSoTien.color = mauBinhThuong;
            textSoTien.text = soTienHienTai.ToString(dinhDangSoTien);

            coroutineHieuUng = null;
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Cập nhật thủ công số tiền hiển thị
        /// </summary>
        /// <param name="soTien">Số tiền mới</param>
        public void CapNhatSoTien(int soTien)
        {
            soTienTruoc = soTienHienTai;
            soTienHienTai = soTien;

            if (coHieuUngThayDoi)
            {
                BatDauHieuUngThayDoi();
            }
            else
            {
                CapNhatHienThi();
            }
        }

        /// <summary>
        /// Bật/tắt hiệu ứng
        /// </summary>
        /// <param name="batTat">True để bật hiệu ứng</param>
        public void DatHieuUng(bool batTat)
        {
            coHieuUngThayDoi = batTat;
        }

        /// <summary>
        /// Đặt định dạng hiển thị số tiền
        /// </summary>
        /// <param name="dinhDang">Định dạng mới (ví dụ: "N0", "F2")</param>
        public void DatDinhDangSoTien(string dinhDang)
        {
            dinhDangSoTien = dinhDang;
            CapNhatHienThi();
        }

        /// <summary>
        /// Hiển thị thông tin chi tiết về tiền tệ
        /// </summary>
        public void HienThiThongTinChiTiet()
        {
            if (CurrencyManager.Instance != null)
            {
                string thongTin = CurrencyManager.Instance.LayThongTinChiTiet();
                Debug.Log($"=== THÔNG TIN TIỀN TỆ ===\n{thongTin}");

                // Có thể mở popup UI ở đây
                // PopupManager.Instance?.HienThiPopup("Thông Tin Tiền Tệ", thongTin);
            }
        }

        /// <summary>
        /// Làm mới UI
        /// </summary>
        public void LamMoiUI()
        {
            if (CurrencyManager.Instance != null)
            {
                soTienHienTai = CurrencyManager.Instance.SoTienHienTai;
                soTienTruoc = soTienHienTai;
                CapNhatHienThi();
            }
        }

        /// <summary>
        /// Test hiệu ứng (chỉ trong Editor)
        /// </summary>
        [ContextMenu("Test Hiệu Ứng Tăng")]
        public void TestHieuUngTang()
        {
            #if UNITY_EDITOR
            if (Application.isPlaying)
            {
                CapNhatSoTien(soTienHienTai + 100);
            }
            #endif
        }

        /// <summary>
        /// Test hiệu ứng giảm (chỉ trong Editor)
        /// </summary>
        [ContextMenu("Test Hiệu Ứng Giảm")]
        public void TestHieuUngGiam()
        {
            #if UNITY_EDITOR
            if (Application.isPlaying)
            {
                CapNhatSoTien(Mathf.Max(0, soTienHienTai - 50));
            }
            #endif
        }
        #endregion

        #region Properties
        /// <summary>
        /// Số tiền hiện tại đang hiển thị
        /// </summary>
        public int SoTienHienThi => soTienHienTai;

        /// <summary>
        /// Kiểm tra có đang chạy hiệu ứng không
        /// </summary>
        public bool DangChayHieuUng => coroutineHieuUng != null;
        #endregion
    }
}
