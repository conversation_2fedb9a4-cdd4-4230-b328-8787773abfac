Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker3.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [26344]  Target information:

Player connection [26344]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1957656050 [EditorId] 1957656050 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [26344] Host joined multi-casting on [***********:54997]...
Player connection [26344] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56036
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002317 seconds.
- Loaded All Assemblies, in  1.121 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.091 seconds
Domain Reload Profiling: 2213ms
	BeginReloadAssembly (215ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (760ms)
		LoadAssemblies (213ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (757ms)
			TypeCache.Refresh (752ms)
				TypeCache.ScanAssembly (686ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (2ms)
	FinalizeReload (1092ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (878ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (116ms)
			SetLoadedEditorAssemblies (20ms)
			BeforeProcessingInitializeOnLoad (191ms)
			ProcessInitializeOnLoadAttributes (385ms)
			ProcessInitializeOnLoadMethodAttributes (164ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.750 seconds
Refreshing native plugins compatible for Editor in 1.44 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.656 seconds
Domain Reload Profiling: 3404ms
	BeginReloadAssembly (349ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (101ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (79ms)
	LoadAllAssembliesAndSetupDomain (1192ms)
		LoadAssemblies (620ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (750ms)
			TypeCache.Refresh (497ms)
				TypeCache.ScanAssembly (448ms)
			BuildScriptInfoCaches (174ms)
			ResolveRequiredComponents (61ms)
	FinalizeReload (1657ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1173ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (225ms)
			ProcessInitializeOnLoadAttributes (819ms)
			ProcessInitializeOnLoadMethodAttributes (115ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (6.8 MB). Loaded Objects now: 7294.
Memory consumption went from 166.9 MB to 160.1 MB.
Total: 23.268100 ms (FindLiveObjects: 3.320800 ms CreateObjectMapping: 1.922000 ms MarkObjects: 11.189700 ms  DeleteObjects: 6.832200 ms)

========================================================================
Received Import Request.
  Time since last request: 103328.770213 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_A.prefab
  artifactKey: Guid(c1348a9d1518f554ab6ace3495c54d68) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_A.prefab using Guid(c1348a9d1518f554ab6ace3495c54d68) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4674269ee0ba7848e11acb5a53745dfe') in 0.5278766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_A.prefab
  artifactKey: Guid(66af56f5ac1a0c44393d61bf32c91005) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_A.prefab using Guid(66af56f5ac1a0c44393d61bf32c91005) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '06e9fd15e43f75110f6616df6daec6b2') in 0.0315593 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Settings/Mobile_Renderer.asset
  artifactKey: Guid(65bc7dbf4170f435aa868c779acfb082) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/Mobile_Renderer.asset using Guid(65bc7dbf4170f435aa868c779acfb082) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '485d88428dc2cd0783c01179f4d13f76') in 0.0242918 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_A.prefab
  artifactKey: Guid(c489ab6cec85cf44e8bde747c04d2bd8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_A.prefab using Guid(c489ab6cec85cf44e8bde747c04d2bd8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '39f7582d63feaa1f132964896a938ceb') in 0.0268629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_C.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_C.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e7ae12c75b6fd3b36f1d7b2af5613d06') in 0.1167881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_C.prefab
  artifactKey: Guid(0fee65b29124ffd439f062a49fafa7e5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_C.prefab using Guid(0fee65b29124ffd439f062a49fafa7e5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5f0998cc799b30c00917a810c7c7a871') in 0.1056313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_C.prefab
  artifactKey: Guid(b13cd54f8ded35948ae5427ef2548079) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_C.prefab using Guid(b13cd54f8ded35948ae5427ef2548079) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f3597fba742a437be46bfcd9edcea7e4') in 0.1102521 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000080 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodB_B.fbx
  artifactKey: Guid(a392f36ee51abac4282dfbd915f9304f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodB_B.fbx using Guid(a392f36ee51abac4282dfbd915f9304f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b66fb2286e8546da487e379e3c9230d3') in 0.3776258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Brazier_B.prefab
  artifactKey: Guid(83682936f26a7e34d9096161bf469b7e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Brazier_B.prefab using Guid(83682936f26a7e34d9096161bf469b7e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '908b42afa230817889eb5536df9cdac2') in 0.2943496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_E.fbx
  artifactKey: Guid(a10204e5d18df1445acfb172e426a50c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_E.fbx using Guid(a10204e5d18df1445acfb172e426a50c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '22cbf900517fc25c326e51b09d26f066') in 0.0348594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_J.prefab
  artifactKey: Guid(2f10db60eb0a4194aba0d558deb6ac25) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_J.prefab using Guid(2f10db60eb0a4194aba0d558deb6ac25) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '52ef713d8e5dd4c039d5a09f216fb7ce') in 0.1232956 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tree_D.fbx
  artifactKey: Guid(b0ec5aba9e828c348bf2d0835873ccc7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tree_D.fbx using Guid(b0ec5aba9e828c348bf2d0835873ccc7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3e3212bdedf046973a404480703faf0') in 0.0339879 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_H.prefab
  artifactKey: Guid(8cf8f3fa42cdfef45ba54a66ec64d487) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_H.prefab using Guid(8cf8f3fa42cdfef45ba54a66ec64d487) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'eacc53504b95e2a869889fcad07d8025') in 0.0202993 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_A.prefab
  artifactKey: Guid(a707c8688dc9e3a4d8db588fb90786a0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_A.prefab using Guid(a707c8688dc9e3a4d8db588fb90786a0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cb976aa1ff0a23c13278aed4e0452ca5') in 0.0229741 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Script/Player/Guides
  artifactKey: Guid(93a278b317423254e9992afdc2724b9e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides using Guid(93a278b317423254e9992afdc2724b9e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd3e18301cffc5cf761e2c44609c87a93') in 0.0251016 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_B.prefab
  artifactKey: Guid(1c4ce969a352d2044b8e2ffedbaf2e0a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_B.prefab using Guid(1c4ce969a352d2044b8e2ffedbaf2e0a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9a21555ef3b27ec18171efbce4ed7de1') in 0.0282066 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_D.prefab
  artifactKey: Guid(069787d8567adb34584b9c0c0de129ad) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_D.prefab using Guid(069787d8567adb34584b9c0c0de129ad) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fb29b6fe1d9d2d15d57871fc320bf0a7') in 0.0253046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_C.prefab
  artifactKey: Guid(891a0a190bd865c4a83ea89744539994) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_C.prefab using Guid(891a0a190bd865c4a83ea89744539994) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b7d7e553e064397ac13f0816d0263d52') in 0.0499132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_C.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_C.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8348f9f1c712d1b934890d68da88b0a7') in 0.0227634 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Script/Economy/InventoryManager.cs
  artifactKey: Guid(318e196abcc24f1478e2b71c6dfa7e0b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/InventoryManager.cs using Guid(318e196abcc24f1478e2b71c6dfa7e0b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b89d925073301f9a87bd404d6624cabc') in 0.0260633 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_D.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_D.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9ed931878a6b27648bd97cd5cd8bbc8c') in 0.0220402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_A.fbx
  artifactKey: Guid(6e54992089fadb847b4d4242cbd61720) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_A.fbx using Guid(6e54992089fadb847b4d4242cbd61720) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a5cc346df1188d0155fe6fd9f109a433') in 0.0355222 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_J.prefab
  artifactKey: Guid(410f0d4757ab4034ea796bf5c0ef48b2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_J.prefab using Guid(410f0d4757ab4034ea796bf5c0ef48b2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd72b984a5e5376b1fe47e4c9c710a74d') in 0.0371205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_E.fbx
  artifactKey: Guid(4293a88dc6c7fae4384854cfe0f81080) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_E.fbx using Guid(4293a88dc6c7fae4384854cfe0f81080) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fb7793762c763b8390db60a17c542e3a') in 0.0414215 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_B.prefab
  artifactKey: Guid(f4cb87f72c4ae6846bbe4245c36bfff1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_B.prefab using Guid(f4cb87f72c4ae6846bbe4245c36bfff1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'af8a896ffdfb5ccaab42688375c0493b') in 0.0248294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_C.prefab
  artifactKey: Guid(4f818683eba502c4e922739fc8d32cdd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_C.prefab using Guid(4f818683eba502c4e922739fc8d32cdd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6d8239fe7d20138e532ed20340ecdade') in 0.0231466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_E.prefab
  artifactKey: Guid(49a88d12c2bdb6e46bc00a85c08193bf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_E.prefab using Guid(49a88d12c2bdb6e46bc00a85c08193bf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '994034626403b6a3be69b01f7f077d2a') in 0.0311309 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bowl_Orange.fbx
  artifactKey: Guid(77e90167e550e3140b4b125567fe1b7b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bowl_Orange.fbx using Guid(77e90167e550e3140b4b125567fe1b7b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d5bac335f6f49fdce93282a1824ea68') in 0.033794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_A.prefab
  artifactKey: Guid(d759df098ec7888498cc1b0a71ab9dd7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_A.prefab using Guid(d759df098ec7888498cc1b0a71ab9dd7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '206808f20b4fb14ff99aca7c433202f6') in 0.0273473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_M.fbx
  artifactKey: Guid(ca5fbf99c0512fd4cabfc3708a76495a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_M.fbx using Guid(ca5fbf99c0512fd4cabfc3708a76495a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f2ae23cc101efe5e176dada7e3360a5f') in 0.0469711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Grey.prefab
  artifactKey: Guid(fb0ff6d204402ca4ca6b6512228ba0af) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Grey.prefab using Guid(fb0ff6d204402ca4ca6b6512228ba0af) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '108796c4d8b6a461faa32628c2f9b2ca') in 0.029615 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_B.prefab
  artifactKey: Guid(80342a30dfd5de14599c5f32b069db43) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_B.prefab using Guid(80342a30dfd5de14599c5f32b069db43) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2400677eba1fa3bc99a1ffb4fccfb6a0') in 0.0252007 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_C.prefab
  artifactKey: Guid(d884613eae895a944857b11f41324b52) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_C.prefab using Guid(d884613eae895a944857b11f41324b52) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '450de9ed1a969598e936ed1c998b7a48') in 0.0223253 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_E.prefab
  artifactKey: Guid(f4d5f6c106589014f92cc4c5a04c623f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_E.prefab using Guid(f4d5f6c106589014f92cc4c5a04c623f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c4bb0e0f165c64999718c0b9d2e39c32') in 0.0254934 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_A.prefab
  artifactKey: Guid(70fc929f93fb6d44a80290abc88edc22) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_A.prefab using Guid(70fc929f93fb6d44a80290abc88edc22) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '714dd567e4e470d58623ea9e1fc04363') in 0.0231208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_C.prefab
  artifactKey: Guid(e9c7da5341073d84d8005faa9edac7fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_C.prefab using Guid(e9c7da5341073d84d8005faa9edac7fa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '611f6a056c181f65a3130a9c317ae9ef') in 0.0210528 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_A.prefab
  artifactKey: Guid(4d7b876ea92a6cd48bc7e105d111b82e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_A.prefab using Guid(4d7b876ea92a6cd48bc7e105d111b82e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3d12206a6324a55e595ef32e4e1cd741') in 0.0289122 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_E.prefab
  artifactKey: Guid(799c371d57be540488146a6d5c0ce89f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_E.prefab using Guid(799c371d57be540488146a6d5c0ce89f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '87e35531f1dd0b19c825bf377562087a') in 0.0274412 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_Sign_A.fbx
  artifactKey: Guid(86927119eef89dc4587fc72cb90598c1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_Sign_A.fbx using Guid(86927119eef89dc4587fc72cb90598c1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '28426fe41c5a286f30726a72ce4330e5') in 0.0340742 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BatteringRam.fbx
  artifactKey: Guid(c0490fd826011074f816780b6b3cd738) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BatteringRam.fbx using Guid(c0490fd826011074f816780b6b3cd738) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9f260e38715422dc6dcc6d266476b75') in 0.0332793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_D.prefab
  artifactKey: Guid(8459d01ad38d81e4cacee79763be4e41) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_D.prefab using Guid(8459d01ad38d81e4cacee79763be4e41) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7480facb048eba39b3acb8e7a6a4048f') in 0.0277367 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_E.prefab
  artifactKey: Guid(7adbf7fee6484834db99675feadc4c47) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_E.prefab using Guid(7adbf7fee6484834db99675feadc4c47) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c9f547f440929dd982377882b3eabfb5') in 0.0244067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Anvil_B.fbx
  artifactKey: Guid(3cf95dc0db613584aad7dee27a5e78b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Anvil_B.fbx using Guid(3cf95dc0db613584aad7dee27a5e78b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef5d241fe4d308bb677e16c8415cce45') in 0.0443085 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000108 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_A.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_A.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '17c6bcdc251bd42fd98e51d38b0ff42f') in 0.0318671 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bread_A.fbx
  artifactKey: Guid(ca56a4b339fa1a043b09c65fc5b3d081) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bread_A.fbx using Guid(ca56a4b339fa1a043b09c65fc5b3d081) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5820bee197691c568f2291a0bc242072') in 0.0296737 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Cook_A.fbx
  artifactKey: Guid(dd497f87c4aea1445819f90e671f081d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Cook_A.fbx using Guid(dd497f87c4aea1445819f90e671f081d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9d564e9e241b1d382e9718a9b11fdde1') in 0.0487304 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_B.prefab
  artifactKey: Guid(f30173630e166a745adb7884a8eccafa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_B.prefab using Guid(f30173630e166a745adb7884a8eccafa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e802f73827230fe2d8380e2992e872dc') in 0.0489969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Orange.prefab
  artifactKey: Guid(537023221717f1543a125910a3c207a7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Orange.prefab using Guid(537023221717f1543a125910a3c207a7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2b84bf6dcad4cb73a85ddfccd9dc57ba') in 0.0221708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_A.prefab
  artifactKey: Guid(461d999f8325970499548ad6ee9df31a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_A.prefab using Guid(461d999f8325970499548ad6ee9df31a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a0ebb3e975536a34cdc70a3fe86f1e1') in 0.0345806 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_C.fbx
  artifactKey: Guid(0ce1b35033deccc4699dea207aab294f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_C.fbx using Guid(0ce1b35033deccc4699dea207aab294f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47ebb99f2c164fd795a4d7aa30cdf62a') in 0.0470315 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/SiegeTower_A.prefab
  artifactKey: Guid(eadb7807054abd8479bae0780283dce9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/SiegeTower_A.prefab using Guid(eadb7807054abd8479bae0780283dce9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2dbf387cf3773b629a5021b3da49b9e6') in 0.024301 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_F.prefab
  artifactKey: Guid(c5e0e0f8fb9aafe40aa541c049e00bcb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_F.prefab using Guid(c5e0e0f8fb9aafe40aa541c049e00bcb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2acb9aaf86f0eb6fa44e6fd280683364') in 0.0208941 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_B.prefab
  artifactKey: Guid(567fd0cd3b1e93442a7d4c72c1880a1b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bush_B.prefab using Guid(567fd0cd3b1e93442a7d4c72c1880a1b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5985b3a05230ac07b6e710af6f31869a') in 0.0319885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_I.prefab
  artifactKey: Guid(c31b2cc8e2fcb6f418cf5d361339b5d3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_I.prefab using Guid(c31b2cc8e2fcb6f418cf5d361339b5d3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '03326d2b844844b03153892384337a6c') in 0.0415339 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Steak.prefab
  artifactKey: Guid(4fab75505524f1e438c839e87ed8bd3b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Steak.prefab using Guid(4fab75505524f1e438c839e87ed8bd3b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8ef4e7752108638a229c8560edc522a9') in 0.0270197 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_B.prefab
  artifactKey: Guid(726cae9a9eadce34cb37f945e9c37432) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_B.prefab using Guid(726cae9a9eadce34cb37f945e9c37432) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '520e7e0a3371e3aabc3bf87a0d69f8e5') in 0.0379607 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_L.prefab
  artifactKey: Guid(144f496e01322a444bd85e56886c2332) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_L.prefab using Guid(144f496e01322a444bd85e56886c2332) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a55c6fcd8b5bf3f3b00a78c62277ceaa') in 0.0346473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_C.prefab
  artifactKey: Guid(fde9a2d3a63744449a41c3e092fbeb84) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_C.prefab using Guid(fde9a2d3a63744449a41c3e092fbeb84) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '647d00abddc1c48cafd92705edc98de7') in 0.024883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_C.prefab
  artifactKey: Guid(7678284f3efea1340a83c6c07c11967e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_C.prefab using Guid(7678284f3efea1340a83c6c07c11967e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0b4d68896413ca48425b26c0cc2f16a3') in 0.0334888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_D.fbx
  artifactKey: Guid(1c12234c61c1d4943afd04aa69482edd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_D.fbx using Guid(1c12234c61c1d4943afd04aa69482edd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3da0a0c58b62b6d2a684936ca630dd1a') in 0.0701353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_B.prefab
  artifactKey: Guid(83f7f91ffd16f39409d9fcfeec4b21d2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_B.prefab using Guid(83f7f91ffd16f39409d9fcfeec4b21d2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5a54b138733b418f5b4de9727bcce52d') in 0.028692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000314 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Cucumber.fbx
  artifactKey: Guid(c73c954d9198dcb4ba266457d707384b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Cucumber.fbx using Guid(c73c954d9198dcb4ba266457d707384b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0fe1c15ee0f2aa87cfbe48e1a9764e4b') in 0.0447294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_E.prefab
  artifactKey: Guid(49a88d12c2bdb6e46bc00a85c08193bf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_E.prefab using Guid(49a88d12c2bdb6e46bc00a85c08193bf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '9662f96f1bcdf419ab7d6bc947e891d7') in 0.0211771 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000082 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_D.prefab
  artifactKey: Guid(f1baa36c277a622458a42df58ba56aeb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_D.prefab using Guid(f1baa36c277a622458a42df58ba56aeb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b7a6f08bdbd05eebaa0cbbf27aabcbf7') in 0.0360548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_C.fbx
  artifactKey: Guid(2564e189200bc994b9bb3bef06d492c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_C.fbx using Guid(2564e189200bc994b9bb3bef06d492c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c359a0acfcae8005b43536a88ab53f00') in 0.0447594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_A.prefab
  artifactKey: Guid(e0f920db2ed60524caafc6b6ed1c7e26) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_A.prefab using Guid(e0f920db2ed60524caafc6b6ed1c7e26) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6a4aa87c346817f7340470f7a1e45cc8') in 0.0367571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_J.fbx
  artifactKey: Guid(e8d5b9ced1e82e54db4966d1cdfa97ee) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_J.fbx using Guid(e8d5b9ced1e82e54db4966d1cdfa97ee) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4973262a85bf253351e10fd543408e88') in 0.052407 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_C.prefab
  artifactKey: Guid(4b67a44c8226ada449d77abfa0bd9e97) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_C.prefab using Guid(4b67a44c8226ada449d77abfa0bd9e97) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fb74b16a7fc0bcd976f2db1ec80b8315') in 0.042503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000084 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Beige_B.prefab
  artifactKey: Guid(5dcfafc313fe9ec4b8c9ba3c043a2d53) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Beige_B.prefab using Guid(5dcfafc313fe9ec4b8c9ba3c043a2d53) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aae510afb71528e0673c2198e59b17c2') in 0.0423754 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval PackBundle/URP
  artifactKey: Guid(39befcaf9582c624c9395b6fec9669d3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval PackBundle/URP using Guid(39befcaf9582c624c9395b6fec9669d3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4c4bedff239c979bb57ecf6bd46d0448') in 0.0670366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_A.prefab
  artifactKey: Guid(7ee9ac36fea508d48be9025471ae432c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_A.prefab using Guid(7ee9ac36fea508d48be9025471ae432c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cf432ae2ba6c477ce289bda122f5d955') in 0.0271885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_C.prefab
  artifactKey: Guid(d582de53372d40a4ca3ec77bafe8fdb4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_C.prefab using Guid(d582de53372d40a4ca3ec77bafe8fdb4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d32136d6f139117bf662bb2cfc78409') in 0.0361891 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Meat_C.fbx
  artifactKey: Guid(3dc86835a9f28144aa202166578c429c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Meat_C.fbx using Guid(3dc86835a9f28144aa202166578c429c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '11a2881e13526a9346499ad50502caf9') in 0.0434873 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000133 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Orange.prefab
  artifactKey: Guid(f35893b1706e32448b4301a2d97a0aae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Orange.prefab using Guid(f35893b1706e32448b4301a2d97a0aae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '311d45dc520eb6ad104ccf4b42e7cc08') in 0.0296524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_L.fbx
  artifactKey: Guid(6e6014e079fca4d4781be7db14030ef6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_L.fbx using Guid(6e6014e079fca4d4781be7db14030ef6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ed3749d919dfae9506904092ef4803c') in 0.0388351 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_Sign_C.fbx
  artifactKey: Guid(6cbc41dccbbf71143894665f67055ca1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_Sign_C.fbx using Guid(6cbc41dccbbf71143894665f67055ca1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '02e4f67800d34026bcb2e1fbbfbf3431') in 0.0466243 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_F.prefab
  artifactKey: Guid(9bfe739cac043884b91a864dce4a3e92) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_F.prefab using Guid(9bfe739cac043884b91a864dce4a3e92) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '916b17bf91db4fa749d6c3360d7a6ebc') in 0.0283075 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Texture/Universal_Pack.png
  artifactKey: Guid(e5051fb83956a1e47b35eab53e8aaf58) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Texture/Universal_Pack.png using Guid(e5051fb83956a1e47b35eab53e8aaf58) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ac8d01c19f4664120ffaf82f4191745a') in 0.0964523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_C.fbx
  artifactKey: Guid(16789524df26e16439c202911385866d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_C.fbx using Guid(16789524df26e16439c202911385866d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a19a903b0dcaccfe69dc6932b515404c') in 0.039271 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_H.prefab
  artifactKey: Guid(8cf8f3fa42cdfef45ba54a66ec64d487) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_H.prefab using Guid(8cf8f3fa42cdfef45ba54a66ec64d487) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ad7b8ebbebdb10b98d03379456abdda') in 0.0308228 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_Part_B.prefab
  artifactKey: Guid(4cbab2649cea49249bfe62bdd4964831) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_Part_B.prefab using Guid(4cbab2649cea49249bfe62bdd4964831) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c47b0bf40bafdaef71bc644b125e0de5') in 0.0297138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_I.prefab
  artifactKey: Guid(726367ff540962245a7506c5337b6392) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_I.prefab using Guid(726367ff540962245a7506c5337b6392) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '659b30d45688ce8945de749ddca2cdb6') in 0.0380921 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_A.prefab
  artifactKey: Guid(b6a724cda4ae81143ab9460744b26bdc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_A.prefab using Guid(b6a724cda4ae81143ab9460744b26bdc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5638db39b31665713ba69dc4330aeff4') in 0.0234097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Table_F.fbx
  artifactKey: Guid(4ac352763c9a8474ba27cfd2226ee8a9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Table_F.fbx using Guid(4ac352763c9a8474ba27cfd2226ee8a9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe2c8a6e1c9c1153c939a9e9f7d64e84') in 0.0459399 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Steel_A.fbx
  artifactKey: Guid(7095fa7f47116b9459e7207e8e179706) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Steel_A.fbx using Guid(7095fa7f47116b9459e7207e8e179706) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b3363eba30d063c5a4ea7adbe047a44') in 0.0462056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_F.prefab
  artifactKey: Guid(858affb861c8a1843ac301426173e598) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_F.prefab using Guid(858affb861c8a1843ac301426173e598) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e62eb281ec5e95bcb88b7e4ae6a0e70f') in 0.0323088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Manger_C.fbx
  artifactKey: Guid(3034e9c3dbea7eb4b906b8c16b376d08) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Manger_C.fbx using Guid(3034e9c3dbea7eb4b906b8c16b376d08) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e787d4e63b3fb6c96ec7a499cd2f2a5c') in 0.0430473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Watermelon_B.prefab
  artifactKey: Guid(a0b3b208bb575c944a60ef9ef2f1532e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Watermelon_B.prefab using Guid(a0b3b208bb575c944a60ef9ef2f1532e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f890cf56d5b674c1ae3e1d9d699415e2') in 0.0275908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_A.fbx
  artifactKey: Guid(06161932855c60a4989055a7a2d6226f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_A.fbx using Guid(06161932855c60a4989055a7a2d6226f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70d6d3dd1ba52c6e3f45f87a89689e36') in 0.0397784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pumpkin_A.prefab
  artifactKey: Guid(fc41251d8b3f4034985141e1b8988bb6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pumpkin_A.prefab using Guid(fc41251d8b3f4034985141e1b8988bb6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd8ada0bc6e693c9bc815cdb95a240629') in 0.026002 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_B.prefab
  artifactKey: Guid(d9fee27a73a8b8b41891b71f98fe50b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_B.prefab using Guid(d9fee27a73a8b8b41891b71f98fe50b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd2417c9cccbeb1da5cca7a6ee29e88f5') in 0.0287871 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000152 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bowl_White.fbx
  artifactKey: Guid(a1a26882e3354cd4791bdda9fcd82fc2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bowl_White.fbx using Guid(a1a26882e3354cd4791bdda9fcd82fc2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '94436a816e3c352cbaf8e90c4db9a28a') in 0.0485123 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_G.prefab
  artifactKey: Guid(63aa36a75e83cbd4eb188bf4d58fb877) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_G.prefab using Guid(63aa36a75e83cbd4eb188bf4d58fb877) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '454b773644b585aca7f0c1ab15382766') in 0.0283034 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_B.prefab
  artifactKey: Guid(e2b36106184ed97499212c9a1abb669a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_B.prefab using Guid(e2b36106184ed97499212c9a1abb669a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83c6f300c0aede1f1fef8359bb28c92e') in 0.0308119 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_G.prefab
  artifactKey: Guid(80ff76b4d95510f40b3e8c61a69eb0b8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_G.prefab using Guid(80ff76b4d95510f40b3e8c61a69eb0b8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ab99a2b19e9fb59c0400cde871b10ea9') in 0.0290339 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part.prefab
  artifactKey: Guid(42ca3118d1852ba46ae739f0fe44923b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part.prefab using Guid(42ca3118d1852ba46ae739f0fe44923b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4eeed865be43763d7edb9fd9fcef63bc') in 0.0276789 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/FeatherQuillPenInk_A.fbx
  artifactKey: Guid(8057fa471ce2bc64ca5f8684d703840e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/FeatherQuillPenInk_A.fbx using Guid(8057fa471ce2bc64ca5f8684d703840e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf58b74fbc9f04128f297b2913522640') in 0.0323046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_T.fbx
  artifactKey: Guid(04a0287bb5608894ebdfe91e7a8ceda8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_T.fbx using Guid(04a0287bb5608894ebdfe91e7a8ceda8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fabc45119de0576eda2aa2b57de02272') in 0.0340826 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Sky.mat
  artifactKey: Guid(b61a1af5aa9e919428f4cbf320253b1e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Sky.mat using Guid(b61a1af5aa9e919428f4cbf320253b1e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e8e1add001292bffe80280b7daeda32f') in 0.0521793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_D.prefab
  artifactKey: Guid(6c67f74f452ded741a93e84cf491e67f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_D.prefab using Guid(6c67f74f452ded741a93e84cf491e67f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '95642ae40df27fdb29c0af5f3fb1e9d2') in 0.0473707 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_A.prefab
  artifactKey: Guid(8aab0018324fa7a4ca02c2cf0bbb9cd0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_A.prefab using Guid(8aab0018324fa7a4ca02c2cf0bbb9cd0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '912b1ee94790049b38fe09f2fdc144e1') in 0.0451157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tent_E.fbx
  artifactKey: Guid(8d042f8f6f4bccf4ea8f3b61f859e520) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tent_E.fbx using Guid(8d042f8f6f4bccf4ea8f3b61f859e520) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc9a41be52e04b48c8250226ef0359bc') in 0.0442408 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_P.prefab
  artifactKey: Guid(fc7b8283225d51b45871e86ebfea7bba) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_P.prefab using Guid(fc7b8283225d51b45871e86ebfea7bba) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '28e56304f6b5ea03c66a389295803e63') in 0.0296793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000095 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Flower_C.fbx
  artifactKey: Guid(22baf4a993378454ebf3878968bd9eb4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Flower_C.fbx using Guid(22baf4a993378454ebf3878968bd9eb4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3dff4a94dfca1315c3e392ae47743095') in 0.038341 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_I.prefab
  artifactKey: Guid(e666f28a72b674b44ab78b2d4df9e1bd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_I.prefab using Guid(e666f28a72b674b44ab78b2d4df9e1bd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1537d782cf1fb28327197f6e96d4b9c5') in 0.0277317 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_C.prefab
  artifactKey: Guid(d2c04a7157d558d48822653e4f60f1e3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_C.prefab using Guid(d2c04a7157d558d48822653e4f60f1e3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '88595a7c7034db9496fda9d11254644d') in 0.028687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_C.prefab
  artifactKey: Guid(fde9a2d3a63744449a41c3e092fbeb84) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_C.prefab using Guid(fde9a2d3a63744449a41c3e092fbeb84) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ab14a9ec677f3311cf46999fb905e8d') in 0.0378054 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Torch_Steel_B.fbx
  artifactKey: Guid(d27391a05d99b594cb8cd1bc9b9ccb52) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Torch_Steel_B.fbx using Guid(d27391a05d99b594cb8cd1bc9b9ccb52) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '013d17d4b06d1843b19959961b9fa208') in 0.0449518 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tower_Wood.prefab
  artifactKey: Guid(fd11d6682cb39cd4d866bd44db1eb46b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tower_Wood.prefab using Guid(fd11d6682cb39cd4d866bd44db1eb46b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f5fc347bf274276dbf923c022e06a88') in 0.0280462 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_K.prefab
  artifactKey: Guid(3a05cd324fd089f438cabeea4b17e363) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_K.prefab using Guid(3a05cd324fd089f438cabeea4b17e363) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '69f1e218ed6c3320bf68825f659c9026') in 0.0282992 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_A.prefab
  artifactKey: Guid(7ee9ac36fea508d48be9025471ae432c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_A.prefab using Guid(7ee9ac36fea508d48be9025471ae432c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd69f46128e8d8713edc81015761c3f96') in 0.0277911 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_B.prefab
  artifactKey: Guid(4acb8425bd5652640a653a1cbbeb5efd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_B.prefab using Guid(4acb8425bd5652640a653a1cbbeb5efd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f2b0147748562349d00ec7faf6e6c8c7') in 0.0290451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000544 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Steel_D.fbx
  artifactKey: Guid(0dccb476523777c4db7f9b789e097bff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Steel_D.fbx using Guid(0dccb476523777c4db7f9b789e097bff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7ff862ef61afbc6a122670051f3e7258') in 0.0342373 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_D.prefab
  artifactKey: Guid(d272ff7dc98607d459f91813fd8293e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_D.prefab using Guid(d272ff7dc98607d459f91813fd8293e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '92149b2811b931ede7ab5f11acdb50a9') in 0.034272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_I.prefab
  artifactKey: Guid(b6b6e8c9b9f27864cb8352814cde1a4d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_I.prefab using Guid(b6b6e8c9b9f27864cb8352814cde1a4d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd9a56ee49bafbdc0a2b63a0326fa9305') in 0.0312995 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Watermelon_A.fbx
  artifactKey: Guid(82b558f3e28482d48b2c07a34e233618) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Watermelon_A.fbx using Guid(82b558f3e28482d48b2c07a34e233618) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ecd352858887b3ae21fac9292bb27270') in 0.042966 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_F.fbx
  artifactKey: Guid(de64353916530384fb8063bca1da6984) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_F.fbx using Guid(de64353916530384fb8063bca1da6984) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a3291a3b9b0d3ad4bbe2687093ff55db') in 0.0419048 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_B.prefab
  artifactKey: Guid(71473546ba57bd74aab8bc5c4b604a38) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_B.prefab using Guid(71473546ba57bd74aab8bc5c4b604a38) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dde3c881a714d95305fd06f2c40a30ea') in 0.0292099 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Red.prefab
  artifactKey: Guid(b638312a21cccc0489a61c5cd10e8c97) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Red.prefab using Guid(b638312a21cccc0489a61c5cd10e8c97) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '39263c0ceea324ac8d0275fd075835e2') in 0.033051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Part_Ladder.fbx
  artifactKey: Guid(e8a69b1db217678419a4af4cd53cc4cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Part_Ladder.fbx using Guid(e8a69b1db217678419a4af4cd53cc4cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '36f1f6735301412d0d231fae553870bc') in 0.0359536 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_A.fbx
  artifactKey: Guid(5bf65adad6bd8284d8f5134070014eb7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/BlacksmithTool_A.fbx using Guid(5bf65adad6bd8284d8f5134070014eb7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '926ba21c3a449c21a12aaf35ed046633') in 0.0408325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Egg_A.fbx
  artifactKey: Guid(56b7477d6884be54d93ed9898c108fff) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Egg_A.fbx using Guid(56b7477d6884be54d93ed9898c108fff) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4855750c48d9fc5bd32b6ea492287964') in 0.0492402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_F.prefab
  artifactKey: Guid(048d859f818449e42b2e362680d414fd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_F.prefab using Guid(048d859f818449e42b2e362680d414fd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5b2abdb8620e53baa2bc8f413aa20978') in 0.0289583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Chaff_A.prefab
  artifactKey: Guid(87d14f7e4a179bd47aa54b0ece65659c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Chaff_A.prefab using Guid(87d14f7e4a179bd47aa54b0ece65659c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '24fe62d0970898b0a35d53fb04f51349') in 0.0292912 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_K.prefab
  artifactKey: Guid(7b6729477db49a6498c1c035abdbb0ac) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_K.prefab using Guid(7b6729477db49a6498c1c035abdbb0ac) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '88ceff900dc1b881fc4b61cf6ce90023') in 0.0336042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000126 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Radish.prefab
  artifactKey: Guid(b0b380fa8bc46e7498f2143a15a93afc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Radish.prefab using Guid(b0b380fa8bc46e7498f2143a15a93afc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1923c81a3b87bef884f6ed3d6672ea00') in 0.0284924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Torch.fbx
  artifactKey: Guid(fbd82d116a991b24d8617d9f9d9e3afd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Torch.fbx using Guid(fbd82d116a991b24d8617d9f9d9e3afd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e3445b1ee7419eb2f46b06858369ec7c') in 0.0497807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_B.fbx
  artifactKey: Guid(4794f224764842a4bb8effaf945ffceb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_B.fbx using Guid(4794f224764842a4bb8effaf945ffceb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '792f0784a719cb52f302b3e398e7e907') in 0.0494211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_C.prefab
  artifactKey: Guid(6c111f1606f6f354089b445976eb9bcd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_C.prefab using Guid(6c111f1606f6f354089b445976eb9bcd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '69ea43640c84fbce9a05ec268e3f802e') in 0.0321661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Yellow.prefab
  artifactKey: Guid(635cde54173d1d848b6f4cc62898584c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Yellow.prefab using Guid(635cde54173d1d848b6f4cc62898584c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'aafeacfee8476c0f41887f78cebf66e2') in 0.0266073 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_B.fbx
  artifactKey: Guid(8985258be57a4ff4d905d85a010ac99f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_B.fbx using Guid(8985258be57a4ff4d905d85a010ac99f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ce10bcf3cbe983fae36185e8bbe07f53') in 0.0320502 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 83.693730 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Transparent.mat
  artifactKey: Guid(a5278495aa8cc2845922ff6f38ec2403) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Transparent.mat using Guid(a5278495aa8cc2845922ff6f38ec2403) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eea4bbbd807a43e54ea07e2c792b9358') in 0.0312198 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Metalness.mat
  artifactKey: Guid(727c918c19e8dac4a998f99e90f12ef5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Metalness.mat using Guid(727c918c19e8dac4a998f99e90f12ef5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d66cd930737dd91d8af7b1af786dc46') in 0.0255233 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Color.mat
  artifactKey: Guid(57c3db2e0def48446b765b48765610ca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Color.mat using Guid(57c3db2e0def48446b765b48765610ca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '274181898cbd75b62e94b52f8b8a730f') in 0.0238864 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 124.735426 seconds.
  path: Assets/Settings/ItemDatabase.asset
  artifactKey: Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/ItemDatabase.asset using Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '702ae9acbac8b97f11b79f1f10255675') in 0.0179294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.944098 seconds.
  path: Assets/Settings/ItemDatabase.asset
  artifactKey: Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Settings/ItemDatabase.asset using Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2af2738b3f0775661cd96f8b3ad925ef') in 0.0302874 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 7.923616 seconds.
  path: Assets/Scenes/Economy
  artifactKey: Guid(4432b718e7582d847ac6638d6223720d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/Economy using Guid(4432b718e7582d847ac6638d6223720d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f916c37077c84b0ade525282b48f46dc') in 0.0005933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.949715 seconds.
  path: Assets/Scenes/Economy
  artifactKey: Guid(4432b718e7582d847ac6638d6223720d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/Economy using Guid(4432b718e7582d847ac6638d6223720d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7abd2d9c881c539f465aadf00baa7ec7') in 0.015942 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.533340 seconds.
  path: Assets/Economy
  artifactKey: Guid(4432b718e7582d847ac6638d6223720d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Economy using Guid(4432b718e7582d847ac6638d6223720d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b6883d1ce1a99493a0aaa5bd7e84ba7d') in 0.013364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.851008 seconds.
  path: Assets/Economy/ItemDatabase.asset
  artifactKey: Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Economy/ItemDatabase.asset using Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '21ef3ae62d46206575dc41822ebd4be0') in 0.0110901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 21.705810 seconds.
  path: Assets/Economy/MainItemDatabase.asset
  artifactKey: Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Economy/MainItemDatabase.asset using Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b17d4a6e0ef2e8668460e7d2fb7a7324') in 0.0010556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.941165 seconds.
  path: Assets/Economy/MainItemDatabase.asset
  artifactKey: Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Economy/MainItemDatabase.asset using Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '385f62806f479d9433577bcfea17b4e5') in 0.0152579 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6626 unused Assets / (8.4 MB). Loaded Objects now: 7298.
Memory consumption went from 139.8 MB to 131.5 MB.
Total: 21.598900 ms (FindLiveObjects: 0.842500 ms CreateObjectMapping: 0.542900 ms MarkObjects: 13.347600 ms  DeleteObjects: 6.863400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 233.959278 seconds.
  path: Assets/Script/Economy/AutoSetup.cs
  artifactKey: Guid(c26f2e5172ed8184cb04d9cdca3e9ee7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/AutoSetup.cs using Guid(c26f2e5172ed8184cb04d9cdca3e9ee7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34ffb461eabf47dcb7e3bdc405ced9fd') in 0.0210976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 33 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6627 unused Assets / (7.6 MB). Loaded Objects now: 7300.
Memory consumption went from 139.9 MB to 132.3 MB.
Total: 20.743800 ms (FindLiveObjects: 1.227300 ms CreateObjectMapping: 1.361600 ms MarkObjects: 11.543300 ms  DeleteObjects: 6.608900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 308.890289 seconds.
  path: Assets/Script/Economy/TestScript.cs
  artifactKey: Guid(bce2d7c707de3be4bb78a40f41cc1806) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/TestScript.cs using Guid(bce2d7c707de3be4bb78a40f41cc1806) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '82b31a4bf0893b98ed36bff55c5d4d85') in 0.0276333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 37.936556 seconds.
  path: Assets/Economy/MainItemDatabase.asset
  artifactKey: Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Economy/MainItemDatabase.asset using Guid(dd7415c194f5b034b92d8b8c903be91b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cc23f3ad6ca75fa58dd2c306ca9fe04b') in 0.0213227 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/SampleScene.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b685205592dfbc2d308420f6c4f345a') in 0.0225389 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

