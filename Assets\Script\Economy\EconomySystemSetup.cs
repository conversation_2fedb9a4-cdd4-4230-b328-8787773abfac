using UnityEngine;

namespace EconomySystem
{
    /// <summary>
    /// Component setup nhanh cho toàn bộ hệ thống Economy
    /// Tự động tạo và cấu hình tất cả các Manager cần thiết
    /// </summary>
    public class EconomySystemSetup : MonoBehaviour
    {
        [Header("Cài Đặt Cơ Bản")]
        [SerializeField] private ItemDatabase itemDatabase;
        [SerializeField] private bool tuDongKhoiTao = true;
        [SerializeField] private bool hienThiLog = true;

        [Header("Cài Đặt Currency")]
        [SerializeField] private int soTienBanDau = 100;
        [SerializeField] private int soTienToiDa = 999999;
        [SerializeField] private bool tuDongLuuTien = true;

        [Header("Cài Đặt Inventory")]
        [SerializeField] private int soSlotInventory = 50;
        [SerializeField] private bool tuDongSapXepInventory = true;
        [SerializeField] private bool tuDongLuuInventory = true;

        [Header("Cài Đặt Shop")]
        [SerializeField] private float phanTramGiamGiaBan = 0.5f;

        [Header("Cài Đặt Data")]
        [SerializeField] private bool tuDongLuuData = true;
        [SerializeField] private float khoangThoiGianLuu = 60f;
        [SerializeField] private bool taoBackup = true;

        [Header("Debug")]
        [SerializeField] private bool cheDoDev = false;

        private bool daKhoiTao = false;

        #region Unity Methods
        private void Awake()
        {
            if (tuDongKhoiTao)
            {
                KhoiTaoHeThong();
            }
        }

        private void Start()
        {
            if (!daKhoiTao && tuDongKhoiTao)
            {
                KhoiTaoHeThong();
            }

            KiemTraCauHinh();
        }

        #if UNITY_EDITOR
        private void OnValidate()
        {
            // Đảm bảo các giá trị hợp lệ
            soTienBanDau = Mathf.Max(0, soTienBanDau);
            soTienToiDa = Mathf.Max(soTienBanDau, soTienToiDa);
            soSlotInventory = Mathf.Max(1, soSlotInventory);
            phanTramGiamGiaBan = Mathf.Clamp01(phanTramGiamGiaBan);
            khoangThoiGianLuu = Mathf.Max(10f, khoangThoiGianLuu);
        }
        #endif
        #endregion

        #region Public Methods
        /// <summary>
        /// Khởi tạo toàn bộ hệ thống Economy
        /// </summary>
        [ContextMenu("Khởi Tạo Hệ Thống")]
        public void KhoiTaoHeThong()
        {
            if (daKhoiTao)
            {
                if (hienThiLog)
                    Debug.LogWarning("Hệ thống đã được khởi tạo rồi!");
                return;
            }

            if (hienThiLog)
                Debug.Log("Bắt đầu khởi tạo hệ thống Economy...");

            // 1. Khởi tạo GameDataManager trước
            KhoiTaoGameDataManager();

            // 2. Khởi tạo CurrencyManager
            KhoiTaoCurrencyManager();

            // 3. Khởi tạo InventoryManager
            KhoiTaoInventoryManager();

            // 4. Khởi tạo ShopManager
            KhoiTaoShopManager();

            // 5. Cấu hình ItemDatabase
            CauHinhItemDatabase();

            daKhoiTao = true;

            if (hienThiLog)
                Debug.Log("✅ Khởi tạo hệ thống Economy hoàn tất!");
        }

        /// <summary>
        /// Reset toàn bộ hệ thống
        /// </summary>
        [ContextMenu("Reset Hệ Thống")]
        public void ResetHeThong()
        {
            if (hienThiLog)
                Debug.Log("Đang reset hệ thống Economy...");

            // Reset Currency
            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.Instance.DatLaiSoTien(soTienBanDau);
            }

            // Reset Inventory
            if (InventoryManager.Instance != null)
            {
                InventoryManager.Instance.XoaToanBoInventory();
            }

            // Reset Data
            if (GameDataManager.Instance != null)
            {
                GameDataManager.Instance.XoaTatCaDuLieu();
            }

            if (hienThiLog)
                Debug.Log("✅ Reset hệ thống hoàn tất!");
        }

        /// <summary>
        /// Kiểm tra cấu hình hệ thống
        /// </summary>
        [ContextMenu("Kiểm Tra Cấu Hình")]
        public void KiemTraCauHinh()
        {
            bool coLoi = false;

            // Kiểm tra ItemDatabase
            if (itemDatabase == null)
            {
                Debug.LogError("❌ ItemDatabase chưa được gán!");
                coLoi = true;
            }
            else if (itemDatabase.LayTongSoVatPhamBan() == 0)
            {
                Debug.LogWarning("⚠️ ItemDatabase không có vật phẩm nào!");
            }

            // Kiểm tra các Manager
            if (CurrencyManager.Instance == null)
            {
                Debug.LogError("❌ CurrencyManager không tồn tại!");
                coLoi = true;
            }

            if (InventoryManager.Instance == null)
            {
                Debug.LogError("❌ InventoryManager không tồn tại!");
                coLoi = true;
            }

            if (ShopManager.Instance == null)
            {
                Debug.LogError("❌ ShopManager không tồn tại!");
                coLoi = true;
            }

            if (GameDataManager.Instance == null)
            {
                Debug.LogError("❌ GameDataManager không tồn tại!");
                coLoi = true;
            }

            if (!coLoi)
            {
                if (hienThiLog)
                    Debug.Log("✅ Cấu hình hệ thống OK!");
            }
        }

        /// <summary>
        /// Tạo vật phẩm mẫu để test
        /// </summary>
        [ContextMenu("Tạo Vật Phẩm Mẫu")]
        public void TaoVatPhamMau()
        {
            if (itemDatabase == null)
            {
                Debug.LogError("ItemDatabase chưa được gán!");
                return;
            }

            // Tạo một số vật phẩm mẫu
            var vatPhamMau = new Item[]
            {
                new Item("wood", "Gỗ", "Vật liệu cơ bản để xây dựng", 5, 2),
                new Item("stone", "Đá", "Vật liệu bền chắc", 8, 3),
                new Item("iron", "Sắt", "Kim loại quý giá", 15, 7),
                new Item("gold", "Vàng", "Kim loại quý hiếm", 50, 25),
                new Item("sword", "Kiếm", "Vũ khí cận chiến", 100, 50),
                new Item("shield", "Khiên", "Giáp phòng thủ", 80, 40),
                new Item("potion_health", "Thuốc Hồi Máu", "Hồi phục sức khỏe", 20, 10),
                new Item("potion_mana", "Thuốc Hồi Mana", "Hồi phục năng lượng", 25, 12),
                new Item("bread", "Bánh Mì", "Thức ăn cơ bản", 3, 1),
                new Item("gem", "Ngọc Quý", "Vật phẩm quý giá", 200, 100)
            };

            foreach (var item in vatPhamMau)
            {
                if (!itemDatabase.KiemTraVatPhamTonTai(item.ID))
                {
                    itemDatabase.ThemVatPham(item);
                }
            }

            if (hienThiLog)
                Debug.Log($"✅ Đã tạo {vatPhamMau.Length} vật phẩm mẫu!");
        }

        /// <summary>
        /// Test mua vật phẩm
        /// </summary>
        [ContextMenu("Test Mua Vật Phẩm")]
        public void TestMuaVatPham()
        {
            if (!daKhoiTao)
            {
                Debug.LogError("Hệ thống chưa được khởi tạo!");
                return;
            }

            if (itemDatabase != null && itemDatabase.LayTongSoVatPhamBan() > 0)
            {
                var danhSach = itemDatabase.LayDanhSachVatPhamBan();
                var item = danhSach[0];
                
                bool thanhCong = ShopManager.Instance.MuaVatPham(item, 1);
                
                if (hienThiLog)
                {
                    if (thanhCong)
                        Debug.Log($"✅ Test mua {item.TenVatPham} thành công!");
                    else
                        Debug.Log($"❌ Test mua {item.TenVatPham} thất bại!");
                }
            }
        }

        /// <summary>
        /// Hiển thị thông tin hệ thống
        /// </summary>
        [ContextMenu("Hiển Thị Thông Tin")]
        public void HienThiThongTin()
        {
            if (!daKhoiTao)
            {
                Debug.Log("Hệ thống chưa được khởi tạo!");
                return;
            }

            string thongTin = "=== THÔNG TIN HỆ THỐNG ECONOMY ===\n";
            
            // Currency
            if (CurrencyManager.Instance != null)
            {
                thongTin += CurrencyManager.Instance.LayThongTinChiTiet() + "\n";
            }

            // Inventory
            if (InventoryManager.Instance != null)
            {
                thongTin += $"Inventory: {InventoryManager.Instance.SoSlotDaSuDung}/{InventoryManager.Instance.SoSlotTrong + InventoryManager.Instance.SoSlotDaSuDung} slots\n";
                thongTin += $"Tổng vật phẩm: {InventoryManager.Instance.TongSoVatPham}\n";
            }

            // Shop
            if (itemDatabase != null)
            {
                thongTin += $"Shop: {itemDatabase.LayTongSoVatPhamBan()} vật phẩm có sẵn\n";
            }

            // Data
            if (GameDataManager.Instance != null)
            {
                thongTin += GameDataManager.Instance.LayTongThongKe();
            }

            Debug.Log(thongTin);
        }
        #endregion

        #region Private Methods
        private void KhoiTaoGameDataManager()
        {
            var gameDataManager = GameDataManager.Instance;
            
            // Cấu hình GameDataManager (các thuộc tính sẽ được set qua reflection hoặc public methods)
            if (hienThiLog)
                Debug.Log("✓ GameDataManager đã sẵn sàng");
        }

        private void KhoiTaoCurrencyManager()
        {
            var currencyManager = CurrencyManager.Instance;
            
            // Nếu là lần đầu chạy, set số tiền ban đầu
            if (PlayerPrefs.GetInt("FirstRun", 1) == 1)
            {
                currencyManager.DatLaiSoTien(soTienBanDau);
                PlayerPrefs.SetInt("FirstRun", 0);
                PlayerPrefs.Save();
            }

            if (hienThiLog)
                Debug.Log("✓ CurrencyManager đã sẵn sàng");
        }

        private void KhoiTaoInventoryManager()
        {
            var inventoryManager = InventoryManager.Instance;
            
            if (hienThiLog)
                Debug.Log("✓ InventoryManager đã sẵn sàng");
        }

        private void KhoiTaoShopManager()
        {
            var shopManager = ShopManager.Instance;
            
            // Gán ItemDatabase
            if (itemDatabase != null)
            {
                shopManager.ItemDatabase = itemDatabase;
            }

            // Cấu hình phần trăm giảm giá
            shopManager.PhanTramGiamGiaBan = phanTramGiamGiaBan;

            if (hienThiLog)
                Debug.Log("✓ ShopManager đã sẵn sàng");
        }

        private void CauHinhItemDatabase()
        {
            if (itemDatabase == null)
            {
                Debug.LogWarning("ItemDatabase chưa được gán!");
                return;
            }

            // Kiểm tra và tạo vật phẩm mẫu nếu database trống
            if (itemDatabase.LayTongSoVatPhamBan() == 0 && cheDoDev)
            {
                TaoVatPhamMau();
            }

            if (hienThiLog)
                Debug.Log("✓ ItemDatabase đã được cấu hình");
        }
        #endregion

        #region Properties
        public bool DaKhoiTao => daKhoiTao;
        public ItemDatabase ItemDatabase => itemDatabase;
        #endregion
    }
}
