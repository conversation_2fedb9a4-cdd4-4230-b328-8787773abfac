using UnityEngine;

/// <summary>
/// Validator để kiểm tra camera system hoạt động đúng
/// Sử dụng để debug và validate setup
/// </summary>
public class CameraSystemValidator : MonoBehaviour
{
    [Header("Validation Settings")]
    [SerializeField] private bool validateOnStart = true;
    [SerializeField] private bool showDetailedReport = true;
    [SerializeField] private bool autoFixIssues = true;
    
    [Header("Test Settings")]
    [SerializeField] private KeyCode validateKey = KeyCode.F2;
    [SerializeField] private KeyCode reportKey = KeyCode.F3;
    [SerializeField] private KeyCode autoFixKey = KeyCode.F4;
    
    private PlayerController playerController;
    private PlayerCamera playerCamera;
    
    private void Start()
    {
        // Tìm components
        FindComponents();
        
        if (validateOnStart)
        {
            ValidateSystem();
        }
    }
    
    private void Update()
    {
        // Phím tắt để test
        if (Input.GetKeyDown(validateKey))
        {
            ValidateSystem();
        }
        
        if (Input.GetKeyDown(reportKey))
        {
            ShowDetailedReport();
        }
        
        if (Input.GetKeyDown(autoFixKey))
        {
            AutoFixIssues();
        }
    }
    
    private void FindComponents()
    {
        playerController = FindFirstObjectByType<PlayerController>();
        if (playerController != null)
        {
            playerCamera = playerController.GetComponent<PlayerCamera>();
        }
    }
    
    public void ValidateSystem()
    {
        Debug.Log("=== CAMERA SYSTEM VALIDATION ===");
        
        bool allValid = true;
        
        // 1. Kiểm tra PlayerController
        if (playerController == null)
        {
            Debug.LogError("❌ PlayerController not found in scene!");
            allValid = false;
        }
        else
        {
            Debug.Log("✅ PlayerController found");
        }
        
        // 2. Kiểm tra PlayerCamera component
        if (playerCamera == null)
        {
            Debug.LogError("❌ PlayerCamera component not found!");
            allValid = false;
        }
        else
        {
            Debug.Log("✅ PlayerCamera component found");
        }
        
        // 3. Kiểm tra Camera reference
        if (playerController != null)
        {
            Camera cam = playerController.PlayerCamera;
            if (cam == null)
            {
                Debug.LogError("❌ PlayerCamera reference is null!");
                allValid = false;
            }
            else
            {
                Debug.Log("✅ Camera reference is valid");
            }
        }
        
        // 4. Kiểm tra CameraRoot
        if (playerController != null)
        {
            Transform cameraRoot = playerController.CameraRoot;
            if (cameraRoot == null)
            {
                Debug.LogError("❌ CameraRoot reference is null!");
                allValid = false;
            }
            else
            {
                Debug.Log("✅ CameraRoot reference is valid");
            }
        }
        
        // 5. Kiểm tra Input System
        ValidateInputSystem();
        
        // 6. Kiểm tra Hierarchy
        ValidateHierarchy();
        
        // Kết quả tổng
        if (allValid)
        {
            Debug.Log("🎉 CAMERA SYSTEM VALIDATION PASSED!");
        }
        else
        {
            Debug.LogWarning("⚠️ Camera system has issues. Use F4 to auto-fix or check the detailed report (F3).");
        }
        
        if (showDetailedReport)
        {
            ShowDetailedReport();
        }
    }
    
    private void ValidateInputSystem()
    {
        // Kiểm tra Input Actions
        try
        {
            var inputActions = new InputSystem_Actions();
            if (inputActions.Player.ToggleCamera != null)
            {
                Debug.Log("✅ ToggleCamera input action found");
            }
            else
            {
                Debug.LogError("❌ ToggleCamera input action not found!");
            }
            inputActions.Dispose();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Input System error: {e.Message}");
        }
    }
    
    private void ValidateHierarchy()
    {
        if (playerController == null) return;
        
        Camera cam = playerController.PlayerCamera;
        Transform cameraRoot = playerController.CameraRoot;
        
        if (cam != null && cameraRoot != null)
        {
            if (cam.transform.parent == cameraRoot)
            {
                Debug.Log("✅ Camera hierarchy is correct");
            }
            else
            {
                Debug.LogWarning("⚠️ Camera should be child of CameraRoot");
            }
        }
    }
    
    public void ShowDetailedReport()
    {
        Debug.Log("=== DETAILED CAMERA SYSTEM REPORT ===");
        
        if (playerController != null)
        {
            Debug.Log(CameraAutoSetup.GetSetupReport(playerController));
        }
        
        // Camera state
        if (playerCamera != null)
        {
            Debug.Log($"Camera Mode: {(playerCamera.IsFirstPerson ? "First Person" : "Third Person")}");
            Debug.Log($"Current Sensitivity: {playerCamera.CurrentSensitivity}");
            Debug.Log($"Current Pitch: {playerCamera.GetCurrentPitch():F1}°");
            Debug.Log($"Current Yaw: {playerCamera.GetCurrentYaw():F1}°");
        }
        
        // Performance info
        Debug.Log($"FPS: {1f / Time.deltaTime:F1}");
        Debug.Log($"Frame Time: {Time.deltaTime * 1000f:F1}ms");
    }
    
    public void AutoFixIssues()
    {
        Debug.Log("=== AUTO FIXING CAMERA ISSUES ===");
        
        if (playerController == null)
        {
            Debug.LogError("Cannot auto-fix: PlayerController not found!");
            return;
        }
        
        bool wasFixed = CameraAutoSetup.AutoSetupCamera(playerController);

        if (wasFixed)
        {
            Debug.Log("✅ Auto-fix completed! Re-validating...");

            // Re-find components after fix
            FindComponents();

            // Validate again
            ValidateSystem();
        }
        else
        {
            Debug.Log("ℹ️ No issues found to auto-fix");
        }
    }
    
    // Test methods
    public void TestCameraToggle()
    {
        if (playerCamera != null)
        {
            playerCamera.ToggleCameraMode();
            Debug.Log($"Camera toggled to: {(playerCamera.IsFirstPerson ? "First Person" : "Third Person")}");
        }
    }
    
    public void TestCameraShake()
    {
        if (playerCamera != null)
        {
            playerCamera.AddCameraShake(0.3f);
            Debug.Log("Camera shake added");
        }
    }
    
    public void TestSensitivityChange()
    {
        if (playerCamera != null)
        {
            float newSens = playerCamera.CurrentSensitivity == 100f ? 150f : 100f;
            playerCamera.SetSensitivity(newSens);
            Debug.Log($"Sensitivity changed to: {newSens}");
        }
    }
    
    // GUI for testing
    private void OnGUI()
    {
        if (!Application.isPlaying) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Camera System Validator", GUI.skin.box);
        
        if (GUILayout.Button("Validate System (F2)"))
        {
            ValidateSystem();
        }
        
        if (GUILayout.Button("Show Report (F3)"))
        {
            ShowDetailedReport();
        }
        
        if (GUILayout.Button("Auto Fix (F4)"))
        {
            AutoFixIssues();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("Test Functions:");
        
        if (GUILayout.Button("Toggle Camera"))
        {
            TestCameraToggle();
        }
        
        if (GUILayout.Button("Test Shake"))
        {
            TestCameraShake();
        }
        
        if (GUILayout.Button("Change Sensitivity"))
        {
            TestSensitivityChange();
        }
        
        GUILayout.EndArea();
    }
}
