Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker4
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker4.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [12788]  Target information:

Player connection [12788]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 405409922 [EditorId] 405409922 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [12788] Host joined multi-casting on [***********:54997]...
Player connection [12788] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56088
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003151 seconds.
- Loaded All Assemblies, in  1.307 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.924 seconds
Domain Reload Profiling: 2231ms
	BeginReloadAssembly (587ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (4ms)
	RebuildCommonClasses (96ms)
	RebuildNativeTypeToScriptingClass (30ms)
	initialDomainReloadingComplete (110ms)
	LoadAllAssembliesAndSetupDomain (483ms)
		LoadAssemblies (582ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (477ms)
			TypeCache.Refresh (473ms)
				TypeCache.ScanAssembly (424ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (2ms)
	FinalizeReload (925ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (823ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (39ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (496ms)
			ProcessInitializeOnLoadMethodAttributes (116ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.312 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.718 seconds
Domain Reload Profiling: 4030ms
	BeginReloadAssembly (360ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (84ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (1779ms)
		LoadAssemblies (838ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1136ms)
			TypeCache.Refresh (940ms)
				TypeCache.ScanAssembly (892ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (32ms)
	FinalizeReload (1720ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1290ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (282ms)
			ProcessInitializeOnLoadAttributes (882ms)
			ProcessInitializeOnLoadMethodAttributes (112ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 29.75 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (9.1 MB). Loaded Objects now: 7294.
Memory consumption went from 163.4 MB to 154.3 MB.
Total: 129.130700 ms (FindLiveObjects: 6.601200 ms CreateObjectMapping: 2.698500 ms MarkObjects: 108.537800 ms  DeleteObjects: 11.291400 ms)

========================================================================
Received Import Request.
  Time since last request: 103330.131292 seconds.
  path: Assets/Script/Player/Guides/LOI_DA_SUA.md
  artifactKey: Guid(eb55c22705ed124468a2c50db753a041) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/LOI_DA_SUA.md using Guid(eb55c22705ed124468a2c50db753a041) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '923de0cad996287aaf517b207432fa31') in 1.6962793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_C.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Steel_C.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6da714d2262f1a033160a564ea51a408') in 0.2044861 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000146 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_A.fbx
  artifactKey: Guid(7ee0d243aaab49f45929ba658e9feb43) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/WeaponRack_A.fbx using Guid(7ee0d243aaab49f45929ba658e9feb43) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2d7eeb9004c790ee6c39cf8fce3453eb') in 0.0984207 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Eggplant.prefab
  artifactKey: Guid(43d2a44f56178634cb4d95f57c802ea4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Eggplant.prefab using Guid(43d2a44f56178634cb4d95f57c802ea4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0af920fdbc91b3652acffe4c0099197c') in 0.062572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_B.fbx
  artifactKey: Guid(e4a81dd873c508f469f4c440756f3fb7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_B.fbx using Guid(e4a81dd873c508f469f4c440756f3fb7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '395d574d0df41a15e3ba3911b88b7db3') in 0.0372238 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000672 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_K.prefab
  artifactKey: Guid(a4e394a922949984db34822befab93f6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_K.prefab using Guid(a4e394a922949984db34822befab93f6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '731bfec3f6036b9d1e1e5ea367691252') in 0.0248369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Anvil_B.prefab
  artifactKey: Guid(1ad2d79931f4ba8428570752d45e50a1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Anvil_B.prefab using Guid(1ad2d79931f4ba8428570752d45e50a1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f0d206e529474629801b0b87463478a6') in 0.0272145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BowlMedicine_A.prefab
  artifactKey: Guid(232fe6c29d83f1a4baa924dc9235b219) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BowlMedicine_A.prefab using Guid(232fe6c29d83f1a4baa924dc9235b219) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ed82a7180547292a000bea0156a98cfb') in 0.0254898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_C.fbx
  artifactKey: Guid(937deee121e8a42478e833a629d65974) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_C.fbx using Guid(937deee121e8a42478e833a629d65974) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b660e2a1c09c9519897c928817d0456c') in 0.0364027 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000077 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_B.prefab
  artifactKey: Guid(22433c843850954439a7ff47a3c83111) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_B.prefab using Guid(22433c843850954439a7ff47a3c83111) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '59640351e12314f94867d1526d253fd7') in 0.0216544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_B.prefab
  artifactKey: Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_B.prefab using Guid(********************************) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a295bac557ff8886df3904d3dcc80fc6') in 0.0257942 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_D.prefab
  artifactKey: Guid(23d4ba00641b2da41a09dad7fe0176d8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_D.prefab using Guid(23d4ba00641b2da41a09dad7fe0176d8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bb23f49bcc6a412dc2dbc5ffc8b91af6') in 0.0267872 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_D.prefab
  artifactKey: Guid(deb8d52904aec8a4586e8f6ee9e0b7cb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_D.prefab using Guid(deb8d52904aec8a4586e8f6ee9e0b7cb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6dbcab1d79fba403b5e291bbd6b1df5d') in 0.020638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_A.prefab
  artifactKey: Guid(b7a727d7207cef541b7d73ab711f79ce) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_A.prefab using Guid(b7a727d7207cef541b7d73ab711f79ce) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0e7926461a57d4082dfc42900106bc7c') in 0.0244725 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_D.prefab
  artifactKey: Guid(c3cb99ce1a3619641b4e065bfba8447a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fire_D.prefab using Guid(c3cb99ce1a3619641b4e065bfba8447a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8c9e4a4f868959b3e60c875386b88ef9') in 0.0314319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Chaff_B.prefab
  artifactKey: Guid(220df28aef0fc404c883bece59dcd74c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Chaff_B.prefab using Guid(220df28aef0fc404c883bece59dcd74c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '460c35501a96413fb18337c92e73ef7e') in 0.0227784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_I.prefab
  artifactKey: Guid(5d8fa9b39c257df42825278077227c7c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_I.prefab using Guid(5d8fa9b39c257df42825278077227c7c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8b03fc1cffeaca6eedf96c25fe57af61') in 0.0296448 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_B.prefab
  artifactKey: Guid(cba4363878eaaa24b82d08257ca5e9c2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_B.prefab using Guid(cba4363878eaaa24b82d08257ca5e9c2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '72e860e6110903b98192eb5aa5012597') in 0.0256651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_F.prefab
  artifactKey: Guid(dea4af319a3e3c948b7cbed57e61235c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_F.prefab using Guid(dea4af319a3e3c948b7cbed57e61235c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b7ad3dfcfb2248e07850ea8c4bc55b72') in 0.0279545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_D.fbx
  artifactKey: Guid(07c3d66a6f6348f43975e729d5f9dedf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_D.fbx using Guid(07c3d66a6f6348f43975e729d5f9dedf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a83863dbc3fecc8263f5016e4a8285e1') in 0.0474788 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_C.prefab
  artifactKey: Guid(5bb3e555d0e555444ba6b1abb43e32dd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_C.prefab using Guid(5bb3e555d0e555444ba6b1abb43e32dd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ac9acc3c31327e1c5cd36fe2ef365ebf') in 0.0247901 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_G.prefab
  artifactKey: Guid(9a1d7b254d500b646b79824d1a6ec1f6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_G.prefab using Guid(9a1d7b254d500b646b79824d1a6ec1f6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f037221508e89baa40ec8d14bfabeb57') in 0.0245593 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_A.prefab
  artifactKey: Guid(c489ab6cec85cf44e8bde747c04d2bd8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_A.prefab using Guid(c489ab6cec85cf44e8bde747c04d2bd8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6e527b4eb3d06b2db9ed473e64b4781c') in 0.0278967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_M.prefab
  artifactKey: Guid(65e2bf70526c2c049b8a8d2bf9a835e8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_M.prefab using Guid(65e2bf70526c2c049b8a8d2bf9a835e8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '22f7d35133b4027318e35b96b3b8ffcf') in 0.0202502 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_F.prefab
  artifactKey: Guid(531e0a6f1a330f04a81dd53cf86ef68f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_F.prefab using Guid(531e0a6f1a330f04a81dd53cf86ef68f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6fc80e569818a683e3c3fd771e026adb') in 0.0269671 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_B.prefab
  artifactKey: Guid(9de4201efc700a34eadf1c8abb090356) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_B.prefab using Guid(9de4201efc700a34eadf1c8abb090356) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '810beba2bad70869364726ed90a6c16c') in 0.03334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_C.prefab
  artifactKey: Guid(651b26b508213d9468d9dfec888ef996) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_C.prefab using Guid(651b26b508213d9468d9dfec888ef996) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b72df3a731cd9d1cb59cb10f53b60d49') in 0.0244952 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_J.prefab
  artifactKey: Guid(09ac18436baea7149aecf2268d80707c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_J.prefab using Guid(09ac18436baea7149aecf2268d80707c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd8ec0a9d2c9730346f4af6648e26aa1d') in 0.0217545 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_B.prefab
  artifactKey: Guid(3158c86f78c137f4893285695fb1e14f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_B.prefab using Guid(3158c86f78c137f4893285695fb1e14f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f509649319e5fe80f1166db59859b134') in 0.0220718 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_E.prefab
  artifactKey: Guid(cc29fd2aa75e4564db8601461e9f97c3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_E.prefab using Guid(cc29fd2aa75e4564db8601461e9f97c3) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c230b87c15b0740de31f070483289a95') in 0.0218464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Candelabrum_A.fbx
  artifactKey: Guid(ba1cddefbe3c95e40ba18658e6a09996) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Candelabrum_A.fbx using Guid(ba1cddefbe3c95e40ba18658e6a09996) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cd72c7117f657f2a72e2b3de4bf60555') in 0.0500683 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Scenes/Demo1.unity
  artifactKey: Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Scenes/Demo1.unity using Guid(9fc0d4010bbf28b4594072e72b8655ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f7c3873915e07ea7494724758381eece') in 0.2402015 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Onion_Yellow.prefab
  artifactKey: Guid(19006d0d6e3988c48ba339539f26a47a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Onion_Yellow.prefab using Guid(19006d0d6e3988c48ba339539f26a47a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8e6152d791233be32a12e8fe5bf6211d') in 0.0225229 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_A.prefab
  artifactKey: Guid(a0c7f93a54eac7b4196d15eae88a5ebf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_A.prefab using Guid(a0c7f93a54eac7b4196d15eae88a5ebf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '529e8a513ae8622bc4d3a64a008e4aff') in 0.0281128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Yellow.prefab
  artifactKey: Guid(bb6c99f5c2301784b8d9afd4e5cd0ec6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Yellow.prefab using Guid(bb6c99f5c2301784b8d9afd4e5cd0ec6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'eaf7683992a3e850629c55af6b85ebac') in 0.0286106 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_B.prefab
  artifactKey: Guid(b852dcb096fb36443923c97dafb2dcd0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_B.prefab using Guid(b852dcb096fb36443923c97dafb2dcd0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '3d15195d73c76578cc898e065d4a6f98') in 0.0246637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_C.prefab
  artifactKey: Guid(4c96ebca674f0a944b5f3a179025ae1f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_C.prefab using Guid(4c96ebca674f0a944b5f3a179025ae1f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4cd184d8e49980eb6393d000d74a7d37') in 0.0219085 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_D.prefab
  artifactKey: Guid(e1691e6940e3f2046b81cbad79e7ad78) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_D.prefab using Guid(e1691e6940e3f2046b81cbad79e7ad78) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a45d3e56e667187b462d4c1d85bb56cf') in 0.0305368 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Steel_C.fbx
  artifactKey: Guid(fa5cadc76150ccf4b8c0d7ab737bd65b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Steel_C.fbx using Guid(fa5cadc76150ccf4b8c0d7ab737bd65b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6b33bf14a832f192d613e620990e7af2') in 0.0534852 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_A.prefab
  artifactKey: Guid(b80a2b67834e46c40b2f64fa79c32df4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_A.prefab using Guid(b80a2b67834e46c40b2f64fa79c32df4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8ce8351cc23fce2129a1fb365cbadcea') in 0.0481681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_B.prefab
  artifactKey: Guid(76fbc51f1958792499113412ca396ff7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_B.prefab using Guid(76fbc51f1958792499113412ca396ff7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6e1ad236143e8468b93b8ae7c7ceaab6') in 0.0245464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_D.prefab
  artifactKey: Guid(a357d4ba0211848439fb35f891f2882f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_D.prefab using Guid(a357d4ba0211848439fb35f891f2882f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e3291d74abb4330470e59bd0f5aa0f34') in 0.0247723 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_A.prefab
  artifactKey: Guid(dd926e78e97c65648bae52eb65497852) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_A.prefab using Guid(dd926e78e97c65648bae52eb65497852) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '555d785674d8f7a198f9e8d74dda984f') in 0.0227798 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_B.prefab
  artifactKey: Guid(cba4363878eaaa24b82d08257ca5e9c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_B.prefab using Guid(cba4363878eaaa24b82d08257ca5e9c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc98409b0f1111a5dafb0ad2b3577d46') in 0.0396933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000121 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Orange.prefab
  artifactKey: Guid(46b9b12749e1a294aaf8a315ed152cd9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Orange.prefab using Guid(46b9b12749e1a294aaf8a315ed152cd9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '77f2f34253a302fcee7f286834474992') in 0.0402318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_C.prefab
  artifactKey: Guid(dae6be95f674e484a9deacc3a8ffc93a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_C.prefab using Guid(dae6be95f674e484a9deacc3a8ffc93a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '67f540c63b7455c52e2c5f00e2912600') in 0.0289807 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_E.prefab
  artifactKey: Guid(03d0ea17e99320741815f3ec783c6352) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Steel_E.prefab using Guid(03d0ea17e99320741815f3ec783c6352) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7af164544b41d9414bd307461556f6ad') in 0.0344581 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Watermelon_A.prefab
  artifactKey: Guid(050c0fca17652d94e943895df847424a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Watermelon_A.prefab using Guid(050c0fca17652d94e943895df847424a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '94eaf21b76b713408055bf02f2f321d4') in 0.026466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bush_A.fbx
  artifactKey: Guid(73bc505759fa8944fa15df4124e43ce1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bush_A.fbx using Guid(73bc505759fa8944fa15df4124e43ce1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4fded12d808b7f13d0bf264fc8681e44') in 0.0441211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_I.prefab
  artifactKey: Guid(b78347208df254f40bccbe9ff001671d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_I.prefab using Guid(b78347208df254f40bccbe9ff001671d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ea6bc3b2ceed88dc7a686ca29417626f') in 0.0291696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Scenes
  artifactKey: Guid(16b5fd49e7e8dec4fbbc5fea6615cce3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Scenes using Guid(16b5fd49e7e8dec4fbbc5fea6615cce3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '37ce5bc4ca12ba4ed2ba41b20be2621e') in 0.0243949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_J.prefab
  artifactKey: Guid(2bb46ceb0da71e140a33c1b47440c3cc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_J.prefab using Guid(2bb46ceb0da71e140a33c1b47440c3cc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8f32e080a2be3e0ae7f0cdca157c8dc9') in 0.0331931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Scenes/Demo2.unity
  artifactKey: Guid(da8ab1f04352341449cd7f9586b070d7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Scenes/Demo2.unity using Guid(da8ab1f04352341449cd7f9586b070d7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter)Opening scene 'Assets/Layer Lab/3D Medieval Pack/Scenes/Demo2.unity additively'
Loaded scene 'Assets/Layer Lab/3D Medieval Pack/Scenes/Demo2.unity'
	Deserialize:            1584.169 ms
	Integration:            2089.147 ms
	Integration of assets:  2.352 ms
	Thread Wait Time:       -2.148 ms
	Total Operation Time:   3673.521 ms
 -> (artifact id: '876152b655c0832363e6a8e171878b40') in 11.4583991 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3337

Editor requested this worker to shutdown with reason: balancing worker count downwards
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0