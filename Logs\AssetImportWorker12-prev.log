Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker12
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker12.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [11636]  Target information:

Player connection [11636]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 609634618 [EditorId] 609634618 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [11636] Host joined multi-casting on [***********:54997]...
Player connection [11636] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56544
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002481 seconds.
- Loaded All Assemblies, in  1.286 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.027 seconds
Domain Reload Profiling: 2314ms
	BeginReloadAssembly (575ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (142ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (106ms)
	LoadAllAssembliesAndSetupDomain (434ms)
		LoadAssemblies (573ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (428ms)
			TypeCache.Refresh (426ms)
				TypeCache.ScanAssembly (395ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1029ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (925ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (48ms)
			SetLoadedEditorAssemblies (16ms)
			BeforeProcessingInitializeOnLoad (365ms)
			ProcessInitializeOnLoadAttributes (384ms)
			ProcessInitializeOnLoadMethodAttributes (111ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.227 seconds
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.918 seconds
Domain Reload Profiling: 4143ms
	BeginReloadAssembly (325ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (99ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (84ms)
	LoadAllAssembliesAndSetupDomain (1687ms)
		LoadAssemblies (1081ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (758ms)
			TypeCache.Refresh (635ms)
				TypeCache.ScanAssembly (606ms)
			BuildScriptInfoCaches (96ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (1920ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1342ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (56ms)
			BeforeProcessingInitializeOnLoad (370ms)
			ProcessInitializeOnLoadAttributes (794ms)
			ProcessInitializeOnLoadMethodAttributes (115ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.15 seconds
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (8.5 MB). Loaded Objects now: 7294.
Memory consumption went from 163.3 MB to 154.8 MB.
Total: 148.860900 ms (FindLiveObjects: 4.137800 ms CreateObjectMapping: 5.775900 ms MarkObjects: 129.325800 ms  DeleteObjects: 9.618500 ms)

========================================================================
Received Import Request.
  Time since last request: 103330.803457 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_D.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_Rock_D.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c39aa47d6d9a833eed4f06839577d2a0') in 1.3593163 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_G.fbx
  artifactKey: Guid(8e2e95211ead0cc4fb31620f7eba5e10) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Signpost_G.fbx using Guid(8e2e95211ead0cc4fb31620f7eba5e10) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5d1902afda7e151b814b16ef97c46f3e') in 0.0384738 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Script/Economy
  artifactKey: Guid(b111bbf0c6c9f5b4db3143a08c6dc3fc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy using Guid(b111bbf0c6c9f5b4db3143a08c6dc3fc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9b871b367928b37bf197d40a3aa2d67d') in 0.021797 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cup_A.prefab
  artifactKey: Guid(70fb689ccac5ecc43ade338ed50ed559) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cup_A.prefab using Guid(70fb689ccac5ecc43ade338ed50ed559) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2664089b5fc031dba3c784ffedc407f4') in 0.0383898 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Brown.prefab
  artifactKey: Guid(251f30be14ace6f41bcea5630f6242ab) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Brown.prefab using Guid(251f30be14ace6f41bcea5630f6242ab) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7bcf769e47571c9ffda71bf5c62bb9a9') in 0.0252317 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_C.fbx
  artifactKey: Guid(f48df756257fc3745b898e84ad2d676a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_C.fbx using Guid(f48df756257fc3745b898e84ad2d676a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d77403a991a68666192f1efa2a0f72f') in 0.0424866 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Script/Player/PlayerCombat.cs
  artifactKey: Guid(92ae3722663a50d48a3c84851cb79f33) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/PlayerCombat.cs using Guid(92ae3722663a50d48a3c84851cb79f33) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5979fed8e79baa5c9fe9bb46270ed1dc') in 0.0273118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Carrier_Wood.prefab
  artifactKey: Guid(561cd7eb1fa3eb54793ed3cc5b9ef5b2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Carrier_Wood.prefab using Guid(561cd7eb1fa3eb54793ed3cc5b9ef5b2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5ce53408fd42ee047ef6633789fd67e6') in 0.0266269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_C.prefab
  artifactKey: Guid(4b67a44c8226ada449d77abfa0bd9e97) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_C.prefab using Guid(4b67a44c8226ada449d77abfa0bd9e97) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7d8bfb1a56b1865eb209edc87713cb4d') in 0.0469848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Brown.prefab
  artifactKey: Guid(29594bb57f313d24f8ed5eef121b57e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Brown.prefab using Guid(29594bb57f313d24f8ed5eef121b57e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c724610f5bee00d65990adf8d916e86') in 0.0482081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_B.prefab
  artifactKey: Guid(284ee59d69677ea41b65f53f941d8d32) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_B.prefab using Guid(284ee59d69677ea41b65f53f941d8d32) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f1c2a66ff5cc7575a38dd155fb78f441') in 0.0247189 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Script/Player/PlayerMovement.cs
  artifactKey: Guid(55757bef96a56ab42aa929cdf494781c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/PlayerMovement.cs using Guid(55757bef96a56ab42aa929cdf494781c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '63ec960ff49dbb042187a273b87643d6') in 0.0223521 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BowlMedicine_A.prefab
  artifactKey: Guid(232fe6c29d83f1a4baa924dc9235b219) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BowlMedicine_A.prefab using Guid(232fe6c29d83f1a4baa924dc9235b219) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08acf289dbda5897abfe9ce0e4032fb1') in 0.0277472 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_E.prefab
  artifactKey: Guid(a571e66f399af384491d66ffa1936b03) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_E.prefab using Guid(a571e66f399af384491d66ffa1936b03) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ceaf91ce882a4e9286570440f0d12d14') in 0.020471 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_Brown.prefab
  artifactKey: Guid(12844ca5226bd6b40adea1a57251c25f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_Brown.prefab using Guid(12844ca5226bd6b40adea1a57251c25f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f5707cdcfb141a9c883d99c7b6cd8767') in 0.0312319 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Yellow.prefab
  artifactKey: Guid(4c63f087cac89f743a1ededaf44bc7ca) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Yellow.prefab using Guid(4c63f087cac89f743a1ededaf44bc7ca) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2eeafe2f7d025234fbe12b42ae181d2a') in 0.0258367 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Flower_E.fbx
  artifactKey: Guid(74d40ba531c1b6c40873c0d6d7a572ac) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Flower_E.fbx using Guid(74d40ba531c1b6c40873c0d6d7a572ac) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e685282cd0448e37fc240db85576069e') in 0.0548984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPen_B.prefab
  artifactKey: Guid(7348e162085e3d44d8a00bd73e022429) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPen_B.prefab using Guid(7348e162085e3d44d8a00bd73e022429) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5d1e7ce51f3985321398b2aae5212141') in 0.0268255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Sword_C.fbx
  artifactKey: Guid(ebfe8daeaafb1844c9b828f827b801ab) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Sword_C.fbx using Guid(ebfe8daeaafb1844c9b828f827b801ab) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dcf38a2b5ec68c666c16cc82e9222ac4') in 0.0486468 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_E.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_Open_E.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '439dd6121d73769b980766b0e32cbe2b') in 0.0317708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_B.prefab
  artifactKey: Guid(4e71abacdc0c08644b22be3cc9ea6780) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_B.prefab using Guid(4e71abacdc0c08644b22be3cc9ea6780) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '290550b2dd659344ba40b290fdb25dc1') in 0.0245292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX
  artifactKey: Guid(1680c8a2de3fbd74d9ff3f8e9e63a4c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX using Guid(1680c8a2de3fbd74d9ff3f8e9e63a4c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4ac43e7b0c54ba188ebb9099df293071') in 0.0248283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Water_A.fbx
  artifactKey: Guid(62e4d83d0fe19434aa6042f5d04950f4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Water_A.fbx using Guid(62e4d83d0fe19434aa6042f5d04950f4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'efb7243a0382f21927879a32afd2c1bd') in 0.0378019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Hourglass_B.fbx
  artifactKey: Guid(d05159e829b1be3449d7bafb77036a3a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Hourglass_B.fbx using Guid(d05159e829b1be3449d7bafb77036a3a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9a02177525ea3bd3746d95f621dbf4c9') in 0.0331573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_A.prefab
  artifactKey: Guid(2e624c421e8bef647afa84da55c6772e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_A.prefab using Guid(2e624c421e8bef647afa84da55c6772e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b11b5761de442355393bcbb349f9e5c9') in 0.0238497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_A.prefab
  artifactKey: Guid(b80a2b67834e46c40b2f64fa79c32df4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_A.prefab using Guid(b80a2b67834e46c40b2f64fa79c32df4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '05009d8b2a3261b16d3c898faff5d329') in 0.0283904 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_A.prefab
  artifactKey: Guid(a4f3d19d5f135f8488c9699bd299df52) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Stable_A.prefab using Guid(a4f3d19d5f135f8488c9699bd299df52) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6bb9b64a20753515f9aed77ad4575ebb') in 0.0232759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_B.prefab
  artifactKey: Guid(cc061631cd2b78b4f995a5cea9765dfb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_B.prefab using Guid(cc061631cd2b78b4f995a5cea9765dfb) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '797e0d14041a916936c5c3cd46e7b31c') in 0.0270776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_H.prefab
  artifactKey: Guid(3fe001d29ca95a348bbd6f6abd426b49) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_H.prefab using Guid(3fe001d29ca95a348bbd6f6abd426b49) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0afa1b8bc6d30c20c8a65b753dc3b1f8') in 0.035548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_Brown.prefab
  artifactKey: Guid(12844ca5226bd6b40adea1a57251c25f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_Brown.prefab using Guid(12844ca5226bd6b40adea1a57251c25f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd354766fec39816f3cee58c3ea5711d') in 0.0305626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_F.prefab
  artifactKey: Guid(e1ef978698948aa44ba2b5d6c6f90504) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_F.prefab using Guid(e1ef978698948aa44ba2b5d6c6f90504) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a283ec0498d7e62006919b9154e79e62') in 0.0260429 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Brown.prefab
  artifactKey: Guid(18d78272b9a4b254882fa45194eb318b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mushroom_Brown.prefab using Guid(18d78272b9a4b254882fa45194eb318b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bc871640dc7510091a16faf85549203c') in 0.0271043 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_C.prefab
  artifactKey: Guid(6c111f1606f6f354089b445976eb9bcd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_C.prefab using Guid(6c111f1606f6f354089b445976eb9bcd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd72a053eb1abff443f32578aa11dd07b') in 0.0358214 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_F.prefab
  artifactKey: Guid(c11064492f6e1114595925710b6825ed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_F.prefab using Guid(c11064492f6e1114595925710b6825ed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '38c914166cf29389f13fa63a8582f190') in 0.0280782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_C.prefab
  artifactKey: Guid(e9c45f806622211479bb545666655237) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_C.prefab using Guid(e9c45f806622211479bb545666655237) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '77441bef64db558c603c7103206584bc') in 0.0259729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_J.fbx
  artifactKey: Guid(95d9056a6763fff4eaa3c5dafbf8d368) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_J.fbx using Guid(95d9056a6763fff4eaa3c5dafbf8d368) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a83dc1cf280dc05c0df5349b851416c') in 0.0569534 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_D.prefab
  artifactKey: Guid(e20e5c66c7b3867419ef91de2d147d17) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_D.prefab using Guid(e20e5c66c7b3867419ef91de2d147d17) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0235bb13e261b5f72d5e40e4a7c3eae4') in 0.0240417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_B.prefab
  artifactKey: Guid(cdb23be4a0ec2914496262d81181b6a4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_Prop_B.prefab using Guid(cdb23be4a0ec2914496262d81181b6a4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1cc14e600d9f9b37d651a7cb8ab6799d') in 0.0297868 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_C.prefab
  artifactKey: Guid(882a0564b335ab849950d7d937fa3978) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_C.prefab using Guid(882a0564b335ab849950d7d937fa3978) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '43e55c6f473a3fc716bda52a4942862b') in 0.025476 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Table_B.fbx
  artifactKey: Guid(ab885dcd102ea724a914d51ad52326e9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Table_B.fbx using Guid(ab885dcd102ea724a914d51ad52326e9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c4f47cda832749a5b5735ff6f1ab7691') in 0.0469646 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000383 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Banana_Green.prefab
  artifactKey: Guid(ee9e1a1ade333ef45a7b03d63d20ccc3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Banana_Green.prefab using Guid(ee9e1a1ade333ef45a7b03d63d20ccc3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '209834ae9c9f3c9903780eb5b8ea6034') in 0.0382226 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_C.prefab
  artifactKey: Guid(26a6917091cf52a4d9c2ccefa20089cd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_C.prefab using Guid(26a6917091cf52a4d9c2ccefa20089cd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '47c951f82ddad7b752320bacdb232424') in 0.0247163 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000103 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_B.prefab
  artifactKey: Guid(9de4201efc700a34eadf1c8abb090356) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_B.prefab using Guid(9de4201efc700a34eadf1c8abb090356) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ce12d245f5fbf056194b37b3e23b0467') in 0.0346821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_A.prefab
  artifactKey: Guid(5579bf428dcf7a247be2d5ecacaf212d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_A.prefab using Guid(5579bf428dcf7a247be2d5ecacaf212d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '275e55cb19773248e5a552043bf9db93') in 0.0280115 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stone_B.fbx
  artifactKey: Guid(429989a6530aa7c4480797b51e7d0c56) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stone_B.fbx using Guid(429989a6530aa7c4480797b51e7d0c56) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '528b58d847652135f6dc6da38fa077a6') in 0.0533523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/CandelStick_A.prefab
  artifactKey: Guid(96cb795b0da218343914f0f463d43418) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/CandelStick_A.prefab using Guid(96cb795b0da218343914f0f463d43418) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '99222eb3e2a8174c80b137c453d21e61') in 0.0390638 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_A.prefab
  artifactKey: Guid(bd31055dd374afb49be3154c529ce030) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_A.prefab using Guid(bd31055dd374afb49be3154c529ce030) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fe5628d0474df5a62169c2cf6f9f0649') in 0.0314553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Watermelon_B.prefab
  artifactKey: Guid(a0b3b208bb575c944a60ef9ef2f1532e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Watermelon_B.prefab using Guid(a0b3b208bb575c944a60ef9ef2f1532e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '941292634f20d41155d90cea701be2f7') in 0.0410244 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_A.prefab
  artifactKey: Guid(80a141f289ed7ac4c9cbeda1cff72177) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_A.prefab using Guid(80a141f289ed7ac4c9cbeda1cff72177) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '768c6f4b90e78178cb5ebb9929e602c2') in 0.0375202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_C.prefab
  artifactKey: Guid(65ac9dabe47805b40921a3bdc94cd197) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_C.prefab using Guid(65ac9dabe47805b40921a3bdc94cd197) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '01f36c9e646878b5b5aa1d50c85cc0e4') in 0.0277687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_F.prefab
  artifactKey: Guid(858affb861c8a1843ac301426173e598) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_F.prefab using Guid(858affb861c8a1843ac301426173e598) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c97855d9fa9b7b249aabc90377133569') in 0.0266639 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_H.prefab
  artifactKey: Guid(50b15f828f63f5b479112845392a3d06) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_H.prefab using Guid(50b15f828f63f5b479112845392a3d06) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '291d7f7c620545e39559fbec7e54d990') in 0.0239435 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_D.prefab
  artifactKey: Guid(b94d6cd142f67804dbc8c88c21c9acbe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_D.prefab using Guid(b94d6cd142f67804dbc8c88c21c9acbe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '40ad0066d606e6ed6ad0e05653f89c9b') in 0.0382234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_I.fbx
  artifactKey: Guid(b0dd835ff03a7da4dad7ad924b63ac94) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_I.fbx using Guid(b0dd835ff03a7da4dad7ad924b63ac94) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5aee6cb8b34c394d7c78ba1fe5db1c57') in 0.0423415 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_C.fbx
  artifactKey: Guid(0efd95f667f8ea0469294692c7051a74) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_C.fbx using Guid(0efd95f667f8ea0469294692c7051a74) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf03b7391691f64b0ebf507d09bc8864') in 0.0540378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_G.prefab
  artifactKey: Guid(aa28a0cab50867643992a10b3bdef969) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_G.prefab using Guid(aa28a0cab50867643992a10b3bdef969) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '12d86e7ff823c927f4b35601391fd155') in 0.0245725 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_A.prefab
  artifactKey: Guid(d759df098ec7888498cc1b0a71ab9dd7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hourglass_A.prefab using Guid(d759df098ec7888498cc1b0a71ab9dd7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '27cf48f55d1ae52fe5b0512d4ef8040f') in 0.0426627 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_G.prefab
  artifactKey: Guid(2c99e27400f0fd04ca6577bcddd90f52) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_G.prefab using Guid(2c99e27400f0fd04ca6577bcddd90f52) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8d8807f2b50ff443b66b137120ec43ae') in 0.0265116 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Hammer_D.fbx
  artifactKey: Guid(7dbfa654df0c3974ba0c0a96329d2eb3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Hammer_D.fbx using Guid(7dbfa654df0c3974ba0c0a96329d2eb3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '214572d12a7092033707550d1f426a59') in 0.0470096 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_A.fbx
  artifactKey: Guid(da92c76c1b777de49bf718e4c45df2a7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_A.fbx using Guid(da92c76c1b777de49bf718e4c45df2a7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7de33d5bca029838a927686d0939b246') in 0.0412915 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_E.fbx
  artifactKey: Guid(a42eec02732dd1243ab2d2fa21e0f9f9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_E.fbx using Guid(a42eec02732dd1243ab2d2fa21e0f9f9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a66731d3c253bdd22e96b468b9b5eb37') in 0.0385054 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_D.fbx
  artifactKey: Guid(51e69a1e7db5ee949a2a3a2e80dd4c17) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_D.fbx using Guid(51e69a1e7db5ee949a2a3a2e80dd4c17) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a672d6fbf7dfe7713d729ed0af6ac56a') in 0.0498695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_G.prefab
  artifactKey: Guid(85630d828ece6e643b71a2eadbb75683) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_G.prefab using Guid(85630d828ece6e643b71a2eadbb75683) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f3f47cdb7c01cd68a762906392b9b701') in 0.0245072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_H.prefab
  artifactKey: Guid(f5e6f817b40a9524d8b38f590f162c4b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_H.prefab using Guid(f5e6f817b40a9524d8b38f590f162c4b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'daedca4e91393522df18c04eedb7304d') in 0.0307592 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_K.prefab
  artifactKey: Guid(a4e394a922949984db34822befab93f6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_K.prefab using Guid(a4e394a922949984db34822befab93f6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9be3604de6748b594be3d4a91f33f478') in 0.0306842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ink.fbx
  artifactKey: Guid(3d396bcd9d721cc4b8333e08af824731) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ink.fbx using Guid(3d396bcd9d721cc4b8333e08af824731) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bac440c5ce425e56bdfd4af87db4b51d') in 0.0364092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Foxtail.fbx
  artifactKey: Guid(ec6cd4ed709a0f149948ef7fd2582dd0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Foxtail.fbx using Guid(ec6cd4ed709a0f149948ef7fd2582dd0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a54ce292897f4f2d35b2be96ab4dc512') in 0.0371961 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Brazier_A.prefab
  artifactKey: Guid(a0a7382262a53914581c916ec0a0270a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Brazier_A.prefab using Guid(a0a7382262a53914581c916ec0a0270a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4b0695429279e94df3cf3d3db278d3f1') in 0.0287069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Watermelon_A.prefab
  artifactKey: Guid(050c0fca17652d94e943895df847424a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Watermelon_A.prefab using Guid(050c0fca17652d94e943895df847424a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a8c502c0d95eb4bc5d047c19777d2fb7') in 0.0301357 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Chisel.fbx
  artifactKey: Guid(9fa91d3755cf69d4fbe7f2b539ce9b0a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Chisel.fbx using Guid(9fa91d3755cf69d4fbe7f2b539ce9b0a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd73f6690e88a740ecbd0439e7e86c41f') in 0.0420532 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_K.fbx
  artifactKey: Guid(4c5b1ffdd781e184ebe40f331b719250) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_K.fbx using Guid(4c5b1ffdd781e184ebe40f331b719250) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eae77824ec378b14ab721673d48ddfea') in 0.0425349 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Paprika_Red.fbx
  artifactKey: Guid(cf9bc3f12ebb709498ae59b33aef5b05) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Paprika_Red.fbx using Guid(cf9bc3f12ebb709498ae59b33aef5b05) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '226c8a651314facc4452bc348b7c154e') in 0.0394688 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_E.fbx
  artifactKey: Guid(20c8b31a3646164429f9611a8de0e8a2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_E.fbx using Guid(20c8b31a3646164429f9611a8de0e8a2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3d8a6f72aa21a71c79c12708253f9c17') in 0.0378659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_B.prefab
  artifactKey: Guid(006c20dec171ecf4190736918a060adf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_B.prefab using Guid(006c20dec171ecf4190736918a060adf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a056bcb7062f4f7c1c5b1d3ad5dd90f') in 0.0344437 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Stable_C.fbx
  artifactKey: Guid(9c19d223476477541bead2256fcdb932) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Stable_C.fbx using Guid(9c19d223476477541bead2256fcdb932) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '860412d826ff1a213149f474ce962498') in 0.0383805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pumpkin_A.fbx
  artifactKey: Guid(f0f1d21574efa2d4cb8042feeeb25c43) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pumpkin_A.fbx using Guid(f0f1d21574efa2d4cb8042feeeb25c43) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c11ce25d475ba779544294724ebc4375') in 0.0378097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Steak.prefab
  artifactKey: Guid(4fab75505524f1e438c839e87ed8bd3b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Steak.prefab using Guid(4fab75505524f1e438c839e87ed8bd3b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3c14a717c1d0f28bd66aa4bafae87101') in 0.0291398 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Hammer_A.fbx
  artifactKey: Guid(faf8c031c9215ee4db57bd939b5d57e5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Hammer_A.fbx using Guid(faf8c031c9215ee4db57bd939b5d57e5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fc10f4c8be060ee6421c84dbeae2b5eb') in 0.0376839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_A.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_A.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76f7a2ffaa06cad15635bc4fa19442df') in 0.0275239 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_D.prefab
  artifactKey: Guid(b06a4bb702e578c4d8bf72f9d41869e9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_D.prefab using Guid(b06a4bb702e578c4d8bf72f9d41869e9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e61e2c76b27c2638b5ec97e5180c6c5b') in 0.0296029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_A.prefab
  artifactKey: Guid(4d7b876ea92a6cd48bc7e105d111b82e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_A.prefab using Guid(4d7b876ea92a6cd48bc7e105d111b82e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '849b40edb96551977b21850975a3be4e') in 0.0331933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_F.fbx
  artifactKey: Guid(079577d38c598604a8b5d4bcc77e4d6f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_F.fbx using Guid(079577d38c598604a8b5d4bcc77e4d6f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a44d7ff5fe38d8525d7dddc1c61e8806') in 0.0346366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Hammer_E.fbx
  artifactKey: Guid(5653a0879797d824a8a0fa7ada946da3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Hammer_E.fbx using Guid(5653a0879797d824a8a0fa7ada946da3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '60e5b89423c266c051949fe8acf62ddd') in 0.034551 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_J.fbx
  artifactKey: Guid(98b76d98cf191034aa4f9fe5e396789e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_J.fbx using Guid(98b76d98cf191034aa4f9fe5e396789e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aa9fffc6a67b0f25519cce50f56f5466') in 0.0352152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Meat_B.fbx
  artifactKey: Guid(54eb8462b735e5f4d86eb882364c02fa) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Meat_B.fbx using Guid(54eb8462b735e5f4d86eb882364c02fa) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ec2fca3ae1a890531d1279724e9fe76') in 0.0414959 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_A.prefab
  artifactKey: Guid(bd31055dd374afb49be3154c529ce030) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_A.prefab using Guid(bd31055dd374afb49be3154c529ce030) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8831a63a2aaeb8c595c6681d53241e1b') in 0.0322757 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_A.prefab
  artifactKey: Guid(7cd595948f2c8c14fb6a10b1048b5279) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_A.prefab using Guid(7cd595948f2c8c14fb6a10b1048b5279) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2657556c5624c07cf534c17d882b4ef2') in 0.0266384 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_B.prefab
  artifactKey: Guid(d830cdd1bf6fb374ba4b7c4bb0a293a6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_B.prefab using Guid(d830cdd1bf6fb374ba4b7c4bb0a293a6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75a7d098a2485637ec2f9bae01977a26') in 0.0284643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Wood_B.fbx
  artifactKey: Guid(78078a3682250da409af4a4c95546e3d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Wood_B.fbx using Guid(78078a3682250da409af4a4c95546e3d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9da5226f3036400b3f26ce1125f34653') in 0.034143 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_B.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_B.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bc8fa3377106685a75991230e1fd7fe1') in 0.041241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_F.fbx
  artifactKey: Guid(64a6ddbba3f86944bad5ccee21c85949) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_F.fbx using Guid(64a6ddbba3f86944bad5ccee21c85949) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cdd1cd58623ea6e3dc6bb4531de065a8') in 0.035963 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Sword_H.fbx
  artifactKey: Guid(720826502a182df4fad2b4b693b0a2c9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Sword_H.fbx using Guid(720826502a182df4fad2b4b693b0a2c9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6380a12541caa8c03cbf72db2934466d') in 0.0545635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_E.fbx
  artifactKey: Guid(930eddb7b5224d64bbdadb800582bd9e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_E.fbx using Guid(930eddb7b5224d64bbdadb800582bd9e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6536288a8d8293688132d0a710b57e0b') in 0.0372573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Flower_F.fbx
  artifactKey: Guid(bfaf0118dfdb26744a1bee73a1ccdb60) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Flower_F.fbx using Guid(bfaf0118dfdb26744a1bee73a1ccdb60) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '64778ddaa47f83d37d37e8d31c56e75e') in 0.0340856 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_H.prefab
  artifactKey: Guid(acfad7292fb21154db3fdd1a10ee8a42) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_H.prefab using Guid(acfad7292fb21154db3fdd1a10ee8a42) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0c8db947be0c1ef1cfad30f4bb1bae2a') in 0.035567 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_A.fbx
  artifactKey: Guid(0ed83d473cbd8af40ba458c6f2bd908a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_A.fbx using Guid(0ed83d473cbd8af40ba458c6f2bd908a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bedd328393a7d13154d2fd39960659bb') in 0.0388967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_C.prefab
  artifactKey: Guid(ef8fb51cbf896b24ebd858bf6b1bc5ed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_C.prefab using Guid(ef8fb51cbf896b24ebd858bf6b1bc5ed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5a784c34ecd9e8e6f3c4acae4181b99a') in 0.0323126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_L.prefab
  artifactKey: Guid(144f496e01322a444bd85e56886c2332) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_L.prefab using Guid(144f496e01322a444bd85e56886c2332) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f2b25f41a95e143d6659ee2398e0f463') in 0.0267695 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_L.fbx
  artifactKey: Guid(49780200cf40c1f41ba02e4d680ba8c2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_L.fbx using Guid(49780200cf40c1f41ba02e4d680ba8c2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd9b5404b4d82c42a83468e091dcf66df') in 0.0481497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000092 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_B.prefab
  artifactKey: Guid(70757251a402c19499beb58ddd6de94d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Meat_B.prefab using Guid(70757251a402c19499beb58ddd6de94d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7455b241a5c3f7fda9ac19a06dba96cf') in 0.0396342 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/CandelStick_B.prefab
  artifactKey: Guid(38a4e775d493bb7408d3c3001ca0804e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/CandelStick_B.prefab using Guid(38a4e775d493bb7408d3c3001ca0804e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1bd2133459db73064039a54092041628') in 0.0294969 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_H.prefab
  artifactKey: Guid(49a0237bc758372418f97f59ddf69271) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_H.prefab using Guid(49a0237bc758372418f97f59ddf69271) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'befab265d7a71b1195f9e29220edf2b7') in 0.0323929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_C.prefab
  artifactKey: Guid(b13cd54f8ded35948ae5427ef2548079) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/CandleChandelier_C.prefab using Guid(b13cd54f8ded35948ae5427ef2548079) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '535c4ca0ac1f273a9f63bfc8aa27624a') in 0.0485676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_A.fbx
  artifactKey: Guid(658adec9eb06de048b52c3c65b674240) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_A.fbx using Guid(658adec9eb06de048b52c3c65b674240) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc31217f5148136e45cbd23ae0b2f3cd') in 0.0561831 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_H.fbx
  artifactKey: Guid(1cc0d0a54d567da4c9bbe7ebc27b2fb4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_H.fbx using Guid(1cc0d0a54d567da4c9bbe7ebc27b2fb4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5f57cf9af6da8c828f1ba7c2ad7c5512') in 0.080075 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_D.fbx
  artifactKey: Guid(8f0e799d4f5d2384c8e3d9b26d30cc4c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_D.fbx using Guid(8f0e799d4f5d2384c8e3d9b26d30cc4c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3e8294b5ee4f228398dc63b55b261305') in 0.0384913 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000205 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_B.prefab
  artifactKey: Guid(9530b3b472e48284ca2beab46715a349) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_B.prefab using Guid(9530b3b472e48284ca2beab46715a349) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '12faa94bc096b489ca2851ddbce1325e') in 0.0268915 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_F.prefab
  artifactKey: Guid(49682b01faecb5a49af5af7118431fe6) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_F.prefab using Guid(49682b01faecb5a49af5af7118431fe6) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8086af838c416748cc6a68e530b2fe0c') in 0.0301606 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_A.prefab
  artifactKey: Guid(1ba3d1e5a1ddca344aea62a9311b68f1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_Open_A.prefab using Guid(1ba3d1e5a1ddca344aea62a9311b68f1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '168fec5d874a14e7e6c1ca4e66f8d835') in 0.0368098 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Cabinet_B.fbx
  artifactKey: Guid(b2c4de2fa4ac74e4b9090406cf3e0f21) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Cabinet_B.fbx using Guid(b2c4de2fa4ac74e4b9090406cf3e0f21) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1c0d18042655a7228f700745fb4dc24b') in 0.0385387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_B.prefab
  artifactKey: Guid(0f71f4a1d49887e4cb2b32cea8d79d36) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_B.prefab using Guid(0f71f4a1d49887e4cb2b32cea8d79d36) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3f94e31ce379fbd7f182ebaf1a812eef') in 0.0296917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Orange.prefab
  artifactKey: Guid(537023221717f1543a125910a3c207a7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Orange.prefab using Guid(537023221717f1543a125910a3c207a7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6d730dcd7d6a1f434b2fd1f9fb6b1a47') in 0.0361409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Brazier_A.fbx
  artifactKey: Guid(0ccee4c6db1744c4aa5bc021045029e3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Brazier_A.fbx using Guid(0ccee4c6db1744c4aa5bc021045029e3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bec6b2422a0c810d0178fe7be9d5fd3e') in 0.0414681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_B.prefab
  artifactKey: Guid(006c20dec171ecf4190736918a060adf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_B.prefab using Guid(006c20dec171ecf4190736918a060adf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '24e1cefbbffbb9e89d65289d38bbce40') in 0.0289751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_G.prefab
  artifactKey: Guid(295dc6d3327b06448953b1c14a9b2e4d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_G.prefab using Guid(295dc6d3327b06448953b1c14a9b2e4d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '402b19380b295dd97180253b27c9bfea') in 0.0344294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_D.fbx
  artifactKey: Guid(a237fa99c6990d742bc39ede1c0ea52d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_D.fbx using Guid(a237fa99c6990d742bc39ede1c0ea52d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c23d3f17dd8ba2d34c7dc24eb51ae455') in 0.0397288 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Script/Player/SimpleModelExample.cs
  artifactKey: Guid(b968a405dcb55064a92b6b2f4b096e59) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/SimpleModelExample.cs using Guid(b968a405dcb55064a92b6b2f4b096e59) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83d7a64683fbbb4514290b57a00600bc') in 0.0255846 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

Editor requested this worker to shutdown with reason: balancing worker count downwards
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0