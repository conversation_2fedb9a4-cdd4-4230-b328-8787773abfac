# 🎮 Hướng Dẫn Sử Dụng Unity Economy & Player System

## 📋 Tổng Quan

Hệ thống này bao gồm hai phần chính:
- **Economy System**: Quản lý tiền tệ (Lea), inventory, shop và giao dịch
- **Player System**: Điều khiển nhân vật với movement, camera, interaction và combat

### ✨ Tính Năng Chính

- 💰 **Hệ thống tiền tệ Lea** với auto-save
- 🎒 **Inventory system** với 50 slots
- 🏪 **Shop system** mua/bán vật phẩm
- 🎮 **Player Controller** hỗ trợ First/Third Person
- 🎯 **3D Shop Environment** với raycast interaction
- 🎨 **Visual Effects** và particle systems
- 🔧 **Inspector fields** có thể điều chỉnh trong Edit mode

---

## 🚀 Phần 1: Thiết Lập Economy System

### Bước 1: Setup EconomySystemSetup Component

1. **Tạo GameObject cho Economy System:**
   ```
   GameObject → Create Empty → "EconomySystemSetup"
   Add Component → Economy System Setup
   ```

2. **<PERSON><PERSON><PERSON> hình trong Inspector:**

   #### 🔧 Cài Đặt Cơ Bản
   ```
   Item Database: [Gán ItemDatabase asset]
   Tự Động Khởi Tạo: ✓ (Khởi tạo khi game start)
   Hiển Thị Log: ✓ (Hiện debug logs)
   ```

   #### 💰 Cài Đặt Currency
   ```
   Số Tiền Ban Đầu: 100 (Lea khởi tạo)
   Số Tiền Tối Đa: 999999 (Giới hạn tối đa)
   Tự Động Lưu Tiền: ✓ (Auto-save currency)
   ```

   #### 🎒 Cài Đặt Inventory
   ```
   Số Slot Inventory: 50 (Số ô chứa đồ)
   Tự Động Sắp Xếp: ✓ (Auto-sort items)
   Tự Động Lưu Inventory: ✓ (Auto-save inventory)
   ```

   #### 🏪 Cài Đặt Shop
   ```
   Phần Trăm Giảm Giá Bán: 0.5 (Bán được 50% giá mua)
   ```

   #### 💾 Cài Đặt Data
   ```
   Tự Động Lưu Data: ✓ (Auto-save game data)
   Khoảng Thời Gian Lưu: 60 (Lưu mỗi 60 giây)
   Tạo Backup: ✓ (Tạo file backup)
   ```

3. **Khởi tạo hệ thống:**
   - Bấm **"Khởi Tạo Hệ Thống"** trong Context Menu
   - Hoặc để **Tự Động Khởi Tạo** = true

### Bước 2: Tạo và Cấu Hình ItemDatabase

1. **Tạo ItemDatabase:**
   ```
   Right-click trong Project → Create → Economy System → Item Database
   ```

2. **Thêm vật phẩm:**
   - Mở ItemDatabase trong Inspector
   - Bấm **"Thêm Vật Phẩm Mới"**
   - Điền thông tin:
     ```
     ID: "wood" (Unique identifier)
     Tên Vật Phẩm: "Gỗ"
     Mô Tả: "Nguyên liệu xây dựng cơ bản"
     Giá Mua: 100 (Lea)
     Giá Bán: 50 (Lea)
     Loại: VatLieu
     Có Thể Bán: ✓
     Vật Phẩm Mới: ✓
     Nổi Bật: ✓
     ```

3. **Gán ItemDatabase:**
   - Kéo ItemDatabase vào field **Item Database** của EconomySystemSetup

### Bước 3: Cấu Hình Các Manager

#### 💰 CurrencyManager
```
Cài Đặt Tiền Tệ:
├── Số Tiền Hiện Tại: 100
├── Số Tiền Tối Đa: 999999
├── Tự Động Lưu: ✓
└── Hiển Thị Log: ✓

Hiệu Ứng:
├── Có Hiệu Ứng: ✓
└── Thời Gian Hiệu Ứng: 0.5s
```

#### 🎒 InventoryManager
```
Cài Đặt Inventory:
├── Số Slot Tối Đa: 50
├── Tự Động Sắp Xếp: ✓
├── Tự Động Lưu: ✓
└── Hiển Thị Log: ✓
```

#### 🏪 ShopManager
```
Cài Đặt Shop:
├── Item Database: [Gán ItemDatabase]
├── Hiển Thị Log: ✓
└── Phần Trăm Giảm Giá Bán: 0.5

Bộ Lọc:
├── Bộ Lọc Loại: VatLieu
├── Từ Khóa Tìm Kiếm: ""
├── Chỉ Hiển Thị Vật Phẩm Mới: ✗
└── Chỉ Hiển Thị Vật Phẩm Nổi Bật: ✗
```

### Bước 4: Test Economy System

1. **Sử dụng Context Menu:**
   - Right-click trên EconomySystemSetup
   - Chọn **"Hiển Thị Thông Tin"** để xem status
   - Chọn **"Reset Hệ Thống"** để reset về mặc định

2. **Test CurrencyManager:**
   - Right-click trên CurrencyManager
   - **"Thêm 1000 Lea"** / **"Trừ 500 Lea"** / **"Reset Tiền"**

3. **Test InventoryManager:**
   - Right-click trên InventoryManager
   - **"Thêm Vật Phẩm Test"** / **"Xóa Vật Phẩm"**

---

## 🎮 Phần 2: Thiết Lập Player System

### Bước 1: Setup PlayerController

1. **Tạo Player GameObject:**
   ```
   GameObject → Create Empty → "Player"
   Add Component → Character Controller
   Add Component → Player Input
   Add Component → Player Controller
   ```

2. **Cấu hình Character Controller:**
   ```
   Height: 2
   Radius: 0.5
   Center: (0, 1, 0)
   ```

3. **Cấu hình Player Input:**
   ```
   Actions: [Gán InputSystem_Actions asset]
   Default Map: Player
   Behavior: Send Messages
   ```

### Bước 2: Cấu Hình Movement Settings

#### 🏃 Movement Settings
```
Walk Speed: 5 (Tốc độ đi bộ)
Sprint Speed: 8 (Tốc độ chạy)
Crouch Speed: 2 (Tốc độ bò)
Acceleration: 10 (Tăng tốc)
Deceleration: 10 (Giảm tốc)
Jump Height: 2 (Độ cao nhảy)
Gravity: -9.81 (Trọng lực)
Air Control: 0.3 (Điều khiển trong không khí)
```

#### 📷 Camera Settings
```
Mouse Sensitivity: 100 (Độ nhạy chuột)
Gamepad Sensitivity: 150 (Độ nhạy tay cầm)
Invert Y: ✗ (Đảo trục Y)
Top Clamp: 90 (Giới hạn nhìn lên)
Bottom Clamp: -90 (Giới hạn nhìn xuống)
Smooth Camera: ✓ (Camera mượt)
Smooth Time: 0.1 (Thời gian làm mượt)
```

#### 🎥 Third Person Camera
```
Third Person Distance: 5 (Khoảng cách camera)
Third Person Offset: (0, 2, 0) (Offset camera)
Camera Transition Speed: 5 (Tốc độ chuyển đổi)
Camera Collision Layers: Default (Layer va chạm)
Camera Collision Buffer: 0.2 (Buffer va chạm)
```

#### 🎯 Interaction Settings
```
Interaction Range: 3 (Phạm vi tương tác)
Interaction Layer Mask: Default (Layer tương tác)
Use Sphere Cast: ✗ (Dùng sphere cast)
Sphere Radius: 0.5 (Bán kính sphere)
```

#### ⚔️ Combat Settings
```
Attack Damage: 10 (Sát thương)
Attack Range: 2 (Phạm vi tấn công)
Attack Cooldown: 0.5 (Thời gian hồi chiêu)
Enemy Layer Mask: Default (Layer kẻ thù)
Attack Force: 5 (Lực đẩy)
Camera Shake Intensity: 0.1 (Cường độ rung camera)
```

### Bước 3: Setup Camera System

1. **Tạo Camera Hierarchy:**
   ```
   Player
   └── CameraRoot (Empty GameObject)
       └── PlayerCamera (Camera)
   ```

2. **Cấu hình Camera:**
   ```
   Position: (0, 1.8, 0) cho CameraRoot
   Position: (0, 0, 0) cho PlayerCamera
   Field of View: 60
   Near Clip Plane: 0.1
   Far Clip Plane: 1000
   ```

3. **Gán References:**
   ```
   Player Camera Component: [Gán PlayerCamera]
   Camera Root: [Gán CameraRoot Transform]
   ```

### Bước 4: Setup Ground Check

1. **Tạo Ground Check:**
   ```
   Player
   └── GroundCheck (Empty GameObject)
   ```

2. **Cấu hình:**
   ```
   Position: (0, -1, 0) relative to Player
   Ground Check: [Gán GroundCheck Transform]
   Ground Distance: 0.4
   Ground Mask: Default
   ```

### Bước 5: Sử Dụng Debug Tools

#### 🔍 PlayerMovementDebugger
```
Add Component → Player Movement Debugger

Debug Settings:
├── Enable Debug UI: ✓
├── Enable Debug Logs: ✗
├── Show Ground Check: ✓
└── Show Movement Vectors: ✓
```

#### ✅ CameraSystemValidator
```
Add Component → Camera System Validator

Validation Settings:
├── Auto Fix Issues: ✓
├── Show Detailed Report: ✓
├── Validate Key: F2
├── Report Key: F3
└── Auto Fix Key: F4
```

---

## 🏪 Phần 3: Tích Hợp 3D Shop System

### Bước 1: Setup EconomyPlayerAdapter

1. **Thêm vào Player:**
   ```
   Player GameObject → Add Component → Economy Player Adapter
   ```

2. **Cấu hình Adapter:**
   ```
   Player References:
   ├── Player Controller: [Tự động tìm]
   ├── Player Interaction: [Tự động tìm]
   └── Player Camera: [Tự động tìm]

   Economy Interaction Settings:
   ├── Economy Raycast Distance: 5
   ├── Economy Interactable Layer: Default
   └── Show Debug Ray: ✓

   Visual Feedback:
   ├── Crosshair: [Gán UI Image]
   ├── Normal Crosshair Color: White
   └── Economy Crosshair Color: Yellow
   ```

### Bước 2: Tạo Shop Environment

1. **Tạo ShopEnvironment:**
   ```
   GameObject → Create Empty → "ShopEnvironment"
   Add Component → Shop Environment
   ```

2. **Tạo Shelves:**
   ```
   GameObject → 3D Object → Cube
   Tag: "Shelf"
   Scale: (2, 1, 0.5)
   Position: Sắp xếp trong cửa hàng
   ```

3. **Cấu hình ShopEnvironment:**
   ```
   Shop Layout:
   ├── Shelf Positions: [Gán các shelf Transforms]
   ├── Cashier Counter: [Optional]
   ├── Player Spawn Point: [Vị trí spawn]
   └── Shop Entrance/Exit: [Optional]

   Item Spawning:
   ├── Item Display Prefab: [Gán prefab]
   ├── Max Items Per Shelf: 5
   ├── Item Spacing: 1
   └── Auto Arrange Items: ✓

   Lighting:
   ├── Warm Light Color: (1, 0.9, 0.7)
   ├── Light Intensity: 1.2
   └── Enable Dynamic Lighting: ✓
   ```

### Bước 3: Tạo InteractableItem3D Prefab

1. **Tạo Item Display:**
   ```
   GameObject → Create Empty → "ItemDisplay"
   └── Visual (Cube với Material đẹp)
   └── PriceTag (3D Text)
   ```

2. **Thêm Components:**
   ```
   Add Component → Box Collider (IsTrigger = true)
   Add Component → Interactable Item 3D
   ```

3. **Cấu hình InteractableItem3D:**
   ```
   Item Configuration:
   ├── Item ID: "wood"
   ├── Stock Quantity: 10
   ├── Is For Sale: ✓
   └── Can Buy Back: ✓

   Interaction:
   ├── Interaction Distance: 3
   └── Interaction Key: E

   Visual Effects:
   ├── Rotate Display: ✓
   └── Rotation Speed: 30
   ```

4. **Tạo Prefab:**
   - Kéo ItemDisplay vào Project folder
   - Gán prefab vào ShopEnvironment

### Bước 4: Setup UI System

1. **Tạo Canvas:**
   ```
   GameObject → UI → Canvas
   Render Mode: Screen Space - Overlay
   ```

2. **Tạo Shop3DUI:**
   ```
   Canvas → Create Empty → "Shop3DUI"
   Add Component → Shop3DUI
   ```

3. **Tạo UI Elements:**
   ```
   Currency HUD:
   ├── Background Panel
   ├── Currency Icon
   ├── Currency Text (TextMeshPro)
   └── Position: Top-right corner

   Item Details Panel:
   ├── Background Panel
   ├── Item Name (TextMeshPro)
   ├── Item Description (TextMeshPro)
   ├── Item Price (TextMeshPro)
   ├── Quantity Slider
   ├── Buy Button
   ├── Sell Button
   └── Close Button

   Interaction Prompt:
   ├── Background Panel
   ├── Prompt Text: "Nhấn [F] để tương tác"
   └── Position: Center-bottom
   ```

4. **Gán UI References:**
   ```
   Shop3DUI Component:
   ├── Currency HUD: [Gán Currency Panel]
   ├── Currency Text: [Gán TextMeshPro]
   ├── Item Details Panel: [Gán Details Panel]
   ├── Item Name Text: [Gán TextMeshPro]
   ├── Item Description Text: [Gán TextMeshPro]
   ├── Item Price Text: [Gán TextMeshPro]
   ├── Quantity Slider: [Gán Slider]
   ├── Buy Button: [Gán Button]
   ├── Sell Button: [Gán Button]
   └── Close Button: [Gán Button]

   EconomyPlayerAdapter:
   ├── Economy Interaction Prompt: [Gán Prompt Panel]
   ├── Prompt Text: [Gán TextMeshPro]
   ├── Item Name Text: [Gán TextMeshPro]
   └── Item Price Text: [Gán TextMeshPro]
   ```

---

## 🎮 Phần 4: Hướng Dẫn Sử Dụng Trong Game

### Controls - Phím Điều Khiển

| Phím | Chức Năng | Hệ Thống |
|------|-----------|----------|
| **WASD** | Di chuyển | Player Movement |
| **Mouse** | Nhìn xung quanh | Player Camera |
| **Shift** | Chạy nhanh | Player Movement |
| **Ctrl** | Bò | Player Movement |
| **Space** | Nhảy | Player Movement |
| **E** | Tương tác thông thường | Player Interaction |
| **F** | Tương tác Economy | Economy Adapter |
| **Tab** | Mở/đóng Shop Menu | Economy Adapter |
| **C** | Chuyển đổi camera | Player Camera |
| **Mouse Click** | Tấn công | Player Combat |
| **Esc** | Đóng UI | UI System |

### Cách Sử Dụng Economy System

#### 💰 Quản Lý Tiền Tệ
- **Xem số dư:** Hiển thị ở góc màn hình
- **Kiếm tiền:** Bán vật phẩm trong inventory
- **Tiêu tiền:** Mua vật phẩm từ shop

#### 🎒 Quản Lý Inventory
- **Xem inventory:** Mở shop menu (Tab)
- **Thêm vật phẩm:** Mua từ shop hoặc nhặt trong game
- **Xóa vật phẩm:** Bán hoặc sử dụng
- **Sắp xếp:** Tự động sắp xếp theo loại

#### 🏪 Mua/Bán Vật Phẩm
1. **Tiến gần vật phẩm** → Crosshair chuyển màu vàng
2. **Nhấn F** → Mở thông tin chi tiết
3. **Chọn số lượng** → Dùng slider
4. **Nhấn "Mua"** → Xác nhận giao dịch
5. **Nhấn "Bán"** → Bán vật phẩm có trong inventory

### Debug và Validation

#### 🔍 Debug Tools
- **F2:** Validate camera system
- **F3:** Hiển thị detailed report
- **F4:** Auto-fix camera issues
- **Debug UI:** Hiển thị thông tin movement realtime

#### ✅ Validation Checklist
```
Player System:
├── PlayerController: ✓
├── CharacterController: ✓
├── PlayerInput: ✓
├── Camera Setup: ✓
└── Ground Check: ✓

Economy System:
├── EconomySystemSetup: ✓
├── ItemDatabase: ✓
├── CurrencyManager: ✓
├── InventoryManager: ✓
└── ShopManager: ✓

3D Shop Integration:
├── EconomyPlayerAdapter: ✓
├── ShopEnvironment: ✓
├── InteractableItem3D: ✓
├── Shop3DUI: ✓
└── UI Canvas: ✓
```

---

## 🔧 Troubleshooting - Khắc Phục Lỗi

### Lỗi Thường Gặp

#### ❌ Player không di chuyển được
**Nguyên nhân:**
- CharacterController chưa được gán
- Ground check không hoạt động
- Input System chưa setup đúng

**Cách khắc phục:**
1. Kiểm tra CharacterController component
2. Đảm bảo Ground có Collider
3. Gán InputSystem_Actions asset
4. Sử dụng PlayerMovementFixer: `Tools → Player Controller → Fix Movement Issues`

#### ❌ Camera không hoạt động
**Nguyên nhân:**
- Camera references chưa được gán
- CameraRoot không đúng hierarchy
- PlayerCamera component thiếu

**Cách khắc phục:**
1. Sử dụng CameraAutoSetup: `Tools → Player Controller → Auto Setup Camera`
2. Kiểm tra hierarchy: Player → CameraRoot → PlayerCamera
3. Gán references trong PlayerController

#### ❌ Economy System không hoạt động
**Nguyên nhân:**
- EconomySystemSetup chưa khởi tạo
- ItemDatabase chưa được gán
- Managers chưa được tạo

**Cách khắc phục:**
1. Bấm "Khởi Tạo Hệ Thống" trong EconomySystemSetup
2. Gán ItemDatabase asset
3. Kiểm tra Console logs để xem lỗi chi tiết

#### ❌ 3D Shop không tương tác được
**Nguyên nhân:**
- EconomyPlayerAdapter chưa được thêm
- InteractableItem3D thiếu Collider
- UI references chưa được gán

**Cách khắc phục:**
1. Thêm EconomyPlayerAdapter vào Player
2. Đảm bảo InteractableItem3D có Box Collider (IsTrigger = true)
3. Gán đầy đủ UI references trong Shop3DUI

#### ❌ UI không hiển thị
**Nguyên nhân:**
- Canvas Render Mode không đúng
- EventSystem thiếu trong scene
- UI references chưa được gán

**Cách khắc phục:**
1. Đặt Canvas Render Mode = Screen Space - Overlay
2. Thêm EventSystem: `GameObject → UI → Event System`
3. Kiểm tra tất cả UI references trong Inspector

### Performance Tips

#### 🚀 Tối Ưu Hóa
1. **Object Pooling:** Sử dụng cho particle effects
2. **LOD System:** Giảm detail khi xa camera
3. **Culling:** Ẩn objects không cần thiết
4. **Batch Rendering:** Gộp draw calls

#### 📊 Monitoring
- Sử dụng Profiler để kiểm tra performance
- Theo dõi memory usage
- Kiểm tra draw calls và batches

---

## 🎯 Kết Luận

Hệ thống Unity Economy & Player này cung cấp:

✅ **Complete Solution** - Giải pháp hoàn chỉnh cho game economy
✅ **Easy Setup** - Thiết lập dễ dàng với Inspector fields
✅ **Flexible Configuration** - Cấu hình linh hoạt trong Edit mode
✅ **Rich Features** - Tính năng phong phú và mở rộng được
✅ **Production Ready** - Sẵn sàng cho production với auto-save
✅ **Vietnamese Support** - Hỗ trợ tiếng Việt đầy đủ

Với hướng dẫn này, bạn có thể tạo ra một hệ thống economy và player hoàn chỉnh cho game Unity của mình!

---

## 📞 Hỗ Trợ

Nếu gặp vấn đề:
1. Kiểm tra Console logs để xem lỗi chi tiết
2. Sử dụng Debug tools và Validation tools
3. Xem lại cấu hình components trong Inspector
4. Test từng phần một cách riêng biệt
5. Sử dụng Context Menu để test các chức năng

**Happy Coding! 🚀**
