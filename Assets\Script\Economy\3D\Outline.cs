using System.Collections.Generic;
using UnityEngine;

namespace EconomySystem.Shop3D
{
    /// <summary>
    /// Component tạo hiệu ứng outline cho objects
    /// Sử dụng để highlight vật phẩm khi hover
    /// </summary>
    [DisallowMultipleComponent]
    public class Outline : MonoBehaviour
    {
        [Header("Outline Settings")]
        [SerializeField] private Color outlineColor = Color.yellow;
        [SerializeField] [Range(0f, 10f)] private float outlineWidth = 2f;
        [SerializeField] private bool precomputeOutline = false;

        [Header("Advanced Settings")]
        [SerializeField] private bool useDepthBuffer = true;
        [SerializeField] private bool enableFlashing = false;
        [SerializeField] private float flashSpeed = 2f;

        // Private variables
        private Renderer[] renderers;
        private Material outlineMaskMaterial;
        private Material outlineFillMaterial;
        private bool needsUpdate = false;

        // Static materials
        private static Material _outlineMaskMaterial;
        private static Material _outlineFillMaterial;

        #region Properties
        public Color OutlineColor
        {
            get { return outlineColor; }
            set
            {
                outlineColor = value;
                needsUpdate = true;
            }
        }

        public float OutlineWidth
        {
            get { return outlineWidth; }
            set
            {
                outlineWidth = Mathf.Clamp(value, 0f, 10f);
                needsUpdate = true;
            }
        }
        #endregion

        #region Unity Methods
        private void Awake()
        {
            // Get all renderers
            renderers = GetComponentsInChildren<Renderer>();

            // Create materials if needed
            CreateMaterials();
        }

        private void OnEnable()
        {
            if (renderers == null)
            {
                renderers = GetComponentsInChildren<Renderer>();
            }

            UpdateMaterialProperties();
        }

        private void OnDisable()
        {
            // Remove outline materials from renderers
            foreach (var renderer in renderers)
            {
                if (renderer != null)
                {
                    RemoveOutlineMaterials(renderer);
                }
            }
        }

        private void Update()
        {
            if (needsUpdate)
            {
                needsUpdate = false;
                UpdateMaterialProperties();
            }

            if (enableFlashing)
            {
                UpdateFlashing();
            }
        }

        private void OnValidate()
        {
            needsUpdate = true;
        }
        #endregion

        #region Material Management
        private void CreateMaterials()
        {
            if (_outlineMaskMaterial == null)
            {
                _outlineMaskMaterial = CreateOutlineMaskMaterial();
            }

            if (_outlineFillMaterial == null)
            {
                _outlineFillMaterial = CreateOutlineFillMaterial();
            }

            outlineMaskMaterial = _outlineMaskMaterial;
            outlineFillMaterial = _outlineFillMaterial;
        }

        private Material CreateOutlineMaskMaterial()
        {
            // Tạo material cho mask pass
            Material material = new Material(Shader.Find("Hidden/OutlineMask"));
            material.name = "OutlineMask";
            return material;
        }

        private Material CreateOutlineFillMaterial()
        {
            // Tạo material cho fill pass
            Material material = new Material(Shader.Find("Hidden/OutlineFill"));
            material.name = "OutlineFill";
            return material;
        }

        private void UpdateMaterialProperties()
        {
            if (outlineFillMaterial != null)
            {
                outlineFillMaterial.SetColor("_OutlineColor", outlineColor);
                outlineFillMaterial.SetFloat("_OutlineWidth", outlineWidth);
            }

            // Apply materials to renderers
            foreach (var renderer in renderers)
            {
                if (renderer != null)
                {
                    ApplyOutlineMaterials(renderer);
                }
            }
        }

        private void ApplyOutlineMaterials(Renderer renderer)
        {
            var materials = new List<Material>(renderer.sharedMaterials);

            // Remove existing outline materials
            materials.RemoveAll(m => m != null && (m.name.Contains("OutlineMask") || m.name.Contains("OutlineFill")));

            // Add outline materials
            materials.Add(outlineMaskMaterial);
            materials.Add(outlineFillMaterial);

            renderer.materials = materials.ToArray();
        }

        private void RemoveOutlineMaterials(Renderer renderer)
        {
            var materials = new List<Material>(renderer.sharedMaterials);
            materials.RemoveAll(m => m != null && (m.name.Contains("OutlineMask") || m.name.Contains("OutlineFill")));
            renderer.materials = materials.ToArray();
        }
        #endregion

        #region Effects
        private void UpdateFlashing()
        {
            if (outlineFillMaterial != null)
            {
                float alpha = (Mathf.Sin(Time.time * flashSpeed) + 1f) * 0.5f;
                Color flashColor = new Color(outlineColor.r, outlineColor.g, outlineColor.b, alpha);
                outlineFillMaterial.SetColor("_OutlineColor", flashColor);
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Bật/tắt outline
        /// </summary>
        /// <param name="enable">True để bật outline</param>
        public void SetOutlineEnabled(bool enable)
        {
            enabled = enable;
        }

        /// <summary>
        /// Đặt màu outline
        /// </summary>
        /// <param name="color">Màu mới</param>
        public void SetOutlineColor(Color color)
        {
            OutlineColor = color;
        }

        /// <summary>
        /// Đặt độ rộng outline
        /// </summary>
        /// <param name="width">Độ rộng mới</param>
        public void SetOutlineWidth(float width)
        {
            OutlineWidth = width;
        }

        /// <summary>
        /// Bật/tắt hiệu ứng nhấp nháy
        /// </summary>
        /// <param name="enable">True để bật nhấp nháy</param>
        public void SetFlashing(bool enable)
        {
            enableFlashing = enable;
            if (!enable && outlineFillMaterial != null)
            {
                outlineFillMaterial.SetColor("_OutlineColor", outlineColor);
            }
        }

        /// <summary>
        /// Làm mới outline (khi có thay đổi về renderers)
        /// </summary>
        public void RefreshOutline()
        {
            renderers = GetComponentsInChildren<Renderer>();
            needsUpdate = true;
        }
        #endregion

        #region Static Methods
        /// <summary>
        /// Thêm outline vào GameObject
        /// </summary>
        /// <param name="obj">GameObject cần thêm outline</param>
        /// <param name="color">Màu outline</param>
        /// <param name="width">Độ rộng outline</param>
        /// <returns>Component Outline đã tạo</returns>
        public static Outline AddOutline(GameObject obj, Color color, float width = 2f)
        {
            if (obj == null) return null;

            Outline outline = obj.GetComponent<Outline>();
            if (outline == null)
            {
                outline = obj.AddComponent<Outline>();
            }

            outline.OutlineColor = color;
            outline.OutlineWidth = width;
            outline.enabled = true;

            return outline;
        }

        /// <summary>
        /// Xóa outline khỏi GameObject
        /// </summary>
        /// <param name="obj">GameObject cần xóa outline</param>
        public static void RemoveOutline(GameObject obj)
        {
            if (obj == null) return;

            Outline outline = obj.GetComponent<Outline>();
            if (outline != null)
            {
                outline.enabled = false;
                DestroyImmediate(outline);
            }
        }
        #endregion

        #region Editor Methods
        #if UNITY_EDITOR
        [ContextMenu("Refresh Outline")]
        private void EditorRefreshOutline()
        {
            RefreshOutline();
        }

        [ContextMenu("Test Flashing")]
        private void EditorTestFlashing()
        {
            SetFlashing(!enableFlashing);
        }
        #endif
        #endregion
    }

    /// <summary>
    /// Simple outline shader fallback nếu không có custom shader
    /// </summary>
    public static class OutlineShaderFallback
    {
        public static readonly string OutlineMaskShader = @"
            Shader ""Hidden/OutlineMask"" {
                Properties {
                    _OutlineWidth (""Outline Width"", Range(0, 10)) = 2
                }
                SubShader {
                    Tags { ""Queue""=""Transparent+100"" }
                    Pass {
                        Cull Off
                        ZWrite Off
                        ZTest Always
                        ColorMask 0
                        
                        CGPROGRAM
                        #pragma vertex vert
                        #pragma fragment frag
                        #include ""UnityCG.cginc""
                        
                        struct appdata {
                            float4 vertex : POSITION;
                        };
                        
                        struct v2f {
                            float4 pos : SV_POSITION;
                        };
                        
                        float _OutlineWidth;
                        
                        v2f vert(appdata v) {
                            v2f o;
                            o.pos = UnityObjectToClipPos(v.vertex);
                            return o;
                        }
                        
                        fixed4 frag(v2f i) : SV_Target {
                            return fixed4(1,1,1,1);
                        }
                        ENDCG
                    }
                }
            }";

        public static readonly string OutlineFillShader = @"
            Shader ""Hidden/OutlineFill"" {
                Properties {
                    _OutlineColor (""Outline Color"", Color) = (1,1,0,1)
                    _OutlineWidth (""Outline Width"", Range(0, 10)) = 2
                }
                SubShader {
                    Tags { ""Queue""=""Transparent+110"" }
                    Pass {
                        Cull Off
                        ZWrite Off
                        ZTest Always
                        Blend SrcAlpha OneMinusSrcAlpha
                        
                        CGPROGRAM
                        #pragma vertex vert
                        #pragma fragment frag
                        #include ""UnityCG.cginc""
                        
                        struct appdata {
                            float4 vertex : POSITION;
                        };
                        
                        struct v2f {
                            float4 pos : SV_POSITION;
                        };
                        
                        fixed4 _OutlineColor;
                        
                        v2f vert(appdata v) {
                            v2f o;
                            o.pos = UnityObjectToClipPos(v.vertex);
                            return o;
                        }
                        
                        fixed4 frag(v2f i) : SV_Target {
                            return _OutlineColor;
                        }
                        ENDCG
                    }
                }
            }";
    }
}
