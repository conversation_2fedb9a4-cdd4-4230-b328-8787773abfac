Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker6
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker6.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21776]  Target information:

Player connection [21776]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 821449780 [EditorId] 821449780 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21776] Host joined multi-casting on [***********:54997]...
Player connection [21776] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.61 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56328
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002932 seconds.
- Loaded All Assemblies, in  1.261 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.922 seconds
Domain Reload Profiling: 2184ms
	BeginReloadAssembly (559ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (113ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (107ms)
	LoadAllAssembliesAndSetupDomain (450ms)
		LoadAssemblies (556ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (443ms)
			TypeCache.Refresh (441ms)
				TypeCache.ScanAssembly (401ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (924ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (825ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (34ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (513ms)
			ProcessInitializeOnLoadMethodAttributes (122ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.328 seconds
Refreshing native plugins compatible for Editor in 2.04 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.746 seconds
Domain Reload Profiling: 4073ms
	BeginReloadAssembly (395ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (77ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (1767ms)
		LoadAssemblies (936ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1051ms)
			TypeCache.Refresh (851ms)
				TypeCache.ScanAssembly (794ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1746ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1315ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (351ms)
			ProcessInitializeOnLoadAttributes (835ms)
			ProcessInitializeOnLoadMethodAttributes (112ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 3.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (10.6 MB). Loaded Objects now: 7294.
Memory consumption went from 165.1 MB to 154.5 MB.
Total: 90.865900 ms (FindLiveObjects: 5.013600 ms CreateObjectMapping: 2.390300 ms MarkObjects: 11.284200 ms  DeleteObjects: 72.175400 ms)

========================================================================
Received Import Request.
  Time since last request: 103330.504443 seconds.
  path: Assets/Settings/PC_Renderer.asset
  artifactKey: Guid(f288ae1f4751b564a96ac7587541f7a2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/PC_Renderer.asset using Guid(f288ae1f4751b564a96ac7587541f7a2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ba5e527252d83125160b7ab1eb317aac') in 1.3590954 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_A.prefab
  artifactKey: Guid(24af20b58bdc99f48877c7f5956c0c75) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_A.prefab using Guid(24af20b58bdc99f48877c7f5956c0c75) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '83c9c7dd11cd6ea9abc9cb8e8d1bb40c') in 0.163458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_F.fbx
  artifactKey: Guid(f3d56f7556c807345b35bd1e1d23fd0e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_F.fbx using Guid(f3d56f7556c807345b35bd1e1d23fd0e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e10de973556d61cfd5672c3826b1416a') in 0.0948168 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Settings/PC_RPAsset.asset
  artifactKey: Guid(4b83569d67af61e458304325a23e5dfd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Settings/PC_RPAsset.asset using Guid(4b83569d67af61e458304325a23e5dfd) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5170b1a5db3e202131ad7c1f5a331d7a') in 0.0270823 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_A.prefab
  artifactKey: Guid(53d6a15837fee4b4da9cad56253677f4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_A.prefab using Guid(53d6a15837fee4b4da9cad56253677f4) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f2cbbcb4349094de019ee67c93dbebdc') in 0.0408474 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BalanceScale.prefab
  artifactKey: Guid(f1eaea8a13c030e4bafee40b015fbd82) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BalanceScale.prefab using Guid(f1eaea8a13c030e4bafee40b015fbd82) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'cbb973a52f8aa80a74f8053c5948c5ff') in 0.0235255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Egg_B.prefab
  artifactKey: Guid(a2f546d1a3e8fcd49aabf5471512146e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Egg_B.prefab using Guid(a2f546d1a3e8fcd49aabf5471512146e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '64706dda48a751b7f334090a9837729e') in 0.0251896 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_B.prefab
  artifactKey: Guid(8cded43a618401f4e88daa88e4929c71) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_B.prefab using Guid(8cded43a618401f4e88daa88e4929c71) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ccec9b68f1c958d28315421cabc3ff7b') in 0.0371389 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/FeatherQuillPen_B.fbx
  artifactKey: Guid(8ef1889395462f94780d5d45ee90b488) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/FeatherQuillPen_B.fbx using Guid(8ef1889395462f94780d5d45ee90b488) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c30316955787873a33d41c5cb5dcce60') in 0.0414889 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_K.prefab
  artifactKey: Guid(f2dec7f6f47dbb546bbfec5126d5dfa0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_K.prefab using Guid(f2dec7f6f47dbb546bbfec5126d5dfa0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ddc53dff2c3761facbb757271b311143') in 0.0296671 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_D.prefab
  artifactKey: Guid(59da3f2227f916f46b3ff2272ac213c7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodA_D.prefab using Guid(59da3f2227f916f46b3ff2272ac213c7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '995c15632d8e5788917825b7352469f6') in 0.0254902 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_G.prefab
  artifactKey: Guid(924c7f8dd6a40df49a1c66d31992b179) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_G.prefab using Guid(924c7f8dd6a40df49a1c66d31992b179) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'fd052aba3a394f28e7b7024d28b6b5b0') in 0.0190195 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_B.fbx
  artifactKey: Guid(648879a47cb7297479a19aec6c3f4a4b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_B.fbx using Guid(648879a47cb7297479a19aec6c3f4a4b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b03f2decffe23f72c361d80bb854e03a') in 0.0362467 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Script/Economy/UI
  artifactKey: Guid(2cea1e09e9f3cd64aaa26fe404e53286) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/UI using Guid(2cea1e09e9f3cd64aaa26fe404e53286) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bcf97c53ea1a0175858ad1d229542ee2') in 0.0236334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Cheese_B.fbx
  artifactKey: Guid(678a102d580bc0a468bfb6d68ca2586c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Cheese_B.fbx using Guid(678a102d580bc0a468bfb6d68ca2586c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8a9210a2c6ddd6e9f1b5a00061ed3a34') in 0.0311625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_E.prefab
  artifactKey: Guid(60d1f2dd17728a14cb732d65fde272b1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_E.prefab using Guid(60d1f2dd17728a14cb732d65fde272b1) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a7c79f06f42d5ef60c702d740e9b8a74') in 0.035818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Open_D.fbx
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Box_Wood_Open_D.fbx using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cb70bcb97f73522bfbf61a60a1dde3ca') in 0.0412396 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_J.prefab
  artifactKey: Guid(a5d758855e4e56f40aae14aacad1eb21) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_J.prefab using Guid(a5d758855e4e56f40aae14aacad1eb21) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9164f089088a430be81d7baff6e282b0') in 0.0294968 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Eggplant.fbx
  artifactKey: Guid(a376dad3e0ef7374aa7a73da6c32fa66) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Eggplant.fbx using Guid(a376dad3e0ef7374aa7a73da6c32fa66) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e11f3a365a50dd54377c0694a0515a88') in 0.0375829 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Sword_D.fbx
  artifactKey: Guid(33829bc22f3016d498b05982d95dbe7d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Sword_D.fbx using Guid(33829bc22f3016d498b05982d95dbe7d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a0c2c4a6604db50c381ea106e9de879f') in 0.0340405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fire_B.fbx
  artifactKey: Guid(084b175763b082c418ebed54798d1c29) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fire_B.fbx using Guid(084b175763b082c418ebed54798d1c29) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '392c4618e330c46210f74e8034f18773') in 0.0453859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000097 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_J.prefab
  artifactKey: Guid(a5d758855e4e56f40aae14aacad1eb21) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_J.prefab using Guid(a5d758855e4e56f40aae14aacad1eb21) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '89dfac361bb57267e5dd917c2f78d3a9') in 0.0224174 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_Part_B.prefab
  artifactKey: Guid(4cbab2649cea49249bfe62bdd4964831) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_Part_B.prefab using Guid(4cbab2649cea49249bfe62bdd4964831) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bf702a0da9dcf2875dc8dc14f30454b5') in 0.0224653 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_C.prefab
  artifactKey: Guid(2f7df7c24910f3541ab7411ecbdf6fed) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Manger_C.prefab using Guid(2f7df7c24910f3541ab7411ecbdf6fed) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd171bd88d132563c8918ed09099493b3') in 0.0261118 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_D.prefab
  artifactKey: Guid(916b190bbc9ce1542b8201779ceb4ed8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_D.prefab using Guid(916b190bbc9ce1542b8201779ceb4ed8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ebf59af8b150173d1f79eb37fa0e0426') in 0.0314813 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_I.prefab
  artifactKey: Guid(726367ff540962245a7506c5337b6392) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_I.prefab using Guid(726367ff540962245a7506c5337b6392) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'efd5d9ba245b47f23f2ce36e47324548') in 0.0232313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_K.prefab
  artifactKey: Guid(b36eb152245a5b04db85284ae46354bf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_K.prefab using Guid(b36eb152245a5b04db85284ae46354bf) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e34f085325475cbcd97999c2aebbfb92') in 0.0244776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_B.prefab
  artifactKey: Guid(4b54c74a7d892f44e82fad64a0c8b59f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_B.prefab using Guid(4b54c74a7d892f44e82fad64a0c8b59f) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7ecb07d33f797230f6eb6c499a0b3d95') in 0.021287 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000104 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_G.prefab
  artifactKey: Guid(afb05f76f5d21dc4a82d4d65b6096657) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_G.prefab using Guid(afb05f76f5d21dc4a82d4d65b6096657) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bb2bf1157ef9e7a80801b4bb6cc4e0af') in 0.0220999 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Manger_A.fbx
  artifactKey: Guid(50a27796d4a41c24e82ea25ec988bdb7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Manger_A.fbx using Guid(50a27796d4a41c24e82ea25ec988bdb7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'da493b47a80220bace981f4d32ab2962') in 0.0352556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Prop_A.fbx
  artifactKey: Guid(24dda46bc17bf9449adfa8ec61634e3c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Prop_A.fbx using Guid(24dda46bc17bf9449adfa8ec61634e3c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1d4bc73907765743dc1973afdca5bd8d') in 0.0366466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bucket_Wood_D.fbx
  artifactKey: Guid(153f51697a5e9ae41a1039378ed0c647) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bucket_Wood_D.fbx using Guid(153f51697a5e9ae41a1039378ed0c647) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '542b4aa0905f1345e2ee782aa2616d4c') in 0.0403344 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000693 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Orange.prefab
  artifactKey: Guid(46b9b12749e1a294aaf8a315ed152cd9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Orange.prefab using Guid(46b9b12749e1a294aaf8a315ed152cd9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f220c3aef5ae23e4cfec7ee4290b49e5') in 0.026501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Red.prefab
  artifactKey: Guid(47a6f69b730842a45b13634c24e06ebe) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Paprika_Red.prefab using Guid(47a6f69b730842a45b13634c24e06ebe) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7db6a9073ecb70b8b567af104472c6b9') in 0.0406465 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_A.prefab
  artifactKey: Guid(6b8661102e852804eb79234ad68eef57) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Brown_A.prefab using Guid(6b8661102e852804eb79234ad68eef57) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b3e498f62adc7e692a155d29665fb721') in 0.0303121 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_C.prefab
  artifactKey: Guid(fe7f934f17ffd744ab90d3b77cbf8916) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Parchment_C.prefab using Guid(fe7f934f17ffd744ab90d3b77cbf8916) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7e7c1418d3ca68e7dad28f0355fddce1') in 0.022525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Greenonion.fbx
  artifactKey: Guid(255921de587c3d245899e3570c5db478) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Greenonion.fbx using Guid(255921de587c3d245899e3570c5db478) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c7395415e76ad4bf16c298c2d3f18f27') in 0.0376403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Grey.prefab
  artifactKey: Guid(fb0ff6d204402ca4ca6b6512228ba0af) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Grey.prefab using Guid(fb0ff6d204402ca4ca6b6512228ba0af) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b281d1cf0d2fd04b75701f1f9e21575b') in 0.0216079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_B.prefab
  artifactKey: Guid(fee301f099047f042a49b1c068b833cc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_B.prefab using Guid(fee301f099047f042a49b1c068b833cc) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6ba16f51b39f850458c93b57700a99ae') in 0.0197777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_C.prefab
  artifactKey: Guid(69fe52486d86d114cbd5d7e43cc480aa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_C.prefab using Guid(69fe52486d86d114cbd5d7e43cc480aa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b638d63ec297488a6d1dd1e926ba16d0') in 0.0347828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_G.prefab
  artifactKey: Guid(6eecd510f46dcd84ebac96e34757b6ba) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Signpost_G.prefab using Guid(6eecd510f46dcd84ebac96e34757b6ba) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '835d5e6392af8f3169db940732e121c2') in 0.0222974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_A.prefab
  artifactKey: Guid(4d4bdfb155e4ac844bc527e8a3abfe7e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_A.prefab using Guid(4d4bdfb155e4ac844bc527e8a3abfe7e) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2b20791ba765ed85dc8dc03e69e2c4d0') in 0.0303347 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_G.prefab
  artifactKey: Guid(295dc6d3327b06448953b1c14a9b2e4d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_G.prefab using Guid(295dc6d3327b06448953b1c14a9b2e4d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '79b281842e50dfc67f28b86c725e26dc') in 0.0390702 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_K.prefab
  artifactKey: Guid(b6c1f3c25e5f21c45a2509c51d895734) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_K.prefab using Guid(b6c1f3c25e5f21c45a2509c51d895734) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e1228abc0c06d20117ab86fdda081c60') in 0.0281584 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Readme.asset
  artifactKey: Guid(8105016687592461f977c054a80ce2f2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Readme.asset using Guid(8105016687592461f977c054a80ce2f2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5ac5786eea55ea811a2e2751d46b8b29') in 0.064571 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_E.prefab
  artifactKey: Guid(84649cb73ad3ab44cad9ae0cacfe83ca) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_E.prefab using Guid(84649cb73ad3ab44cad9ae0cacfe83ca) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'edfff265ab9338b09de860aea7d88a0f') in 0.083888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tower_Wood.prefab
  artifactKey: Guid(fd11d6682cb39cd4d866bd44db1eb46b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tower_Wood.prefab using Guid(fd11d6682cb39cd4d866bd44db1eb46b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b7d93a12531c94e321df052613f39867') in 0.0239237 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_A.prefab
  artifactKey: Guid(a3117c614e1b8054d931b375b4322771) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_A.prefab using Guid(a3117c614e1b8054d931b375b4322771) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b96365eba1cae19f5697d4068eb4161b') in 0.0234658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_B.prefab
  artifactKey: Guid(726cae9a9eadce34cb37f945e9c37432) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tree_B.prefab using Guid(726cae9a9eadce34cb37f945e9c37432) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8c2cf59aa2cb26ca411a1b638af58ae5') in 0.0355051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Green.prefab
  artifactKey: Guid(79d7201dc6fc5b94f8c73bfb143b8af2) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Green.prefab using Guid(79d7201dc6fc5b94f8c73bfb143b8af2) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0d94629a104f5b2c441ba6886d5e15de') in 0.037457 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_B.prefab
  artifactKey: Guid(34985651cffcb2f46a53e181d2ccd412) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Tent_B.prefab using Guid(34985651cffcb2f46a53e181d2ccd412) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7b0ba267babe8394b52a9688a62acc1a') in 0.0280854 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Chaff_A.fbx
  artifactKey: Guid(eb455d50b94499a468ed736b70328385) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Chaff_A.fbx using Guid(eb455d50b94499a468ed736b70328385) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '23cbdca7080d02df837ac626a1082308') in 0.0374593 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_A.prefab
  artifactKey: Guid(b6a724cda4ae81143ab9460744b26bdc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stool_A.prefab using Guid(b6a724cda4ae81143ab9460744b26bdc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'aceddae664b43aeb8cd0f4130a137126') in 0.0297989 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_E.prefab
  artifactKey: Guid(ed92e1eee8219e740a542cb16dc7bc6d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_E.prefab using Guid(ed92e1eee8219e740a542cb16dc7bc6d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '230ed1d1f895deadf3ad0a577c9a9659') in 0.0284491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_E.prefab
  artifactKey: Guid(11f68173a07e0dd47982eaf70d018c11) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_E.prefab using Guid(11f68173a07e0dd47982eaf70d018c11) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '70d49e5f8bad77ab9880820fa3ab13a2') in 0.0304145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_E.fbx
  artifactKey: Guid(8e092b0fd33fc894abaebc5ec270d471) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Grass_Tile_E.fbx using Guid(8e092b0fd33fc894abaebc5ec270d471) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8a1907d3624767a3dd4c27f94e52b71b') in 0.0461345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_D.prefab
  artifactKey: Guid(97e9ce4d06ef06b4c9156931b512d8ce) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_D.prefab using Guid(97e9ce4d06ef06b4c9156931b512d8ce) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9b2b9c00ade951e79cdc9075bbd3bd90') in 0.0671891 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_E.prefab
  artifactKey: Guid(36f8be23c78f8f44ab821740640e6f74) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_E.prefab using Guid(36f8be23c78f8f44ab821740640e6f74) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2fda7c09d42e52f40a67f80a41f6ab07') in 0.0264143 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Onion_Purple.fbx
  artifactKey: Guid(82aa7338af470fe4ba587795746142b3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Onion_Purple.fbx using Guid(82aa7338af470fe4ba587795746142b3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '62d24bf3a6acbac4880263a4df38554a') in 0.0440023 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_Open_B.fbx
  artifactKey: Guid(da4326ab69c498444ad0492750c122e0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_Open_B.fbx using Guid(da4326ab69c498444ad0492750c122e0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '89c1c2bcd7a0d97f3aa8ec2aa76ff421') in 0.0369659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Mushroom_Yellow.fbx
  artifactKey: Guid(154a68cdb1bd6434695102c7fc25d829) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Mushroom_Yellow.fbx using Guid(154a68cdb1bd6434695102c7fc25d829) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '067a1e890e18958f10684d1fc0a359ad') in 0.0475711 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Anvil_A.fbx
  artifactKey: Guid(cbe3deee340c05248a61cdc095a4a6e1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Anvil_A.fbx using Guid(cbe3deee340c05248a61cdc095a4a6e1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'baf2607ef8c5f1a467ab6d2c46a31cf4') in 0.0389123 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_F.prefab
  artifactKey: Guid(391da3cf860ca2c4183f95c343351198) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_F.prefab using Guid(391da3cf860ca2c4183f95c343351198) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2a6e91714de44a7322b6e51f3ab79eb5') in 0.0334496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Banana_Yellow.prefab
  artifactKey: Guid(9b3ff622e7d227f46983c68cdc2af006) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Banana_Yellow.prefab using Guid(9b3ff622e7d227f46983c68cdc2af006) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ade7beae0395f4eb14aecfa0576d5acb') in 0.0228026 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_F.fbx
  artifactKey: Guid(496700b5c4febb245805f1214ffe5700) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_F.fbx using Guid(496700b5c4febb245805f1214ffe5700) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '68b3cdb3ece7ac0f60f5dba3522a5231') in 0.0490356 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_O.fbx
  artifactKey: Guid(7141085593447674198babffdb1955a8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_O.fbx using Guid(7141085593447674198babffdb1955a8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0f3fb38765703c22f07e098ef45c6591') in 0.0406539 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodB_C.fbx
  artifactKey: Guid(63b59e28f933d714281e82561e74fe5a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodB_C.fbx using Guid(63b59e28f933d714281e82561e74fe5a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd89b7574a096c328601c3efad3127c3d') in 0.0372442 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Ax_I.fbx
  artifactKey: Guid(25fc1edb7d7ccac4ca436d59a159684b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Ax_I.fbx using Guid(25fc1edb7d7ccac4ca436d59a159684b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '096d5453b51e8155ccab4a7278b4bab4') in 0.0390316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_C.prefab
  artifactKey: Guid(882a0564b335ab849950d7d937fa3978) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Steel_C.prefab using Guid(882a0564b335ab849950d7d937fa3978) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '518cf9dc8de9141f2507fa7dab9d4747') in 0.0435751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_B.prefab
  artifactKey: Guid(a9db87a94740b7d48a44dd14c39b1935) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_B.prefab using Guid(a9db87a94740b7d48a44dd14c39b1935) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4b67ab8850efbbe721bc492cd96c5d20') in 0.0256933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_J.fbx
  artifactKey: Guid(5e0ca3c193352b64ab7dde0dd6da1ff4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Roof_J.fbx using Guid(5e0ca3c193352b64ab7dde0dd6da1ff4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '5ea8f4fbf6a8c8807cab468f5ca76a19') in 0.0383729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_B.prefab
  artifactKey: Guid(3498abc376298eb429918e2768d5810e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_B.prefab using Guid(3498abc376298eb429918e2768d5810e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '60fc12c28e306f904039d4154371b8d9') in 0.0354864 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_A.prefab
  artifactKey: Guid(a0c7f93a54eac7b4196d15eae88a5ebf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_A.prefab using Guid(a0c7f93a54eac7b4196d15eae88a5ebf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fe2f6e7d7e172f3870d0c7bb3bda11e5') in 0.0293255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_I.fbx
  artifactKey: Guid(d0d6ad994b02e3a459663f108c47b5ee) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_I.fbx using Guid(d0d6ad994b02e3a459663f108c47b5ee) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fbd01e0cc61c58eab7ffa3400ae24840') in 0.0393364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_I.prefab
  artifactKey: Guid(75d9c3bad581ad54e86670c606cb1a05) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_I.prefab using Guid(75d9c3bad581ad54e86670c606cb1a05) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '41604c8db6d3a89f62a18b0f25b929ab') in 0.0273692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_D.prefab
  artifactKey: Guid(a357d4ba0211848439fb35f891f2882f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Sword_D.prefab using Guid(a357d4ba0211848439fb35f891f2882f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3ba97362f4f8b3a021958c875e1f255d') in 0.0351321 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bucket_Wood_B.fbx
  artifactKey: Guid(14822991e172f6f44801f7ae41329492) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bucket_Wood_B.fbx using Guid(14822991e172f6f44801f7ae41329492) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9fe10c572800bf67fbf0c09eda8a4416') in 0.0369053 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/GunnyBag_Brown.fbx
  artifactKey: Guid(8aaa42cae6504d24f85f9a03af681b8f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/GunnyBag_Brown.fbx using Guid(8aaa42cae6504d24f85f9a03af681b8f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2c50d5bd421b6de62a8e86c97864a765') in 0.0369461 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs
  artifactKey: Guid(64235871605e58b4382f803f4edc867f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs using Guid(64235871605e58b4382f803f4edc867f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2146a0ad84d805fdba6e19c130e32dd5') in 0.0160335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_G.prefab
  artifactKey: Guid(85630d828ece6e643b71a2eadbb75683) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_G.prefab using Guid(85630d828ece6e643b71a2eadbb75683) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4faa8872cf0e3b4d6006582729be5f66') in 0.0334847 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Signpost.fbx
  artifactKey: Guid(46a009dba6cc43246acb3aaadf112583) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Signpost.fbx using Guid(46a009dba6cc43246acb3aaadf112583) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '08453a177f3bcf72b67f9ec39067a06f') in 0.0389659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_B.prefab
  artifactKey: Guid(0b9ea1e0d4308df429857cf58e6a6ec3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_B.prefab using Guid(0b9ea1e0d4308df429857cf58e6a6ec3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7036f4d937ce721cbb2362f59ece55b6') in 0.0281681 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_J.prefab
  artifactKey: Guid(d4e4057c0f99ef74ea3cb7c006ec2eae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_J.prefab using Guid(d4e4057c0f99ef74ea3cb7c006ec2eae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '77571ffef209e3f4cff90dbdd11bf86a') in 0.0329265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Part_Steel_A.fbx
  artifactKey: Guid(294ac78d0c5a3924698e0e122239be6b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Scaffold_Part_Steel_A.fbx using Guid(294ac78d0c5a3924698e0e122239be6b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6c22666be045a3d5c16345ccc3a90d80') in 0.032939 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Metalness.mat
  artifactKey: Guid(727c918c19e8dac4a998f99e90f12ef5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Metalness.mat using Guid(727c918c19e8dac4a998f99e90f12ef5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7a570c7012fa46dfe87d164c58957ce6') in 0.0693175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_A.fbx
  artifactKey: Guid(12857662aac4d5241bb93a0e51fb5642) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_A.fbx using Guid(12857662aac4d5241bb93a0e51fb5642) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f865084ea4adf7c3b962a8675d469d7d') in 0.0404046 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_C.prefab
  artifactKey: Guid(e2727f92b0de8474c9524324042c802c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bucket_Wood_C.prefab using Guid(e2727f92b0de8474c9524324042c802c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '6cb037fb01c42b183ef25dfb3295fcb5') in 0.0252917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Stool_A.fbx
  artifactKey: Guid(73199cbb5c5945146a3d1bd23b8c8bef) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Stool_A.fbx using Guid(73199cbb5c5945146a3d1bd23b8c8bef) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a56aee5efba90d1a255fec8b53f7448c') in 0.0395003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Materials/Transparent.mat
  artifactKey: Guid(a5278495aa8cc2845922ff6f38ec2403) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Materials/Transparent.mat using Guid(a5278495aa8cc2845922ff6f38ec2403) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2ca3ea4a2540d38a92533e9505b5b02c') in 0.0831573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bowl_Yellow.fbx
  artifactKey: Guid(4693541d7679422469467c98b58fef29) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bowl_Yellow.fbx using Guid(4693541d7679422469467c98b58fef29) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '601aa9e4fa99c77fd586cfb471d61dbd') in 0.0454574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000075 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_K.prefab
  artifactKey: Guid(6fc4f53d9b505ee49b1a4d2f02dea5cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_K.prefab using Guid(6fc4f53d9b505ee49b1a4d2f02dea5cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b8df66d2a30bdf72f9eb6b345463b249') in 0.0329179 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_H.fbx
  artifactKey: Guid(98141637132c17d469a00e54af81e6cf) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_H.fbx using Guid(98141637132c17d469a00e54af81e6cf) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f938e61d85db7b8c336633166a26dd5c') in 0.0394312 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_G.prefab
  artifactKey: Guid(4b9d7b29dff011747acef1149999995e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_G.prefab using Guid(4b9d7b29dff011747acef1149999995e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3279aeed96ed02c0f0f3f5b47e5baaed') in 0.0265305 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_H.fbx
  artifactKey: Guid(763d67caf583c8547a0122dfc7d1531a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_H.fbx using Guid(763d67caf583c8547a0122dfc7d1531a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c9bbe0751512e9359fcd5e5d1f474b53') in 0.0387279 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_E.prefab
  artifactKey: Guid(cc29fd2aa75e4564db8601461e9f97c3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_E.prefab using Guid(cc29fd2aa75e4564db8601461e9f97c3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c15474ed380f79f27021641653351a0b') in 0.031634 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_H.prefab
  artifactKey: Guid(62e6834c0be2815498d662814fdb993b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_H.prefab using Guid(62e6834c0be2815498d662814fdb993b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '346e5773bbab1b29d75f24c4cc8d8f66') in 0.0440242 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Script/Economy/GameDataManager.cs
  artifactKey: Guid(664cb11453b04fa4b9bcf24ab04ee821) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Economy/GameDataManager.cs using Guid(664cb11453b04fa4b9bcf24ab04ee821) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b96d911971d48397d8caff9b316b9d96') in 0.0394547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_H.prefab
  artifactKey: Guid(50b15f828f63f5b479112845392a3d06) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_H.prefab using Guid(50b15f828f63f5b479112845392a3d06) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '76a3584e272be9bd663575366de4f284') in 0.0527114 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_B.prefab
  artifactKey: Guid(a3610280da04ff242af3d2fb02dab358) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wood_B.prefab using Guid(a3610280da04ff242af3d2fb02dab358) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be36e42a5c8d6c356ccc3ff6cf79fed4') in 0.036477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_E.prefab
  artifactKey: Guid(eb50bee0027e4de4892b4bb47dbb9d55) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_E.prefab using Guid(eb50bee0027e4de4892b4bb47dbb9d55) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '643c95e611909a78d192d06a78897724') in 0.0355366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_A.fbx
  artifactKey: Guid(5cbe33336a5cff64585e0d7498796288) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_Window_A.fbx using Guid(5cbe33336a5cff64585e0d7498796288) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9f7a1d9afbacdce3812a02e44c41d593') in 0.0458771 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_E.prefab
  artifactKey: Guid(f0a8d1ebc1669e0449530d440ba4ffe5) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_E.prefab using Guid(f0a8d1ebc1669e0449530d440ba4ffe5) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '146b44b35362c5bf82b1d898b6a0d31d') in 0.0407368 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Parchment_C.fbx
  artifactKey: Guid(c7637073894cb1f47a9b3c5c26875419) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Parchment_C.fbx using Guid(c7637073894cb1f47a9b3c5c26875419) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '403fcc6905beb2cc9b10a87fdd72a0a0') in 0.0338183 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_F.fbx
  artifactKey: Guid(52316ac471cb4a8409f31848b3443e1c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_F.fbx using Guid(52316ac471cb4a8409f31848b3443e1c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0cbc0f483d5996f4b5d0223cd56eb175') in 0.057629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_J.prefab
  artifactKey: Guid(2bb46ceb0da71e140a33c1b47440c3cc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_J.prefab using Guid(2bb46ceb0da71e140a33c1b47440c3cc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75c71402b063632141a109a8cccfa60b') in 0.0344615 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_B.prefab
  artifactKey: Guid(efb4054f963fd4046aa1edc91917ce48) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Table_B.prefab using Guid(efb4054f963fd4046aa1edc91917ce48) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6de2750a782872463b1d0471783768a9') in 0.0310392 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_A.prefab
  artifactKey: Guid(a707c8688dc9e3a4d8db588fb90786a0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_A.prefab using Guid(a707c8688dc9e3a4d8db588fb90786a0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e759428c600eaf94e354933b12fdc27e') in 0.0264132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Cabinet_A.fbx
  artifactKey: Guid(120df632cb651ce41bb8bcf9138d4833) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_Cabinet_A.fbx using Guid(120df632cb651ce41bb8bcf9138d4833) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '808b0a59cc58c0da9350aeb6387700e3') in 0.036749 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_C.fbx
  artifactKey: Guid(8e36c54aef247c6428cee38da9428f90) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_C.fbx using Guid(8e36c54aef247c6428cee38da9428f90) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '6088778adbdaeee666d49e2647274631') in 0.0510916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/CandelStick_B.fbx
  artifactKey: Guid(6885e7b4089ddf94aaec7c121545c964) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/CandelStick_B.fbx using Guid(6885e7b4089ddf94aaec7c121545c964) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2340765f8f999270c39d6474c295dc3c') in 0.0477492 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Yellow.fbx
  artifactKey: Guid(8628c50b7fbe4254682070655214dde1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Pot_Yellow.fbx using Guid(8628c50b7fbe4254682070655214dde1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ff18cfc027ba2e47a66a7df84604d9cc') in 0.0397827 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_C.prefab
  artifactKey: Guid(632cfdd3b0364524a87f33d3943fc1e4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Table_C.prefab using Guid(632cfdd3b0364524a87f33d3943fc1e4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'cf8167d523aac59963bca265e2ff0ddf') in 0.0340146 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_A.prefab
  artifactKey: Guid(b7a727d7207cef541b7d73ab711f79ce) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_A.prefab using Guid(b7a727d7207cef541b7d73ab711f79ce) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c325d67edbdcfed028af643382e57a6e') in 0.0265468 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_I.fbx
  artifactKey: Guid(d4b86ed0564814048807caebc1a461c0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodC_I.fbx using Guid(d4b86ed0564814048807caebc1a461c0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7fcf2b534392f4e90484468213f16c33') in 0.0428634 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPen_B.prefab
  artifactKey: Guid(7348e162085e3d44d8a00bd73e022429) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPen_B.prefab using Guid(7348e162085e3d44d8a00bd73e022429) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '39ae20b8e8bceecb924c8f3f2c8b33c7') in 0.0313387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_B.prefab
  artifactKey: Guid(6105094c528af8342b4192e8194e14ff) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_1Floor_Side_B.prefab using Guid(6105094c528af8342b4192e8194e14ff) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '2b139b24c9a8447ff0de9de8699dd0fa') in 0.0276665 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Green.prefab
  artifactKey: Guid(79d7201dc6fc5b94f8c73bfb143b8af2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Green.prefab using Guid(79d7201dc6fc5b94f8c73bfb143b8af2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '42df70de84a8c148816e1b2dc7db738a') in 0.0235917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

Editor requested this worker to shutdown with reason: balancing worker count downwards
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0