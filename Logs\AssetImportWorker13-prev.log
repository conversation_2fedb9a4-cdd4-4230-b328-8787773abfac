Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 24260 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-06-12T10:30:14Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker13
-projectPath
E:/2025/Boder/My boder 3
-logFile
Logs/AssetImportWorker13.log
-srvPort
59068
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/2025/Boder/My boder 3
E:/2025/Boder/My boder 3
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [16140]  Target information:

Player connection [16140]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3893309924 [EditorId] 3893309924 [Version] 1048832 [Id] WindowsEditor(7,LAPTOP-S1448K3N) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [16140] Host joined multi-casting on [***********:54997]...
Player connection [16140] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path E:/Unity/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/2025/Boder/My boder 3/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4050 Laptop GPU (ID=0x28e1)
    Vendor:   NVIDIA
    VRAM:     5921 MB
    Driver:   32.0.15.6649
Initialize mono
Mono path[0] = 'E:/Unity/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56080
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.003541 seconds.
- Loaded All Assemblies, in  1.219 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.017 seconds
Domain Reload Profiling: 2236ms
	BeginReloadAssembly (524ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (98ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (102ms)
	LoadAllAssembliesAndSetupDomain (460ms)
		LoadAssemblies (521ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (454ms)
			TypeCache.Refresh (452ms)
				TypeCache.ScanAssembly (414ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1019ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (920ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (42ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (318ms)
			ProcessInitializeOnLoadAttributes (444ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.292 seconds
Refreshing native plugins compatible for Editor in 1.11 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.591 seconds
Domain Reload Profiling: 3881ms
	BeginReloadAssembly (353ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (86ms)
	LoadAllAssembliesAndSetupDomain (1756ms)
		LoadAssemblies (1108ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (811ms)
			TypeCache.Refresh (686ms)
				TypeCache.ScanAssembly (649ms)
			BuildScriptInfoCaches (95ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (1592ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1135ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (218ms)
			ProcessInitializeOnLoadAttributes (789ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.13 seconds
Refreshing native plugins compatible for Editor in 1.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 211 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6635 unused Assets / (10.6 MB). Loaded Objects now: 7294.
Memory consumption went from 163.3 MB to 152.7 MB.
Total: 224.330100 ms (FindLiveObjects: 8.565100 ms CreateObjectMapping: 9.542000 ms MarkObjects: 125.389500 ms  DeleteObjects: 80.831300 ms)

========================================================================
Received Import Request.
  Time since last request: 103330.932811 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_B.prefab
  artifactKey: Guid(d830cdd1bf6fb374ba4b7c4bb0a293a6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bread_B.prefab using Guid(d830cdd1bf6fb374ba4b7c4bb0a293a6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '02a81d76c3478ab99a1bb92ecae002ce') in 1.2939289 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Yellow.prefab
  artifactKey: Guid(5026a8af21b42a144a753025fba4863f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Apple_Yellow.prefab using Guid(5026a8af21b42a144a753025fba4863f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '18fe794d5d02b3e3d2f4b8ab66069684') in 0.1748374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_G.prefab
  artifactKey: Guid(80ff76b4d95510f40b3e8c61a69eb0b8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_G.prefab using Guid(80ff76b4d95510f40b3e8c61a69eb0b8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'bd397600284358decc7320f64a0cf403') in 0.0253862 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Orange.prefab
  artifactKey: Guid(f35893b1706e32448b4301a2d97a0aae) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bowl_Orange.prefab using Guid(f35893b1706e32448b4301a2d97a0aae) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '0e75891645504db096e328ec4b39bbb9') in 0.0201106 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Candelabrum_A.prefab
  artifactKey: Guid(12adfc3cf6795f54d91ba3c2445a89e5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Candelabrum_A.prefab using Guid(12adfc3cf6795f54d91ba3c2445a89e5) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'f13c04c7ffabdc28f7cc51f018203f42') in 0.0237103 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Potato.prefab
  artifactKey: Guid(863a4e4589f34c648a3073540ce2d9be) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Potato.prefab using Guid(863a4e4589f34c648a3073540ce2d9be) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '834010b78f64165cadf92ef291e01320') in 0.028561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_C.fbx
  artifactKey: Guid(5e9e8c9d44075da419bdbc0beb105a57) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bottle_C.fbx using Guid(5e9e8c9d44075da419bdbc0beb105a57) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '312a6328c8cd4f7e650da86561e42711') in 0.0771123 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_A.prefab
  artifactKey: Guid(1b2263e3fee7832408b6139c673cfab7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_A.prefab using Guid(1b2263e3fee7832408b6139c673cfab7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ee3ff6936aaf381a2e4232f840f7c0ec') in 0.0213104 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_B.prefab
  artifactKey: Guid(61ed72b22a5b0564ead6abdb6c0f369c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_B.prefab using Guid(61ed72b22a5b0564ead6abdb6c0f369c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd5c0b08c04e6691175350e0361962a3f') in 0.0199533 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_C.prefab
  artifactKey: Guid(d884613eae895a944857b11f41324b52) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_C.prefab using Guid(d884613eae895a944857b11f41324b52) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3a7066082f863bea62bd064758b29ee3') in 0.0262424 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_G.prefab
  artifactKey: Guid(94fec1719e190e343be766600ee0ecea) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_G.prefab using Guid(94fec1719e190e343be766600ee0ecea) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd559f9cd319ac1ffd155c9902526e493') in 0.0205292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(99c9720ab356a0642a771bea13969a05) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Scenes/SampleScene.unity using Guid(99c9720ab356a0642a771bea13969a05) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ef8290f9d3e03aced3e1900836684144') in 0.0227767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Foxtail.prefab
  artifactKey: Guid(49bc0d165a5e54e4aaf109e6d334b20a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Foxtail.prefab using Guid(49bc0d165a5e54e4aaf109e6d334b20a) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5bdacfdd5a87e89296526cb074d35a1a') in 0.0312497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_C.prefab
  artifactKey: Guid(2f95738b352668a40ab4da2ee5fdf9ed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ground_Tile_C.prefab using Guid(2f95738b352668a40ab4da2ee5fdf9ed) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'a7c1d53295cb04e8328cf6f6724cce2a') in 0.0247292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tree_B.fbx
  artifactKey: Guid(d5c928a4801489f49b52cea0aa42deb8) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tree_B.fbx using Guid(d5c928a4801489f49b52cea0aa42deb8) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '73d2c3916eb70778f91f3ed965fb4da7') in 0.0478168 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_D.prefab
  artifactKey: Guid(6a22363de62f0d14a9b93bc67e14bb79) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_Rock_D.prefab using Guid(6a22363de62f0d14a9b93bc67e14bb79) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'e4b9ac9b2bb0bf6cc1a7bb5870f41435') in 0.0195105 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_D.prefab
  artifactKey: Guid(d909c1a2efcb72d4d87b3c9daf5c3f7d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_D.prefab using Guid(d909c1a2efcb72d4d87b3c9daf5c3f7d) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'c87cd028d23febf3801c4d6c1a99f667') in 0.0224496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_A.prefab
  artifactKey: Guid(dd926e78e97c65648bae52eb65497852) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_A.prefab using Guid(dd926e78e97c65648bae52eb65497852) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a4bf0006bb99b244f5ffa1d12d2d3ef7') in 0.0329793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_I.prefab
  artifactKey: Guid(5d8fa9b39c257df42825278077227c7c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_I.prefab using Guid(5d8fa9b39c257df42825278077227c7c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '597153ed94b66142b537a06b5a823b1f') in 0.0278202 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_F.prefab
  artifactKey: Guid(5b5048f232d5af6458fecb05c2d5f6aa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_F.prefab using Guid(5b5048f232d5af6458fecb05c2d5f6aa) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'eea1c25e50f394619774ffd5056e6001') in 0.0256099 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_C.prefab
  artifactKey: Guid(a98411c3a1e8f34448aad79e6dea5672) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_C.prefab using Guid(a98411c3a1e8f34448aad79e6dea5672) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a6e1777949d8a10c80bd1b4c08e4cd68') in 0.0290877 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_C.prefab
  artifactKey: Guid(1e0d4844a1a345543afac4d0fa3f41f3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cook_C.prefab using Guid(1e0d4844a1a345543afac4d0fa3f41f3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '8bcdbeaff95ac7728b41c1d7d80e9979') in 0.0289674 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_E.prefab
  artifactKey: Guid(7adbf7fee6484834db99675feadc4c47) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_E.prefab using Guid(7adbf7fee6484834db99675feadc4c47) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b898a42008303b738e1ec305b747a005') in 0.0282701 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000114 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_C.prefab
  artifactKey: Guid(c76dece1fb8d33c4ab80e35943b7d7d9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_C.prefab using Guid(c76dece1fb8d33c4ab80e35943b7d7d9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5495394e1366a72d427f7ea5af812a87') in 0.0268054 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_K.prefab
  artifactKey: Guid(c7b59dda060e2bb4c90ae4cd61e938d6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Roof_K.prefab using Guid(c7b59dda060e2bb4c90ae4cd61e938d6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'efa3b5f8ef067a250b79cef27f6906cd') in 0.02587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_F.prefab
  artifactKey: Guid(391da3cf860ca2c4183f95c343351198) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_F.prefab using Guid(391da3cf860ca2c4183f95c343351198) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7428686a41369beabddc4424d56a870f') in 0.0230453 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_I.prefab
  artifactKey: Guid(c107862298ce55f49b5a42cc0ec95c40) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_I.prefab using Guid(c107862298ce55f49b5a42cc0ec95c40) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '5364d0a81535df99bda6ed69a3b749a6') in 0.0241689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Black.fbx
  artifactKey: Guid(abe526ed2a250e248954624b2f638358) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Black.fbx using Guid(abe526ed2a250e248954624b2f638358) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a2a1d68dae7e599e42f57c22aaf6f073') in 0.0450269 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Chisel.prefab
  artifactKey: Guid(52f88ce38e88f9a49a85725cfd817733) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Chisel.prefab using Guid(52f88ce38e88f9a49a85725cfd817733) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '02ec6e4158b71820a382068ef0c0d2af') in 0.0295191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_D.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_D.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '848b5557307b6150099fb16dd0650ce4') in 0.0323801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_E.prefab
  artifactKey: Guid(de27c75054ff3124999b0f9632537d15) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_E.prefab using Guid(de27c75054ff3124999b0f9632537d15) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '50e71d251f0b48ed920b5e81610b59e8') in 0.0516703 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_D.prefab
  artifactKey: Guid(eabd4d6fda3311f4b916fb6784dfaec8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_D.prefab using Guid(eabd4d6fda3311f4b916fb6784dfaec8) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '120d038b061282ceb0c42fcdca8fb320') in 0.0354736 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_B.prefab
  artifactKey: Guid(7a082443cb4e055448db40caccc0151c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_B.prefab using Guid(7a082443cb4e055448db40caccc0151c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '665b408362165d2edf72a0cfc0124c06') in 0.0221049 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_D.prefab
  artifactKey: Guid(76fd593f03898184d97c39c9f8021bc2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_D.prefab using Guid(76fd593f03898184d97c39c9f8021bc2) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '375ec9c61c7aa6ea904ea5a17ab63bbb') in 0.0208486 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_H.prefab
  artifactKey: Guid(c6ea47c7ffb4a29418846dd981c4cfa7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_2Floor_Side_H.prefab using Guid(c6ea47c7ffb4a29418846dd981c4cfa7) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'ad7233891935f5551bdcf117686d2ef3') in 0.0282014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_D.prefab
  artifactKey: Guid(958246969e6df094daa9f4048dcf3adb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodB_D.prefab using Guid(958246969e6df094daa9f4048dcf3adb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '4d229ad371c5ef75d7555fde9985c26e') in 0.0332175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Cook_B.fbx
  artifactKey: Guid(dbcc2cd8880a0cc4f838416288f6822c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Cook_B.fbx using Guid(dbcc2cd8880a0cc4f838416288f6822c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '85d94bca9dbdda61722b3dcce076624c') in 0.0358337 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Ladder.prefab
  artifactKey: Guid(733c1c3299e20b140a05befdccea196b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Scaffold_Part_Ladder.prefab using Guid(733c1c3299e20b140a05befdccea196b) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '942874870fe47e1d3de1774a70a9575e') in 0.0343087 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_B.prefab
  artifactKey: Guid(2e25825c8c834b746bcce755381fcfe0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_B.prefab using Guid(2e25825c8c834b746bcce755381fcfe0) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '1dcba14808f373ade9af089f3d10cdb4') in 0.0304246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000302 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Apple_Red.fbx
  artifactKey: Guid(fa2a5e1bfb8a9e64da938db58df565ec) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Apple_Red.fbx using Guid(fa2a5e1bfb8a9e64da938db58df565ec) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7bad8adda60f4011fac45635c9f72ca5') in 0.0537815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_D.prefab
  artifactKey: Guid(b94d6cd142f67804dbc8c88c21c9acbe) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Stone_D.prefab using Guid(b94d6cd142f67804dbc8c88c21c9acbe) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b5185469ba4380a9de89e5d793894cff') in 0.0247643 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_B.prefab
  artifactKey: Guid(9530b3b472e48284ca2beab46715a349) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pot_Water_B.prefab using Guid(9530b3b472e48284ca2beab46715a349) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '8f463a12edb6ac3176b3996623a67ff0') in 0.0288324 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_B.fbx
  artifactKey: Guid(c404de1f584c68a4a8deae80f11c813b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Fence_WoodA_B.fbx using Guid(c404de1f584c68a4a8deae80f11c813b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'bbb16c66a1f172fd7c1167c389e4bc05') in 0.0489524 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Carrier_Wood.fbx
  artifactKey: Guid(be95ee955df0f2a44b14215af2666a52) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Carrier_Wood.fbx using Guid(be95ee955df0f2a44b14215af2666a52) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '009464dc8ada8952977d2f104a15015f') in 0.0578804 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_E.prefab
  artifactKey: Guid(11f68173a07e0dd47982eaf70d018c11) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_E.prefab using Guid(11f68173a07e0dd47982eaf70d018c11) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7368a48c54dd4f95d3fd43838b2e3449') in 0.0432635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Well.fbx
  artifactKey: Guid(aed2a706c4ea96245a93ce1fafcbc609) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Well.fbx using Guid(aed2a706c4ea96245a93ce1fafcbc609) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '688e0a8b5d93b0285e5b7636e9059076') in 0.0729609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPenInk_B.prefab
  artifactKey: Guid(29e5b75180a515a458237af3eb969094) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPenInk_B.prefab using Guid(29e5b75180a515a458237af3eb969094) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9e901d4e01490e2ca5ba95ba3e377db8') in 0.0318828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_B.prefab
  artifactKey: Guid(0aee6e4d8ad1c9e45a652d62a103dd91) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_B.prefab using Guid(0aee6e4d8ad1c9e45a652d62a103dd91) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ce6f63661248739bc8b8223cb4b8df04') in 0.0454072 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_E.prefab
  artifactKey: Guid(16bb755f7c294ef45a12beffe1040d07) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_E.prefab using Guid(16bb755f7c294ef45a12beffe1040d07) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f606a00b34293ac8569107213e7f9fd2') in 0.0421933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack
  artifactKey: Guid(520ff1aebcc89404cb236d2b863141e9) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack using Guid(520ff1aebcc89404cb236d2b863141e9) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0b76149134d029106cd6aeb30bed2665') in 0.0222189 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_F.prefab
  artifactKey: Guid(594478582566f234e89959fcfd3f2b51) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_F.prefab using Guid(594478582566f234e89959fcfd3f2b51) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '642c203487906d7775e1dde91b492e44') in 0.0234014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Egg_B.fbx
  artifactKey: Guid(e863b96c2b8faa049959476f8d9f0d60) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Egg_B.fbx using Guid(e863b96c2b8faa049959476f8d9f0d60) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7691fa23cdf73b7c3301b2c1acc64988') in 0.0467367 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_A.prefab
  artifactKey: Guid(1087e272b56684a49b1c245d1af9d4a9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Weapon_A.prefab using Guid(1087e272b56684a49b1c245d1af9d4a9) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'b3f6244c8d75a16732f79d95464a501f') in 0.0250409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_I.prefab
  artifactKey: Guid(f8c590ec91412624ba7f6759e70d6884) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_I.prefab using Guid(f8c590ec91412624ba7f6759e70d6884) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '4c296b09888de5349237a29aa3a96186') in 0.0248553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_B.fbx
  artifactKey: Guid(aaad7033221d36f418871000254bd58b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_2Floor_Side_B.fbx using Guid(aaad7033221d36f418871000254bd58b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '92477dad9b7e20dbeeea3fb1ef8ec7a6') in 0.0428091 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bowl_Brown.fbx
  artifactKey: Guid(a2c280ee42fa0374c8f1442809da55ae) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bowl_Brown.fbx using Guid(a2c280ee42fa0374c8f1442809da55ae) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1b4016df868fd5d3166ce6f0fe364573') in 0.0394042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_E.prefab
  artifactKey: Guid(0808151e53985f041b031a43691eb13d) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Lamp_Road_E.prefab using Guid(0808151e53985f041b031a43691eb13d) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '588a98de8dc8ddd92ad9c729b698a922') in 0.0328767 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_C.fbx
  artifactKey: Guid(d226b9fd0c653114094910ebc5c43457) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_C.fbx using Guid(d226b9fd0c653114094910ebc5c43457) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '837787bbb57d0a48101ffd2e3ce6a77a') in 0.0380256 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_D.fbx
  artifactKey: Guid(1b15be082851267449d80f366f715b0f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_D.fbx using Guid(1b15be082851267449d80f366f715b0f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '37b59536240a14fe7b5118328c4ad191') in 0.0360815 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_F.prefab
  artifactKey: Guid(a28448a815ded8e458b13eb20a6943fe) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/BlacksmithTool_F.prefab using Guid(a28448a815ded8e458b13eb20a6943fe) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd7be04c05d8501af7cebb4ebd0e96459') in 0.0309932 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Script/Player/Guides/HUONG_DAN_CHI_TIET.md
  artifactKey: Guid(e2a2fd41f7647454b84b510403cd140e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/HUONG_DAN_CHI_TIET.md using Guid(e2a2fd41f7647454b84b510403cd140e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dce3ef134de9de572eb64bd0f79fdb2b') in 0.0383659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_B.prefab
  artifactKey: Guid(2e7cb8d40151bb849b4d5d35bcd61034) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bench_B.prefab using Guid(2e7cb8d40151bb849b4d5d35bcd61034) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '7863a613e002310de93d8a2a32202149') in 0.0248411 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/SiegeTower_A.fbx
  artifactKey: Guid(f79c1c3e97a87504bad9667e83629774) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/SiegeTower_A.fbx using Guid(f79c1c3e97a87504bad9667e83629774) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'eebb69d4f018ad5d2b94da34730a2c30') in 0.0388633 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cup_B.prefab
  artifactKey: Guid(8775ca8174311be46876c299f133b301) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cup_B.prefab using Guid(8775ca8174311be46876c299f133b301) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de4d29ebacb49b1de78bc4632664b39d') in 0.0506761 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_L.prefab
  artifactKey: Guid(79ca67310ac86c244bd6fd275c182391) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bottle_L.prefab using Guid(79ca67310ac86c244bd6fd275c182391) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '2e7897ca48c6ee734121f9537e4992a2') in 0.0440063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_C.prefab
  artifactKey: Guid(654b16543fc7e304980b8fda8c8b5a4c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Torch_Steel_C.prefab using Guid(654b16543fc7e304980b8fda8c8b5a4c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '93c2cbd46d1d19aefe59f0dec07debce') in 0.0458581 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_F.prefab
  artifactKey: Guid(e7a15ec4b35b14e4a926cb52efc116d6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_F.prefab using Guid(e7a15ec4b35b14e4a926cb52efc116d6) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'd730d60236cada4ef5ccd2dbf40b16e4') in 0.0234025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_D.prefab
  artifactKey: Guid(deb8d52904aec8a4586e8f6ee9e0b7cb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_D.prefab using Guid(deb8d52904aec8a4586e8f6ee9e0b7cb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a4492c991bee7efaddebbe0d0f839e32') in 0.028294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_E.fbx
  artifactKey: Guid(0868f9c1255c91246986f8deb2a19792) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Lamp_Road_E.fbx using Guid(0868f9c1255c91246986f8deb2a19792) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'a4539b71ec76e897b2f50622a14da3c9') in 0.0381247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_I.fbx
  artifactKey: Guid(9134aaf3a26ec244180e039a57853599) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_I.fbx using Guid(9134aaf3a26ec244180e039a57853599) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dd659a19b48eca865b735bf43c81423e') in 0.0390833 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pear_White.prefab
  artifactKey: Guid(5218ef9d9c7e1ed4ab97a52a21aac3be) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pear_White.prefab using Guid(5218ef9d9c7e1ed4ab97a52a21aac3be) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '38ac285966c210039820a6f27947079e') in 0.0370687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Market_Sign_B.fbx
  artifactKey: Guid(122325214b14a244c9ae9d4daf4eb012) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Market_Sign_B.fbx using Guid(122325214b14a244c9ae9d4daf4eb012) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '92254ee7125ae7ac937b3135201afc46') in 0.0429131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_D.prefab
  artifactKey: Guid(25ab957f59d257047b16055027e6cf97) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_D.prefab using Guid(25ab957f59d257047b16055027e6cf97) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: 'dadfa2feb4f004674962cb24bce6d4ff') in 0.0252526 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_A.prefab
  artifactKey: Guid(2e624c421e8bef647afa84da55c6772e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Flower_A.prefab using Guid(2e624c421e8bef647afa84da55c6772e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ad040061452c7fc160ace2365df2451e') in 0.0285565 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Potato.fbx
  artifactKey: Guid(5fc001bf6d7670846a1e9b7cac442d09) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bundle_Potato.fbx using Guid(5fc001bf6d7670846a1e9b7cac442d09) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'dc1d822495c9d5f5aa8078ef3d5e948b') in 0.0369562 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_C.fbx
  artifactKey: Guid(877756408eb5a7d4c96e4d502dec1af4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_C.fbx using Guid(877756408eb5a7d4c96e4d502dec1af4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '834e62ef51af72beeab59193115c86b5') in 0.0367171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_D.prefab
  artifactKey: Guid(8cdf31b700b475b41baf731edc60942f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Market_Sign_D.prefab using Guid(8cdf31b700b475b41baf731edc60942f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '716cbdf9b82a92812e734453560fa86b') in 0.0348378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Foxtail.prefab
  artifactKey: Guid(49bc0d165a5e54e4aaf109e6d334b20a) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Foxtail.prefab using Guid(49bc0d165a5e54e4aaf109e6d334b20a) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e75c6a1ad99d1057aa05aaa309b4602c') in 0.0318871 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_C.prefab
  artifactKey: Guid(cb6fd5d63c87d4144832f932d053dea4) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Hammer_C.prefab using Guid(cb6fd5d63c87d4144832f932d053dea4) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1eb0a9686c36f27237546038cd6c383c') in 0.0363137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Cheese_C.fbx
  artifactKey: Guid(5107de5e217b4a34b8c6dc6bd2a3f081) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Cheese_C.fbx using Guid(5107de5e217b4a34b8c6dc6bd2a3f081) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '47d3c833448f6696480040520922002e') in 0.0422686 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Paprika_Orange.fbx
  artifactKey: Guid(51332f4488683bb4d83ba36c499afd1c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Paprika_Orange.fbx using Guid(51332f4488683bb4d83ba36c499afd1c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd0407baf3bd43639f07e647df0a43878') in 0.0401436 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_Open_C.fbx
  artifactKey: Guid(14f490c15ca57f74e8c4926077b989d7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_Open_C.fbx using Guid(14f490c15ca57f74e8c4926077b989d7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3da879527e816efa38bf3b150025295c') in 0.0314126 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPenInk_A.prefab
  artifactKey: Guid(7900cf281ccfe9648a3dc235f84bc3df) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/FeatherQuillPenInk_A.prefab using Guid(7900cf281ccfe9648a3dc235f84bc3df) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '080240ebdaac599ce12a3f3541280135') in 0.0307919 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_F.prefab
  artifactKey: Guid(8f978d6fadfddcd44b4ca14ba638346b) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Rock_F.prefab using Guid(8f978d6fadfddcd44b4ca14ba638346b) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '34ea0462bc1ad28fe44c675cfd183362') in 0.033348 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tent_A.fbx
  artifactKey: Guid(b6a963c963a45d14abefbc3f5f2e4898) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tent_A.fbx using Guid(b6a963c963a45d14abefbc3f5f2e4898) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '88f5ea116ced7de3506944bf8950df6c') in 0.0432801 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Pear_Yellow.prefab
  artifactKey: Guid(7de01e3f92e2b1b4e9d570ec3d429449) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Pear_Yellow.prefab using Guid(7de01e3f92e2b1b4e9d570ec3d429449) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'b53c456d802886e4e77b5514a8bd6c66') in 0.0329383 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_C.prefab
  artifactKey: Guid(03db32261ed5fca4bab071a2e382ba2f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Cheese_C.prefab using Guid(03db32261ed5fca4bab071a2e382ba2f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd090d9d0852b34378d66b7e95f2faa9b') in 0.0296066 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_B.prefab
  artifactKey: Guid(80342a30dfd5de14599c5f32b069db43) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Grass_Tile_B.prefab using Guid(80342a30dfd5de14599c5f32b069db43) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '84db94f845a67169b058abcc142ac871') in 0.0339294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bench_C.fbx
  artifactKey: Guid(f53ec94bb131be0439d075cde55d7dbc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bench_C.fbx using Guid(f53ec94bb131be0439d075cde55d7dbc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'afd7a51e1cb0f7f9bf259f9ef5ffd308') in 0.0339421 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_C.prefab
  artifactKey: Guid(9deaa2f3df142c54f81826a5d1d43ad0) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_C.prefab using Guid(9deaa2f3df142c54f81826a5d1d43ad0) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd4db10b6d2729c96a1ef166e61f53346') in 0.0260374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Rock_B.fbx
  artifactKey: Guid(4250a45ef4a8e4e4ab1b41b18b279958) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Rock_B.fbx using Guid(4250a45ef4a8e4e4ab1b41b18b279958) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '81cc8455e7abbc096437c5b844948dbb') in 0.0357511 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Flower_A.fbx
  artifactKey: Guid(7f0ba52bfcd286b4ca6158cd4e5325ef) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Flower_A.fbx using Guid(7f0ba52bfcd286b4ca6158cd4e5325ef) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '44b556d171d00322a96fa0b09d7ca6b8') in 0.0424905 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_B.prefab
  artifactKey: Guid(c1929576f46dbc84d92418a9d2c1fac1) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Gate_B.prefab using Guid(c1929576f46dbc84d92418a9d2c1fac1) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'c174c1551712908898c3a57797646385') in 0.0359853 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Tower_Wood.fbx
  artifactKey: Guid(8b844a5ff924c674b966c77ccc1e9325) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Tower_Wood.fbx using Guid(8b844a5ff924c674b966c77ccc1e9325) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '29a658f19ab454cc8bca2602c1e1e61d') in 0.0360249 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Bed_B.fbx
  artifactKey: Guid(8a567be1ad688a34cb7c53f8ed511e8e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Bed_B.fbx using Guid(8a567be1ad688a34cb7c53f8ed511e8e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '1cd06d6ea06ffa696e66127ffb018fd7') in 0.0345777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_A.prefab
  artifactKey: Guid(e1b03fc33957bed4eb4c3618fc18af52) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Fence_WoodC_A.prefab using Guid(e1b03fc33957bed4eb4c3618fc18af52) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '04633fc5c212bc6c74d3d168c2f6b3a4') in 0.0473756 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_B.prefab
  artifactKey: Guid(795976c6200121c408074881b8d0d368) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/House_Window_B.prefab using Guid(795976c6200121c408074881b8d0d368) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '85f5eaecf77a73762a4b20a3ae256d10') in 0.0367523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000159 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Book_E.fbx
  artifactKey: Guid(919704a447d16e7479fa0016e37121dc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Book_E.fbx using Guid(919704a447d16e7479fa0016e37121dc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7d7ddb2eaac75e6ac2049774109ff602') in 0.043886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Potato.prefab
  artifactKey: Guid(863a4e4589f34c648a3073540ce2d9be) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Bundle_Potato.prefab using Guid(863a4e4589f34c648a3073540ce2d9be) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'e8503128fe03cc91e004d2969f10fdca') in 0.0382413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_G.prefab
  artifactKey: Guid(7357a6208e582cb4a93e9305c5d0c17c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Wagon_G.prefab using Guid(7357a6208e582cb4a93e9305c5d0c17c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '3b53887561869dcc0f74f376feb52186') in 0.0313719 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000101 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_White.prefab
  artifactKey: Guid(63e8317d1ef3a9e41b831ca2cc6e185f) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/GunnyBag_White.prefab using Guid(63e8317d1ef3a9e41b831ca2cc6e185f) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '75bb5e460c63cd6d8686f3fcd71e3026') in 0.0533303 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_G.prefab
  artifactKey: Guid(d28c597ec1d67a947bfb454cce4cc4cd) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/WeaponRack_G.prefab using Guid(d28c597ec1d67a947bfb454cce4cc4cd) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'ec9bfe3748eee3ced410fcf799f75940') in 0.0319226 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Torch_Steel_A.fbx
  artifactKey: Guid(a83ac63198ba8164aaa98ae8a59fa719) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Torch_Steel_A.fbx using Guid(a83ac63198ba8164aaa98ae8a59fa719) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'f1a6dfbe0d747ffb9222399f4c4f5bf8') in 0.0413171 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_B.prefab
  artifactKey: Guid(61ed72b22a5b0564ead6abdb6c0f369c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Barrel_B.prefab using Guid(61ed72b22a5b0564ead6abdb6c0f369c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '25d4ba98e9620285f2890c40d16a896c') in 0.0351052 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Rock_C.fbx
  artifactKey: Guid(ecb4b00d1e96efd4a967fc09e9b07735) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Rock_C.fbx using Guid(ecb4b00d1e96efd4a967fc09e9b07735) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '711aaffdb94f40c0a4861ae7be6f8f38') in 0.0384904 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_E.prefab
  artifactKey: Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Box_Wood_E.prefab using Guid(********************************) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'be8f2ed97f052b25d688e0f9e01a28de') in 0.03064 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_B.prefab
  artifactKey: Guid(7a082443cb4e055448db40caccc0151c) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Mountain_B.prefab using Guid(7a082443cb4e055448db40caccc0151c) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'fddac90bd5ba3e4bbc6c1024bc5d2e88') in 0.0299222 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_C.fbx
  artifactKey: Guid(81a29510f79a00c40a25d8f5901ccc3e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/House_1Floor_Side_C.fbx using Guid(81a29510f79a00c40a25d8f5901ccc3e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '415a0b8053797511ec9354f7b9e6c651') in 0.0368127 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_K.fbx
  artifactKey: Guid(37a7d0505fdea2c479100502319db8cb) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Weapon_K.fbx using Guid(37a7d0505fdea2c479100502319db8cb) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '24d9c3c2e54651ba1d41bfb53fa838a9') in 0.044403 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_K.fbx
  artifactKey: Guid(2baa6f2525aebf8459e4ba9edbf7b97e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/FBX/Barrel_K.fbx using Guid(2baa6f2525aebf8459e4ba9edbf7b97e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'd06c6837c4b69d632f914ba6d69ebc8f') in 0.0379694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_C.prefab
  artifactKey: Guid(120eada2927331f4991061bbdc16b876) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Book_C.prefab using Guid(120eada2927331f4991061bbdc16b876) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '22a7bcce3272349e6fae9dab1f5847f6') in 0.021345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Texture
  artifactKey: Guid(544f1993571def64e9f8ebef648526c7) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Layer Lab/3D Medieval Pack/Texture using Guid(544f1993571def64e9f8ebef648526c7) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '7761e5c8cfc2213e081a7c187ff699a7') in 0.0199684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Script/Player/Guides/HUONG_DAN_MO_RONG.md
  artifactKey: Guid(8788fb34c590d4649a58d2c5251c647e) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Player/Guides/HUONG_DAN_MO_RONG.md using Guid(8788fb34c590d4649a58d2c5251c647e) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '9ba28dc4456ec7b30ca46b7bbee75352') in 0.0384723 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_D.prefab
  artifactKey: Guid(9a83dd3d977a04148858ceb5a38eed3c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b)
Start importing Assets/Layer Lab/3D Medieval Pack/Prefabs/Ax_D.prefab using Guid(9a83dd3d977a04148858ceb5a38eed3c) Importer(2089858483,3d70f49cd1c2eb9ae422958d1c652c6b) (ScriptedImporter) -> (artifact id: '65545df0483f77d210a0b4165fd039e4') in 0.0235077 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

Editor requested this worker to shutdown with reason: balancing worker count downwards
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0