using UnityEngine;
using UnityEditor;
using System.Linq;

namespace EconomySystem.Editor
{
    /// <summary>
    /// Custom Editor cho ItemDatabase
    /// <PERSON>ung cấp giao diện dễ sử dụng để quản lý vật phẩm
    /// </summary>
    [CustomEditor(typeof(ItemDatabase))]
    public class ItemDatabaseEditor : UnityEditor.Editor
    {
        private ItemDatabase database;
        private Vector2 scrollPosition;
        private bool showAddItemSection = false;
        private bool showStatistics = true;
        private bool showItemList = true;

        // Thông tin vật phẩm mới
        private string newItemID = "";
        private string newItemName = "";
        private string newItemDescription = "";
        private int newItemBuyPrice = 10;
        private int newItemSellPrice = 5;
        private bool newItemCanSell = true;
        private ItemType newItemType = ItemType.VatLieu;
        private ItemRarity newItemRarity = ItemRarity.ThuongThuong;
        private int newItemMaxStack = 99;
        private Sprite newItemIcon = null;

        private void OnEnable()
        {
            database = (ItemDatabase)target;
        }

        public override void OnInspectorGUI()
        {
            if (database == null) return;

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Item Database Manager", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Thống kê
            DrawStatistics();

            EditorGUILayout.Space();

            // Nút chức năng chính
            DrawMainButtons();

            EditorGUILayout.Space();

            // Section thêm vật phẩm mới
            DrawAddItemSection();

            EditorGUILayout.Space();

            // Danh sách vật phẩm
            DrawItemList();

            // Lưu thay đổi
            if (GUI.changed)
            {
                EditorUtility.SetDirty(database);
            }
        }

        private void DrawStatistics()
        {
            showStatistics = EditorGUILayout.Foldout(showStatistics, "📊 Thống Kê", true);
            
            if (showStatistics)
            {
                EditorGUILayout.BeginVertical("box");
                
                EditorGUILayout.LabelField($"Tổng số vật phẩm: {database.LayTongSoVatPham()}");
                EditorGUILayout.LabelField($"Vật phẩm có trong shop: {database.LayTongSoVatPhamBan()}");
                
                // Thống kê theo loại
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Theo loại:", EditorStyles.boldLabel);
                
                foreach (ItemType type in System.Enum.GetValues(typeof(ItemType)))
                {
                    int count = database.LayVatPhamTheoLoai(type).Count;
                    if (count > 0)
                    {
                        EditorGUILayout.LabelField($"  {LayTenLoaiVatPham(type)}: {count}");
                    }
                }

                // Thống kê theo độ hiếm
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Theo độ hiếm:", EditorStyles.boldLabel);
                
                foreach (ItemRarity rarity in System.Enum.GetValues(typeof(ItemRarity)))
                {
                    int count = database.LayVatPhamTheoDoHiem(rarity).Count;
                    if (count > 0)
                    {
                        EditorGUILayout.LabelField($"  {LayTenDoHiem(rarity)}: {count}");
                    }
                }

                EditorGUILayout.EndVertical();
            }
        }

        private void DrawMainButtons()
        {
            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("🔍 Kiểm Tra Database", GUILayout.Height(30)))
            {
                database.KiemTraDatabase();
                EditorUtility.DisplayDialog("Kiểm Tra Database", "Đã kiểm tra database xong! Xem Console để biết chi tiết.", "OK");
            }

            if (GUILayout.Button("🎲 Tạo Vật Phẩm Mẫu", GUILayout.Height(30)))
            {
                if (EditorUtility.DisplayDialog("Tạo Vật Phẩm Mẫu", 
                    "Bạn có muốn tạo một số vật phẩm mẫu để test không?", "Có", "Không"))
                {
                    TaoVatPhamMau();
                }
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("📁 Tạo Database Mới", GUILayout.Height(25)))
            {
                TaoItemDatabaseMoi();
            }

            if (GUILayout.Button("🗑️ Xóa Tất Cả", GUILayout.Height(25)))
            {
                if (EditorUtility.DisplayDialog("Xác Nhận Xóa", 
                    "Bạn có chắc muốn xóa tất cả vật phẩm trong database?", "Có", "Không"))
                {
                    XoaTatCaVatPham();
                }
            }

            EditorGUILayout.EndHorizontal();
        }

        private void DrawAddItemSection()
        {
            showAddItemSection = EditorGUILayout.Foldout(showAddItemSection, "➕ Thêm Vật Phẩm Mới", true);
            
            if (showAddItemSection)
            {
                EditorGUILayout.BeginVertical("box");

                // Thông tin cơ bản
                EditorGUILayout.LabelField("Thông Tin Cơ Bản", EditorStyles.boldLabel);
                newItemID = EditorGUILayout.TextField("ID:", newItemID);
                newItemName = EditorGUILayout.TextField("Tên:", newItemName);
                newItemDescription = EditorGUILayout.TextField("Mô tả:", newItemDescription, GUILayout.Height(40));
                newItemIcon = (Sprite)EditorGUILayout.ObjectField("Icon:", newItemIcon, typeof(Sprite), false);

                EditorGUILayout.Space();

                // Giá cả
                EditorGUILayout.LabelField("Giá Cả", EditorStyles.boldLabel);
                newItemBuyPrice = EditorGUILayout.IntField("Giá mua:", newItemBuyPrice);
                newItemSellPrice = EditorGUILayout.IntField("Giá bán:", newItemSellPrice);
                newItemCanSell = EditorGUILayout.Toggle("Có thể bán:", newItemCanSell);

                EditorGUILayout.Space();

                // Phân loại
                EditorGUILayout.LabelField("Phân Loại", EditorStyles.boldLabel);
                newItemType = (ItemType)EditorGUILayout.EnumPopup("Loại:", newItemType);
                newItemRarity = (ItemRarity)EditorGUILayout.EnumPopup("Độ hiếm:", newItemRarity);
                newItemMaxStack = EditorGUILayout.IntField("Số lượng tối đa:", newItemMaxStack);

                EditorGUILayout.Space();

                // Nút thêm
                EditorGUILayout.BeginHorizontal();
                
                if (GUILayout.Button("✅ Thêm Vật Phẩm", GUILayout.Height(30)))
                {
                    ThemVatPhamMoi();
                }

                if (GUILayout.Button("🔄 Reset Form", GUILayout.Height(30)))
                {
                    ResetFormThemVatPham();
                }

                EditorGUILayout.EndHorizontal();

                EditorGUILayout.EndVertical();
            }
        }

        private void DrawItemList()
        {
            showItemList = EditorGUILayout.Foldout(showItemList, $"📋 Danh Sách Vật Phẩm ({database.LayTongSoVatPham()})", true);
            
            if (showItemList && database.DanhSachVatPham.Count > 0)
            {
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition, GUILayout.Height(300));

                for (int i = 0; i < database.DanhSachVatPham.Count; i++)
                {
                    var shopItem = database.DanhSachVatPham[i];
                    if (shopItem?.Item == null) continue;

                    EditorGUILayout.BeginVertical("box");
                    
                    EditorGUILayout.BeginHorizontal();
                    
                    // Thông tin vật phẩm
                    EditorGUILayout.BeginVertical();
                    EditorGUILayout.LabelField($"{shopItem.Item.TenVatPham} (ID: {shopItem.Item.ID})", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField($"Loại: {LayTenLoaiVatPham(shopItem.Item.LoaiVatPham)} | Độ hiếm: {LayTenDoHiem(shopItem.Item.DoHiem)}");
                    EditorGUILayout.LabelField($"Giá: {shopItem.Item.GiaMua} / {shopItem.Item.GiaBan} Lea");
                    EditorGUILayout.EndVertical();

                    // Nút xóa
                    if (GUILayout.Button("🗑️", GUILayout.Width(30), GUILayout.Height(30)))
                    {
                        if (EditorUtility.DisplayDialog("Xác Nhận Xóa", 
                            $"Bạn có chắc muốn xóa '{shopItem.Item.TenVatPham}'?", "Có", "Không"))
                        {
                            database.XoaVatPham(shopItem.Item.ID);
                            break;
                        }
                    }

                    EditorGUILayout.EndHorizontal();
                    EditorGUILayout.EndVertical();
                }

                EditorGUILayout.EndScrollView();
            }
            else if (showItemList)
            {
                EditorGUILayout.HelpBox("Database trống. Hãy thêm vật phẩm mới hoặc tạo vật phẩm mẫu.", MessageType.Info);
            }
        }

        private void ThemVatPhamMoi()
        {
            // Validate
            if (string.IsNullOrEmpty(newItemID))
            {
                EditorUtility.DisplayDialog("Lỗi", "ID không được để trống!", "OK");
                return;
            }

            if (string.IsNullOrEmpty(newItemName))
            {
                EditorUtility.DisplayDialog("Lỗi", "Tên vật phẩm không được để trống!", "OK");
                return;
            }

            if (database.KiemTraVatPhamTonTai(newItemID))
            {
                EditorUtility.DisplayDialog("Lỗi", $"Vật phẩm với ID '{newItemID}' đã tồn tại!", "OK");
                return;
            }

            // Tạo vật phẩm mới
            var newItem = new Item();
            
            // Sử dụng reflection để set private fields
            var itemType = typeof(Item);
            
            SetPrivateField(newItem, "id", newItemID);
            SetPrivateField(newItem, "tenVatPham", newItemName);
            SetPrivateField(newItem, "moTa", newItemDescription);
            SetPrivateField(newItem, "icon", newItemIcon);
            SetPrivateField(newItem, "giaMua", newItemBuyPrice);
            SetPrivateField(newItem, "giaBan", newItemSellPrice);
            SetPrivateField(newItem, "coTheBan", newItemCanSell);
            SetPrivateField(newItem, "loaiVatPham", newItemType);
            SetPrivateField(newItem, "doHiem", newItemRarity);
            SetPrivateField(newItem, "soLuongToiDa", newItemMaxStack);

            // Thêm vào database
            database.ThemVatPham(newItem);

            // Reset form
            ResetFormThemVatPham();

            EditorUtility.DisplayDialog("Thành Công", $"Đã thêm vật phẩm '{newItemName}' vào database!", "OK");
        }

        private void SetPrivateField(object obj, string fieldName, object value)
        {
            var field = obj.GetType().GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            field?.SetValue(obj, value);
        }

        private void ResetFormThemVatPham()
        {
            newItemID = database.TaoIDTuDong();
            newItemName = "";
            newItemDescription = "";
            newItemBuyPrice = 10;
            newItemSellPrice = 5;
            newItemCanSell = true;
            newItemType = ItemType.VatLieu;
            newItemRarity = ItemRarity.ThuongThuong;
            newItemMaxStack = 99;
            newItemIcon = null;
        }

        private void TaoVatPhamMau()
        {
            var vatPhamMau = new (string id, string ten, string moTa, int giaMua, int giaBan, ItemType loai, ItemRarity doHiem)[]
            {
                ("wood", "Gỗ", "Vật liệu cơ bản để xây dựng", 5, 2, ItemType.VatLieu, ItemRarity.ThuongThuong),
                ("stone", "Đá", "Vật liệu bền chắc", 8, 3, ItemType.VatLieu, ItemRarity.ThuongThuong),
                ("iron", "Sắt", "Kim loại quý giá", 15, 7, ItemType.VatLieu, ItemRarity.KhongThuong),
                ("gold", "Vàng", "Kim loại quý hiếm", 50, 25, ItemType.VatLieu, ItemRarity.HiemCo),
                ("sword", "Kiếm", "Vũ khí cận chiến", 100, 50, ItemType.VuKhi, ItemRarity.KhongThuong),
                ("shield", "Khiên", "Giáp phòng thủ", 80, 40, ItemType.GiapGiap, ItemRarity.KhongThuong),
                ("potion_health", "Thuốc Hồi Máu", "Hồi phục sức khỏe", 20, 10, ItemType.TieuHao, ItemRarity.ThuongThuong),
                ("bread", "Bánh Mì", "Thức ăn cơ bản", 3, 1, ItemType.TieuHao, ItemRarity.ThuongThuong),
                ("gem", "Ngọc Quý", "Vật phẩm quý giá", 200, 100, ItemType.QuyGia, ItemRarity.CucHiem),
                ("diamond", "Kim Cương", "Vật phẩm cực quý", 1000, 500, ItemType.QuyGia, ItemRarity.ThanThoai)
            };

            int soLuongThem = 0;
            foreach (var (id, ten, moTa, giaMua, giaBan, loai, doHiem) in vatPhamMau)
            {
                if (!database.KiemTraVatPhamTonTai(id))
                {
                    var item = new Item();
                    SetPrivateField(item, "id", id);
                    SetPrivateField(item, "tenVatPham", ten);
                    SetPrivateField(item, "moTa", moTa);
                    SetPrivateField(item, "giaMua", giaMua);
                    SetPrivateField(item, "giaBan", giaBan);
                    SetPrivateField(item, "loaiVatPham", loai);
                    SetPrivateField(item, "doHiem", doHiem);
                    
                    database.ThemVatPham(item);
                    soLuongThem++;
                }
            }

            EditorUtility.DisplayDialog("Hoàn Thành", $"Đã tạo {soLuongThem} vật phẩm mẫu!", "OK");
        }

        private void XoaTatCaVatPham()
        {
            database.DanhSachVatPham.Clear();
            EditorUtility.SetDirty(database);
            EditorUtility.DisplayDialog("Hoàn Thành", "Đã xóa tất cả vật phẩm!", "OK");
        }

        private void TaoItemDatabaseMoi()
        {
            string path = EditorUtility.SaveFilePanelInProject(
                "Tạo Item Database Mới",
                "NewItemDatabase",
                "asset",
                "Chọn vị trí lưu Item Database mới");

            if (!string.IsNullOrEmpty(path))
            {
                var newDatabase = CreateInstance<ItemDatabase>();
                AssetDatabase.CreateAsset(newDatabase, path);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                EditorUtility.DisplayDialog("Thành Công", $"Đã tạo Item Database mới tại: {path}", "OK");
            }
        }

        private string LayTenLoaiVatPham(ItemType loai)
        {
            switch (loai)
            {
                case ItemType.VatLieu: return "Vật Liệu";
                case ItemType.CongCu: return "Công Cụ";
                case ItemType.VuKhi: return "Vũ Khí";
                case ItemType.GiapGiap: return "Giáp";
                case ItemType.TieuHao: return "Tiêu Hao";
                case ItemType.QuyGia: return "Quý Giá";
                case ItemType.Khac: return "Khác";
                default: return "Không Xác Định";
            }
        }

        private string LayTenDoHiem(ItemRarity doHiem)
        {
            switch (doHiem)
            {
                case ItemRarity.ThuongThuong: return "Thường Thường";
                case ItemRarity.KhongThuong: return "Không Thường";
                case ItemRarity.HiemCo: return "Hiếm Có";
                case ItemRarity.CucHiem: return "Cực Hiếm";
                case ItemRarity.ThanThoai: return "Thần Thoại";
                default: return "Không Xác Định";
            }
        }
    }

    /// <summary>
    /// Menu item để tạo ItemDatabase
    /// </summary>
    public class ItemDatabaseMenuItems
    {
        [MenuItem("Assets/Create/Economy System/Item Database")]
        public static void TaoItemDatabase()
        {
            var database = ScriptableObject.CreateInstance<ItemDatabase>();
            
            string path = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (string.IsNullOrEmpty(path))
                path = "Assets";
            else if (System.IO.Path.GetExtension(path) != "")
                path = path.Replace(System.IO.Path.GetFileName(AssetDatabase.GetAssetPath(Selection.activeObject)), "");

            string assetPathAndName = AssetDatabase.GenerateUniqueAssetPath(path + "/NewItemDatabase.asset");

            AssetDatabase.CreateAsset(database, assetPathAndName);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            EditorUtility.FocusProjectWindow();
            Selection.activeObject = database;
        }
    }
}
