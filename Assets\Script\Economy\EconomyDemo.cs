using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace EconomySystem
{
    /// <summary>
    /// Script demo để test và hướng dẫn sử dụng hệ thống Economy
    /// C<PERSON> thể gắn vào GameObject để test các chức năng
    /// </summary>
    public class EconomyDemo : MonoBehaviour
    {
        [Header("Demo UI (Tùy chọn)")]
        [SerializeField] private Button buttonThemTien;
        [SerializeField] private Button buttonTruTien;
        [SerializeField] private Button buttonMuaVatPham;
        [SerializeField] private Button buttonBanVatPham;
        [SerializeField] private Button buttonHienThiThongKe;
        [SerializeField] private TextMeshProUGUI textKetQua;

        [Header("Cài Đặt Demo")]
        [SerializeField] private int soTienDemo = 100;
        [SerializeField] private string idVatPhamDemo = "wood";
        [SerializeField] private bool tuDongChayDemo = false;
        [SerializeField] private float khoangThoiGianDemo = 2f;

        private float thoiGianDemo = 0f;
        private int buocDemo = 0;

        #region Unity Methods
        private void Start()
        {
            KhoiTaoDemo();
            
            if (tuDongChayDemo)
            {
                InvokeRepeating(nameof(ChayDemoTuDong), 1f, khoangThoiGianDemo);
            }
        }

        private void Update()
        {
            // Phím tắt để test
            if (Input.GetKeyDown(KeyCode.F1))
                TestThemTien();
            
            if (Input.GetKeyDown(KeyCode.F2))
                TestTruTien();
            
            if (Input.GetKeyDown(KeyCode.F3))
                TestMuaVatPham();
            
            if (Input.GetKeyDown(KeyCode.F4))
                TestBanVatPham();
            
            if (Input.GetKeyDown(KeyCode.F5))
                HienThiThongKe();
        }
        #endregion

        #region Initialization
        private void KhoiTaoDemo()
        {
            // Thiết lập buttons
            if (buttonThemTien != null)
                buttonThemTien.onClick.AddListener(TestThemTien);

            if (buttonTruTien != null)
                buttonTruTien.onClick.AddListener(TestTruTien);

            if (buttonMuaVatPham != null)
                buttonMuaVatPham.onClick.AddListener(TestMuaVatPham);

            if (buttonBanVatPham != null)
                buttonBanVatPham.onClick.AddListener(TestBanVatPham);

            if (buttonHienThiThongKe != null)
                buttonHienThiThongKe.onClick.AddListener(HienThiThongKe);

            // Đăng ký events để hiển thị kết quả
            DangKyEvents();

            HienThiKetQua("🎮 Economy Demo đã sẵn sàng!\n" +
                         "Phím tắt: F1-Thêm tiền, F2-Trừ tiền, F3-Mua, F4-Bán, F5-Thống kê");
        }

        private void DangKyEvents()
        {
            // Currency events
            CurrencyManager.OnCurrencyChanged += OnTienThayDoi;
            CurrencyManager.OnTransactionCompleted += OnGiaoDichHoanThanh;
            CurrencyManager.OnTransactionFailed += OnGiaoDichThatBai;

            // Shop events
            ShopManager.OnItemPurchased += OnMuaVatPham;
            ShopManager.OnItemSold += OnBanVatPham;
            ShopManager.OnTransactionFailed += OnGiaoDichThatBai;

            // Inventory events
            InventoryManager.OnItemAdded += OnThemVatPhamVaoInventory;
            InventoryManager.OnItemRemoved += OnXoaVatPhamKhoiInventory;
            InventoryManager.OnInventoryFull += OnInventoryDay;
        }

        private void HuyDangKyEvents()
        {
            CurrencyManager.OnCurrencyChanged -= OnTienThayDoi;
            CurrencyManager.OnTransactionCompleted -= OnGiaoDichHoanThanh;
            CurrencyManager.OnTransactionFailed -= OnGiaoDichThatBai;

            ShopManager.OnItemPurchased -= OnMuaVatPham;
            ShopManager.OnItemSold -= OnBanVatPham;
            ShopManager.OnTransactionFailed -= OnGiaoDichThatBai;

            InventoryManager.OnItemAdded -= OnThemVatPhamVaoInventory;
            InventoryManager.OnItemRemoved -= OnXoaVatPhamKhoiInventory;
            InventoryManager.OnInventoryFull -= OnInventoryDay;
        }
        #endregion

        #region Demo Functions
        /// <summary>
        /// Test thêm tiền
        /// </summary>
        [ContextMenu("Test Thêm Tiền")]
        public void TestThemTien()
        {
            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.Instance.ThemTien(soTienDemo, "Demo test thêm tiền");
            }
            else
            {
                HienThiKetQua("❌ CurrencyManager chưa được khởi tạo!");
            }
        }

        /// <summary>
        /// Test trừ tiền
        /// </summary>
        [ContextMenu("Test Trừ Tiền")]
        public void TestTruTien()
        {
            if (CurrencyManager.Instance != null)
            {
                bool thanhCong = CurrencyManager.Instance.TruTien(soTienDemo / 2, "Demo test trừ tiền");
                if (!thanhCong)
                {
                    HienThiKetQua("❌ Không đủ tiền để trừ!");
                }
            }
            else
            {
                HienThiKetQua("❌ CurrencyManager chưa được khởi tạo!");
            }
        }

        /// <summary>
        /// Test mua vật phẩm
        /// </summary>
        [ContextMenu("Test Mua Vật Phẩm")]
        public void TestMuaVatPham()
        {
            if (ShopManager.Instance != null)
            {
                bool thanhCong = ShopManager.Instance.MuaVatPham(idVatPhamDemo, 1);
                if (!thanhCong)
                {
                    HienThiKetQua($"❌ Không thể mua vật phẩm '{idVatPhamDemo}'!");
                }
            }
            else
            {
                HienThiKetQua("❌ ShopManager chưa được khởi tạo!");
            }
        }

        /// <summary>
        /// Test bán vật phẩm
        /// </summary>
        [ContextMenu("Test Bán Vật Phẩm")]
        public void TestBanVatPham()
        {
            if (ShopManager.Instance != null)
            {
                bool thanhCong = ShopManager.Instance.BanVatPham(idVatPhamDemo, 1);
                if (!thanhCong)
                {
                    HienThiKetQua($"❌ Không thể bán vật phẩm '{idVatPhamDemo}'!");
                }
            }
            else
            {
                HienThiKetQua("❌ ShopManager chưa được khởi tạo!");
            }
        }

        /// <summary>
        /// Hiển thị thống kê hệ thống
        /// </summary>
        [ContextMenu("Hiển Thị Thống Kê")]
        public void HienThiThongKe()
        {
            string thongKe = "=== THỐNG KÊ HỆ THỐNG ===\n";

            // Currency
            if (CurrencyManager.Instance != null)
            {
                thongKe += CurrencyManager.Instance.LayThongTinChiTiet() + "\n\n";
            }

            // Inventory
            if (InventoryManager.Instance != null)
            {
                var inventory = InventoryManager.Instance;
                thongKe += $"📦 INVENTORY:\n";
                thongKe += $"Slots: {inventory.SoSlotDaSuDung}/{inventory.SoSlotDaSuDung + inventory.SoSlotTrong}\n";
                thongKe += $"Tổng vật phẩm: {inventory.TongSoVatPham}\n";
                thongKe += $"Loại vật phẩm: {inventory.DanhSachVatPham.Count}\n\n";
            }

            // Shop
            if (ShopManager.Instance?.ItemDatabase != null)
            {
                var database = ShopManager.Instance.ItemDatabase;
                thongKe += $"🏪 SHOP:\n";
                thongKe += $"Vật phẩm có sẵn: {database.LayTongSoVatPhamBan()}\n";
                thongKe += $"Tổng vật phẩm: {database.LayTongSoVatPham()}\n\n";
            }

            // Daily Data
            if (GameDataManager.Instance != null)
            {
                thongKe += GameDataManager.Instance.LayTongThongKe();
            }

            HienThiKetQua(thongKe);
            Debug.Log(thongKe);
        }

        /// <summary>
        /// Chạy demo tự động
        /// </summary>
        private void ChayDemoTuDong()
        {
            switch (buocDemo % 4)
            {
                case 0:
                    TestThemTien();
                    break;
                case 1:
                    TestMuaVatPham();
                    break;
                case 2:
                    TestBanVatPham();
                    break;
                case 3:
                    HienThiThongKe();
                    break;
            }

            buocDemo++;

            if (buocDemo >= 20) // Dừng sau 20 bước
            {
                CancelInvoke(nameof(ChayDemoTuDong));
                HienThiKetQua("🏁 Demo tự động đã hoàn thành!");
            }
        }
        #endregion

        #region Event Handlers
        private void OnTienThayDoi(int soTienMoi)
        {
            HienThiKetQua($"💰 Số tiền hiện tại: {soTienMoi:N0} Lea");
        }

        private void OnGiaoDichHoanThanh(int soTienThayDoi, string lyDo)
        {
            string icon = soTienThayDoi > 0 ? "💰" : "💸";
            HienThiKetQua($"{icon} {lyDo}: {soTienThayDoi:+#;-#;0} Lea");
        }

        private void OnGiaoDichThatBai(string lyDo)
        {
            HienThiKetQua($"❌ Giao dịch thất bại: {lyDo}");
        }

        private void OnMuaVatPham(Item item, int soLuong)
        {
            HienThiKetQua($"🛒 Đã mua {soLuong}x {item.TenVatPham}");
        }

        private void OnBanVatPham(Item item, int soLuong)
        {
            HienThiKetQua($"💰 Đã bán {soLuong}x {item.TenVatPham}");
        }

        private void OnThemVatPhamVaoInventory(Item item, int soLuong)
        {
            HienThiKetQua($"📦 Thêm vào inventory: {soLuong}x {item.TenVatPham}");
        }

        private void OnXoaVatPhamKhoiInventory(Item item, int soLuong)
        {
            HienThiKetQua($"📤 Xóa khỏi inventory: {soLuong}x {item.TenVatPham}");
        }

        private void OnInventoryDay()
        {
            HienThiKetQua("📦 Inventory đã đầy!");
        }
        #endregion

        #region Helper Methods
        private void HienThiKetQua(string noiDung)
        {
            if (textKetQua != null)
            {
                textKetQua.text = noiDung;
            }

            Debug.Log($"[Economy Demo] {noiDung}");
        }
        #endregion

        #region Unity Lifecycle
        private void OnDestroy()
        {
            HuyDangKyEvents();
            CancelInvoke();
        }
        #endregion

        #region Public Properties
        /// <summary>
        /// Kiểm tra hệ thống có sẵn sàng không
        /// </summary>
        public bool HeThongSanSang
        {
            get
            {
                return CurrencyManager.Instance != null &&
                       InventoryManager.Instance != null &&
                       ShopManager.Instance != null &&
                       GameDataManager.Instance != null;
            }
        }
        #endregion
    }
}
