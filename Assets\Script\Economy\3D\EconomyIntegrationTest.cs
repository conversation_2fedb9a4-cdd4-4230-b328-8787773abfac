using UnityEngine;
using EconomySystem;

namespace EconomySystem.Shop3D
{
    /// <summary>
    /// Script test đơn giản để kiểm tra tích hợp Economy 3D với hệ thống Player hiện có
    /// Thêm vào scene để test nhanh
    /// </summary>
    public class EconomyIntegrationTest : MonoBehaviour
    {
        [Header("Test Settings")]
        [SerializeField] private bool autoSetup = true;
        [SerializeField] private bool createTestItems = true;
        [SerializeField] private bool showDebugInfo = true;

        [Header("Test Items")]
        [SerializeField] private GameObject testItemPrefab;
        [SerializeField] private Transform[] spawnPoints;

        private bool isSetupComplete = false;

        #region Unity Methods
        private void Start()
        {
            if (autoSetup)
            {
                Invoke(nameof(RunIntegrationTest), 1f); // Delay để đảm bảo các system đã sẵn sàng
            }
        }

        private void Update()
        {
            if (showDebugInfo && isSetupComplete)
            {
                DisplayDebugInfo();
            }
        }
        #endregion

        #region Integration Test
        [ContextMenu("Run Integration Test")]
        public void RunIntegrationTest()
        {
            Debug.Log("🧪 Bắt đầu test tích hợp Economy 3D...");

            // 1. Kiểm tra hệ thống Player hiện có
            bool playerSystemOK = CheckPlayerSystem();

            // 2. Setup Economy System
            bool economySystemOK = SetupEconomySystem();

            // 3. Tích hợp EconomyPlayerAdapter
            bool adapterOK = SetupEconomyAdapter();

            // 4. Tạo test items
            bool itemsOK = true;
            if (createTestItems)
            {
                itemsOK = CreateTestItems();
            }

            // 5. Kết quả
            isSetupComplete = playerSystemOK && economySystemOK && adapterOK && itemsOK;

            if (isSetupComplete)
            {
                Debug.Log("✅ Tích hợp Economy 3D thành công!");
                ShowSuccessMessage();
            }
            else
            {
                Debug.LogError("❌ Tích hợp thất bại! Kiểm tra Console để biết chi tiết.");
            }
        }

        private bool CheckPlayerSystem()
        {
            Debug.Log("🔍 Kiểm tra hệ thống Player hiện có...");

            var playerController = FindFirstObjectByType<PlayerController>();
            var playerInteraction = FindFirstObjectByType<PlayerInteraction>();

            if (playerController == null)
            {
                Debug.LogError("❌ Không tìm thấy PlayerController! Hãy đảm bảo có Player trong scene.");
                return false;
            }

            if (playerInteraction == null)
            {
                Debug.LogWarning("⚠️ Không tìm thấy PlayerInteraction. Một số tính năng có thể bị hạn chế.");
            }

            Debug.Log($"✅ PlayerController: {playerController.name}");
            Debug.Log($"✅ PlayerInteraction: {(playerInteraction != null ? playerInteraction.name : "Không có")}");

            return true;
        }

        private bool SetupEconomySystem()
        {
            Debug.Log("🔍 Setup Economy System...");

            // Tìm hoặc tạo EconomySystemSetup
            var economySetup = FindFirstObjectByType<EconomySystemSetup>();
            if (economySetup == null)
            {
                GameObject setupObj = new GameObject("EconomySystemSetup");
                economySetup = setupObj.AddComponent<EconomySystemSetup>();
                Debug.Log("✅ Đã tạo EconomySystemSetup");
            }

            // Kiểm tra ItemDatabase
            if (economySetup.ItemDatabase == null)
            {
                Debug.LogWarning("⚠️ ItemDatabase chưa được gán. Tạo database test...");
                CreateTestItemDatabase(economySetup);
            }

            // Khởi tạo hệ thống
            economySetup.KhoiTaoHeThong();

            // Kiểm tra các manager
            bool managersOK = CheckEconomyManagers();

            return managersOK;
        }

        private void CreateTestItemDatabase(EconomySystemSetup economySetup)
        {
            var database = ScriptableObject.CreateInstance<ItemDatabase>();
            
            // Tạo test items
            var testItems = new Item[]
            {
                new Item("test_sword", "Kiếm Test", "Vũ khí test", 100, 50),
                new Item("test_potion", "Thuốc Test", "Thuốc hồi máu test", 20, 10),
                new Item("test_gem", "Ngọc Test", "Ngọc quý test", 500, 250)
            };

            foreach (var item in testItems)
            {
                database.ThemVatPham(item);
            }

            // Gán database (sử dụng reflection)
            var setupType = typeof(EconomySystemSetup);
            var databaseField = setupType.GetField("itemDatabase", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            databaseField?.SetValue(economySetup, database);

            Debug.Log("✅ Đã tạo test ItemDatabase");
        }

        private bool CheckEconomyManagers()
        {
            bool allOK = true;

            if (CurrencyManager.Instance == null)
            {
                Debug.LogError("❌ CurrencyManager không khởi tạo được!");
                allOK = false;
            }
            else
            {
                Debug.Log("✅ CurrencyManager OK");
            }

            if (InventoryManager.Instance == null)
            {
                Debug.LogError("❌ InventoryManager không khởi tạo được!");
                allOK = false;
            }
            else
            {
                Debug.Log("✅ InventoryManager OK");
            }

            if (ShopManager.Instance == null)
            {
                Debug.LogError("❌ ShopManager không khởi tạo được!");
                allOK = false;
            }
            else
            {
                Debug.Log("✅ ShopManager OK");
            }

            return allOK;
        }

        private bool SetupEconomyAdapter()
        {
            Debug.Log("🔍 Setup EconomyPlayerAdapter...");

            var playerController = FindFirstObjectByType<PlayerController>();
            if (playerController == null)
            {
                Debug.LogError("❌ Không tìm thấy PlayerController để gắn adapter!");
                return false;
            }

            // Kiểm tra adapter đã có chưa
            var adapter = playerController.GetComponent<EconomyPlayerAdapter>();
            if (adapter == null)
            {
                adapter = playerController.gameObject.AddComponent<EconomyPlayerAdapter>();
                Debug.Log("✅ Đã thêm EconomyPlayerAdapter");
            }
            else
            {
                Debug.Log("✅ EconomyPlayerAdapter đã có sẵn");
            }

            // Tạo UI cho adapter nếu cần
            CreateAdapterUI(adapter);

            return true;
        }

        private void CreateAdapterUI(EconomyPlayerAdapter adapter)
        {
            // Tạo Canvas nếu chưa có
            var canvas = FindFirstObjectByType<Canvas>();
            if (canvas == null)
            {
                GameObject canvasObj = new GameObject("EconomyCanvas");
                canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
                canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            }

            // Tạo interaction prompt đơn giản
            GameObject promptObj = new GameObject("EconomyInteractionPrompt");
            promptObj.transform.SetParent(canvas.transform, false);

            var promptImage = promptObj.AddComponent<UnityEngine.UI.Image>();
            promptImage.color = new Color(0, 0, 0, 0.7f);

            var promptRect = promptObj.GetComponent<RectTransform>();
            promptRect.anchorMin = new Vector2(0.3f, 0.1f);
            promptRect.anchorMax = new Vector2(0.7f, 0.3f);
            promptRect.offsetMin = Vector2.zero;
            promptRect.offsetMax = Vector2.zero;

            // Text
            GameObject textObj = new GameObject("PromptText");
            textObj.transform.SetParent(promptObj.transform, false);

            var promptText = textObj.AddComponent<TMPro.TextMeshProUGUI>();
            promptText.text = "Nhấn F để tương tác";
            promptText.fontSize = 18;
            promptText.color = Color.white;
            promptText.alignment = TMPro.TextAlignmentOptions.Center;

            var textRect = textObj.GetComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;

            // Gán vào adapter (sử dụng reflection)
            var adapterType = typeof(EconomyPlayerAdapter);
            var promptField = adapterType.GetField("economyInteractionPrompt",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            promptField?.SetValue(adapter, promptObj);

            var textField = adapterType.GetField("promptText",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            textField?.SetValue(adapter, promptText);

            promptObj.SetActive(false); // Ẩn ban đầu

            Debug.Log("✅ Đã tạo UI cho EconomyPlayerAdapter");
        }

        private bool CreateTestItems()
        {
            Debug.Log("🔍 Tạo test items...");

            if (testItemPrefab == null)
            {
                CreateTestItemPrefab();
            }

            if (spawnPoints == null || spawnPoints.Length == 0)
            {
                CreateTestSpawnPoints();
            }

            // Spawn test items
            for (int i = 0; i < spawnPoints.Length; i++)
            {
                if (spawnPoints[i] != null)
                {
                    GameObject itemObj = Instantiate(testItemPrefab, spawnPoints[i].position, spawnPoints[i].rotation);
                    itemObj.name = $"TestItem_{i}";

                    // Setup InteractableItem3D
                    var interactable = itemObj.GetComponent<InteractableItem3D>();
                    if (interactable != null)
                    {
                        // Set item ID dựa trên index
                        string[] testItemIDs = { "test_sword", "test_potion", "test_gem" };
                        string itemID = testItemIDs[i % testItemIDs.Length];

                        // Sử dụng reflection để set private fields
                        var itemField = typeof(InteractableItem3D).GetField("itemID",
                            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                        itemField?.SetValue(interactable, itemID);
                    }
                }
            }

            Debug.Log($"✅ Đã tạo {spawnPoints.Length} test items");
            return true;
        }

        private void CreateTestItemPrefab()
        {
            testItemPrefab = new GameObject("TestItemPrefab");

            // Visual
            var cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.transform.SetParent(testItemPrefab.transform);
            cube.transform.localScale = Vector3.one * 0.5f;

            // Collider
            var collider = testItemPrefab.AddComponent<BoxCollider>();
            collider.isTrigger = true;

            // InteractableItem3D
            testItemPrefab.AddComponent<InteractableItem3D>();

            Debug.Log("✅ Đã tạo test item prefab");
        }

        private void CreateTestSpawnPoints()
        {
            spawnPoints = new Transform[3];

            for (int i = 0; i < 3; i++)
            {
                GameObject spawnPoint = new GameObject($"TestSpawnPoint_{i}");
                spawnPoint.transform.SetParent(transform);
                spawnPoint.transform.position = new Vector3(i * 2f - 2f, 1f, 2f);
                spawnPoints[i] = spawnPoint.transform;
            }

            Debug.Log("✅ Đã tạo test spawn points");
        }
        #endregion

        #region Debug Info
        private void DisplayDebugInfo()
        {
            if (Input.GetKeyDown(KeyCode.F12))
            {
                ShowDebugInfo();
            }
        }

        [ContextMenu("Show Debug Info")]
        public void ShowDebugInfo()
        {
            string info = "=== ECONOMY 3D INTEGRATION STATUS ===\n";

            // Player System
            var playerController = FindFirstObjectByType<PlayerController>();
            var playerInteraction = FindFirstObjectByType<PlayerInteraction>();
            info += $"PlayerController: {(playerController != null ? "✅" : "❌")}\n";
            info += $"PlayerInteraction: {(playerInteraction != null ? "✅" : "❌")}\n";

            // Economy System
            info += $"CurrencyManager: {(CurrencyManager.Instance != null ? "✅" : "❌")}\n";
            info += $"InventoryManager: {(InventoryManager.Instance != null ? "✅" : "❌")}\n";
            info += $"ShopManager: {(ShopManager.Instance != null ? "✅" : "❌")}\n";

            // Adapter
            var adapter = EconomyPlayerAdapter.Instance;
            info += $"EconomyPlayerAdapter: {(adapter != null ? "✅" : "❌")}\n";

            if (adapter != null)
            {
                info += $"Current Economy Item: {(adapter.GetCurrentEconomyInteractable() != null ? "✅" : "❌")}\n";
                info += $"Economy Mode: {(adapter.IsInEconomyInteractionMode() ? "✅" : "❌")}\n";
            }

            // Currency
            if (CurrencyManager.Instance != null)
            {
                info += $"Current Money: {CurrencyManager.Instance.SoTienHienTai:N0} Lea\n";
            }

            Debug.Log(info);
        }

        private void ShowSuccessMessage()
        {
            string message = "🎉 ECONOMY 3D INTEGRATION THÀNH CÔNG!\n\n" +
                           "Controls:\n" +
                           "• WASD: Di chuyển\n" +
                           "• Mouse: Nhìn xung quanh\n" +
                           "• E: Tương tác thông thường\n" +
                           "• F: Tương tác Economy\n" +
                           "• Tab: Shop Menu\n" +
                           "• F12: Debug Info\n\n" +
                           "Hãy di chuyển đến gần các test items và thử tương tác!";

            Debug.Log(message);
        }
        #endregion

        #region Public Methods
        [ContextMenu("Add Test Money")]
        public void AddTestMoney()
        {
            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.Instance.ThemTien(1000, "Test money");
                Debug.Log("✅ Đã thêm 1000 Lea");
            }
        }

        [ContextMenu("Reset Economy")]
        public void ResetEconomy()
        {
            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.Instance.DatLaiSoTien(100);
            }

            if (InventoryManager.Instance != null)
            {
                InventoryManager.Instance.XoaToanBoInventory();
            }

            Debug.Log("✅ Đã reset Economy System");
        }
        #endregion
    }
}
