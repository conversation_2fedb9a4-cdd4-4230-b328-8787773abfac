# 🏪 Hướng Dẫn Cửa Hàng 3D - Unity Economy System

## 📋 Tổng Quan

Hệ thống cửa hàng 3D này tích hợp hoàn toàn với Economy System hiện có, cho phép player tương tác với vật phẩm trong môi trường 3D thực tế.

### ✨ Tính Năng Chính

- 🎮 **Tương tác 3D thực tế** với raycast và visual feedback
- 🏪 **Môi trường cửa hàng** với kệ hàng, lighting, atmosphere
- 💰 **Tích hợp Economy System** sử dụng tiền tệ Lea
- 🎨 **Visual Effects** với particles, outline, screen shake
- 🎵 **Audio System** với background music và sound effects
- 🎯 **Player Controller** hỗ trợ First/Third Person
- 📱 **UI 3D** với popup thông tin và giao dịch

---

## 🚀 Cài Đặt Nhanh

### Bước 1: Chuẩn Bị Scene

1. **Tạo Scene mới:**
   ```
   File → New Scene → 3D Core
   ```

2. **Tạo Ground:**
   ```
   GameObject → 3D Object → Plane
   ```
   - Scale: (5, 1, 5)
   - Position: (0, 0, 0)

3. **Tạo Lighting:**
   ```
   GameObject → Light → Directional Light
   ```
   - Rotation: (50, -30, 0)
   - Intensity: 1.2

### Bước 2: Setup Economy System

1. **Thêm EconomySystemSetup:**
   ```
   GameObject → Create Empty → "EconomySystemSetup"
   Add Component → Economy System Setup
   ```

2. **Tạo ItemDatabase:**
   ```
   Right-click Project → Create → Economy System → Item Database
   ```
   - Gán vào EconomySystemSetup
   - Bấm "Khởi Tạo Hệ Thống"

### Bước 3: Tích Hợp với Player Hiện Có

1. **Tìm Player hiện có:**
   ```csharp
   var playerController = FindObjectOfType<PlayerController>();
   ```

2. **Thêm EconomyPlayerAdapter:**
   ```csharp
   var adapter = playerController.gameObject.AddComponent<EconomyPlayerAdapter>();
   ```

3. **Hoặc sử dụng Shop3DDemo:**
   ```
   GameObject → Create Empty → "Shop3DDemo"
   Add Component → Shop3DDemo
   Context Menu → "Setup Complete Demo"
   ```

4. **Adapter sẽ tự động:**
   - Tìm PlayerController và PlayerInteraction hiện có
   - Tìm Camera của player
   - Setup input actions cho Economy system

### Bước 4: Tạo Shop Environment

1. **Tạo ShopEnvironment:**
   ```
   GameObject → Create Empty → "ShopEnvironment"
   Add Component → Shop Environment
   ```

2. **Tạo Shelves:**
   ```
   GameObject → 3D Object → Cube (cho mỗi shelf)
   ```
   - Đặt tag "Shelf"
   - Scale: (2, 1, 0.5)
   - Sắp xếp trong cửa hàng

3. **Gán Shelf Positions:**
   - Kéo các shelf vào array "Shelf Positions" trong ShopEnvironment

### Bước 5: Tạo Item Display Prefab

1. **Tạo Item Display:**
   ```
   GameObject → Create Empty → "ItemDisplay"
   ```

2. **Thêm Visual:**
   ```
   GameObject → 3D Object → Cube (làm con của ItemDisplay)
   ```
   - Scale: (0.5, 0.5, 0.5)
   - Thêm Material đẹp

3. **Thêm Components:**
   ```
   Add Component → Box Collider (set IsTrigger = true)
   Add Component → Interactable Item 3D
   ```

4. **Tạo Price Tag:**
   ```
   GameObject → 3D Object → Text Mesh
   ```
   - Đặt làm con của ItemDisplay
   - Position: (0, 1, 0)
   - Text: "100 Lea"

5. **Tạo Prefab:**
   - Kéo ItemDisplay vào Project folder
   - Gán prefab vào ShopEnvironment

### Bước 6: Setup UI

1. **Tạo Canvas:**
   ```
   GameObject → UI → Canvas
   ```

2. **Tạo Shop3DUI:**
   ```
   GameObject → Create Empty → "Shop3DUI"
   Add Component → Shop 3D UI
   ```

3. **Tạo UI Panels:**
   - Item Details Panel
   - Currency HUD
   - Interaction Prompt

4. **Gán UI References:**
   - Gán tất cả UI elements vào Shop3DUI component

### Bước 7: Thêm Visual Effects

1. **Tạo VisualEffects:**
   ```
   GameObject → Create Empty → "VisualEffects"
   Add Component → Visual Effects
   ```

2. **Tạo Particle Systems:**
   ```
   GameObject → Effects → Particle System
   ```
   - Tạo cho Purchase, Hover, Coin effects
   - Gán vào VisualEffects component

---

## 🎮 Hướng Dẫn Sử Dụng

### Controls

| Phím | Chức năng | Hệ thống |
|------|-----------|----------|
| **WASD** | Di chuyển | Player hiện có |
| **Mouse** | Nhìn xung quanh | Player hiện có |
| **Shift** | Chạy | Player hiện có |
| **Space** | Nhảy | Player hiện có |
| **E** | Tương tác thông thường | PlayerInteraction |
| **F** | Tương tác Economy | EconomyPlayerAdapter |
| **Tab** | Mở/đóng shop menu | EconomyPlayerAdapter |
| **Esc** | Đóng UI | UI System |

### Tương Tác Với Vật Phẩm

1. **Hover:** Nhìn vào vật phẩm để highlight
2. **Interact:** Nhấn E để mở thông tin chi tiết
3. **Purchase:** Chọn số lượng và bấm "Mua"
4. **Sell:** Bấm "Bán" nếu có trong inventory

### UI Elements

- **Currency HUD:** Hiển thị số dư Lea ở góc màn hình
- **Interaction Prompt:** Hiện khi nhìn vào vật phẩm
- **Item Details:** Popup với thông tin chi tiết
- **Confirmation Dialog:** Xác nhận giao dịch

---

## ⚙️ Cấu Hình Chi Tiết

### InteractableItem3D Settings

```csharp
[Header("Item Configuration")]
itemID = "wood";                    // ID vật phẩm
stockQuantity = 10;                 // Số lượng trong kho
isForSale = true;                   // Có bán không
canBuyBack = true;                  // Có thu mua không

[Header("Interaction")]
interactionDistance = 3f;           // Khoảng cách tương tác
interactionKey = KeyCode.E;         // Phím tương tác

[Header("Visual Effects")]
rotateDisplay = true;               // Xoay vật phẩm
rotationSpeed = 30f;                // Tốc độ xoay
```

### PlayerController3D Settings

```csharp
[Header("Movement")]
walkSpeed = 5f;                     // Tốc độ đi bộ
runSpeed = 8f;                      // Tốc độ chạy
jumpHeight = 2f;                    // Độ cao nhảy

[Header("Mouse Look")]
mouseSensitivity = 2f;              // Độ nhạy chuột
maxLookAngle = 80f;                 // Góc nhìn tối đa
lockCursor = true;                  // Khóa con trỏ
```

### ShopEnvironment Settings

```csharp
[Header("Item Spawning")]
maxItemsPerShelf = 5;               // Số vật phẩm tối đa/kệ
itemSpacing = 1f;                   // Khoảng cách giữa items
autoArrangeItems = true;            // Tự động sắp xếp
randomizeItemPositions = false;     // Vị trí ngẫu nhiên

[Header("Lighting")]
warmLightColor = (1, 0.9, 0.7);    // Màu ánh sáng ấm
lightIntensity = 1.2f;              // Cường độ ánh sáng
enableDynamicLighting = true;       // Ánh sáng động
```

---

## 🎨 Customization

### Thêm Vật Phẩm Mới

1. **Tạo 3D Model:**
   - Import model vào Unity
   - Tạo prefab cho display model

2. **Thêm vào ItemDatabase:**
   - Sử dụng Custom Editor
   - Điền thông tin đầy đủ

3. **Refresh Shop:**
   - Bấm "Refresh Shop Items" trong ShopEnvironment

### Tạo Hiệu Ứng Mới

1. **Particle Effects:**
   ```csharp
   // Trong VisualEffects.cs
   public void PlayCustomEffect(Vector3 position)
   {
       // Code hiệu ứng của bạn
   }
   ```

2. **Sound Effects:**
   ```csharp
   // Thêm AudioClip vào VisualEffects
   [SerializeField] private AudioClip customSound;
   ```

### Tùy Chỉnh UI

1. **Thay đổi Layout:**
   - Chỉnh sửa prefab UI panels
   - Điều chỉnh anchors và positions

2. **Thêm Animation:**
   ```csharp
   // Trong Shop3DUI.cs
   private IEnumerator CustomUIAnimation()
   {
       // Animation code
   }
   ```

---

## 🔧 Troubleshooting

### Lỗi Thường Gặp

1. **"Raycast không hoạt động":**
   - Kiểm tra Layer Mask trong PlayerInteraction
   - Đảm bảo Collider có IsTrigger = true

2. **"Vật phẩm không spawn":**
   - Kiểm tra ItemDatabase có dữ liệu
   - Đảm bảo ShelfPositions được gán

3. **"UI không hiển thị":**
   - Kiểm tra Canvas Render Mode
   - Đảm bảo EventSystem có trong scene

4. **"Player không di chuyển được":**
   - Kiểm tra CharacterController component
   - Đảm bảo Ground có Collider

### Debug Tools

- **Gizmos:** Hiển thị interaction range
- **Console Logs:** Chi tiết về giao dịch
- **Context Menus:** Test nhanh các chức năng

---

## 📊 Performance Tips

### Tối Ưu Hóa

1. **Object Pooling:**
   ```csharp
   // Cho particle effects và floating text
   public class ObjectPool : MonoBehaviour
   {
       // Implementation
   }
   ```

2. **LOD System:**
   - Sử dụng LOD Group cho 3D models
   - Giảm detail khi xa camera

3. **Culling:**
   - Frustum culling cho items
   - Occlusion culling cho môi trường

### Memory Management

- Unload unused assets
- Compress textures
- Optimize mesh topology

---

## 🎯 Mở Rộng

### Tính Năng Có Thể Thêm

1. **Quest System:**
   - NPCs với dialogue
   - Delivery quests
   - Reward system

2. **Crafting System:**
   - Workbenches
   - Recipe system
   - Material combination

3. **Multiplayer:**
   - Shared shop space
   - Player trading
   - Auction house

4. **Mobile Support:**
   - Touch controls
   - UI scaling
   - Performance optimization

---

## 📝 Kết Luận

Hệ thống cửa hàng 3D này cung cấp:

✅ **Immersive Experience** - Tương tác thực tế trong môi trường 3D
✅ **Full Integration** - Tích hợp hoàn toàn với Economy System
✅ **Rich Feedback** - Visual và audio effects phong phú
✅ **Flexible Setup** - Dễ dàng customize và mở rộng
✅ **Production Ready** - Tối ưu cho performance và UX

Với hướng dẫn này, bạn có thể tạo ra một cửa hàng 3D hoàn chỉnh và hấp dẫn cho game của mình!

---

## 📞 Hỗ Trợ

Nếu gặp vấn đề:
1. Kiểm tra Console logs
2. Sử dụng Debug tools
3. Xem lại cấu hình components
4. Test từng phần một cách riêng biệt
