# Hệ Thống Tiền Tệ Lea - Unity Economy System

Đây là một hệ thống tiền tệ và cửa hàng hoàn chỉnh cho Unity với tiền tệ chính "Lea", đ<PERSON><PERSON><PERSON> thiết kế để dễ sử dụng, mở rộng và tích hợp với game theo ngày.

## Tính Năng Chính

### 🪙 Hệ Thống Tiền Tệ
- Quản lý tiền tệ chính (Lea)
- Hiển thị UI với hiệu ứng thay đổi
- Lưu trữ tự động
- Events để theo dõi giao dịch

### 🎒 Hệ Thống Inventory
- Quản lý vật phẩm với stack
- Sắp xếp tự động theo loại và độ hiếm
- Kiểm tra số lượng và slot trống
- Tích hợp với hệ thống cửa hàng

### 🏪 Hệ Thống Cửa Hàng
- Mua/bán vật phẩm
- <PERSON><PERSON> lọc theo loại và tìm kiếm
- UI trực quan với thông tin chi tiết
- Kiểm tra điều kiện mua/bán

### 💾 Hệ Thống Lưu Trữ
- Lưu/tải dữ liệu tự động
- Backup an toàn
- Hỗ trợ JSON format

## Cài Đặt Nhanh

### Bước 1: Thiết Lập Cơ Bản

1. **Tạo ItemDatabase:**
   - Right-click trong Project → Create → Economy System → Item Database
   - Đặt tên: `MainItemDatabase`
   - Sử dụng Custom Editor để thêm vật phẩm mẫu

2. **Thêm EconomySystemSetup vào scene:**
   - Tạo GameObject mới trong scene
   - Thêm component `EconomySystemSetup`
   - Gán ItemDatabase vào field "Item Database"
   - Bấm "Khởi Tạo Hệ Thống" trong Inspector

3. **Cấu hình tự động:**
   - Hệ thống sẽ tự động tạo tất cả Manager cần thiết
   - Kiểm tra Console để đảm bảo không có lỗi

### Bước 2: Tạo UI

1. **Tạo Canvas nếu chưa có:**
   - Hierarchy → UI → Canvas
   - Đảm bảo có EventSystem trong scene

2. **Thiết lập Currency UI:**
   - Tạo UI Text (TextMeshPro) để hiển thị số tiền Lea
   - Thêm component `CurrencyUI`
   - Gán Text reference vào field "Text So Tien"
   - UI sẽ tự động cập nhật khi số tiền thay đổi

3. **Thiết lập Shop UI (Tùy chọn):**
   - Tạo Panel cho cửa hàng
   - Thêm ScrollView cho danh sách vật phẩm
   - Thêm component `ShopUI`
   - Tạo prefab `ShopItemUI` cho từng vật phẩm

### Bước 3: Cấu Hình Vật Phẩm

1. **Sử dụng Custom Editor:**
   - Chọn ItemDatabase trong Project
   - Sử dụng Custom Editor để thêm vật phẩm
   - Bấm "Tạo Vật Phẩm Mẫu" để có dữ liệu test

2. **Thêm vật phẩm thủ công:**
   - Mở section "Thêm Vật Phẩm Mới"
   - Điền thông tin: ID, tên, mô tả, giá cả
   - Chọn loại và độ hiếm
   - Bấm "Thêm Vật Phẩm"

3. **Kiểm tra dữ liệu:**
   - Bấm "Kiểm Tra Database" để validate
   - Xem thống kê trong Inspector

## Sử Dụng Trong Code

### Quản Lý Tiền Tệ Lea

```csharp
// Thêm tiền Lea
CurrencyManager.Instance.ThemTien(100, "Hoàn thành nhiệm vụ");

// Trừ tiền Lea
bool thanhCong = CurrencyManager.Instance.TruTien(50, "Mua vật phẩm");

// Kiểm tra đủ tiền
bool duTien = CurrencyManager.Instance.KiemTraDuTien(200);

// Lấy số tiền hiện tại
int soTien = CurrencyManager.Instance.SoTienHienTai;

// Lấy thông tin chi tiết
string thongTin = CurrencyManager.Instance.LayThongTinChiTiet();

// Đặt lại số tiền (chỉ dùng cho debug)
CurrencyManager.Instance.DatLaiSoTien(1000);
```

### Quản Lý Inventory

```csharp
// Thêm vật phẩm
Item item = ShopManager.Instance.ItemDatabase.LayVatPhamTheoID("wood");
int soLuongThemVao = InventoryManager.Instance.ThemVatPham(item, 5);

// Xóa vật phẩm
int soLuongXoa = InventoryManager.Instance.XoaVatPham("wood", 2);

// Kiểm tra có vật phẩm
bool coVatPham = InventoryManager.Instance.CoVatPham("wood", 3);

// Lấy số lượng vật phẩm
int soLuong = InventoryManager.Instance.LaySoLuongVatPham("wood");

// Sắp xếp inventory
InventoryManager.Instance.SapXepInventory();

// Lấy vật phẩm theo loại
var vatLieu = InventoryManager.Instance.LayVatPhamTheoLoai(ItemType.VatLieu);
```

### Quản Lý Cửa Hàng

```csharp
// Mua vật phẩm
bool muaThanhCong = ShopManager.Instance.MuaVatPham("sword", 1);

// Bán vật phẩm
bool banThanhCong = ShopManager.Instance.BanVatPham("wood", 5);

// Kiểm tra có thể mua
bool coTheMua = ShopManager.Instance.CoTheMuaVatPham("sword", 1);

// Lấy danh sách vật phẩm bán
List<Item> danhSach = ShopManager.Instance.LayDanhSachVatPhamBan();

// Tìm kiếm vật phẩm
List<Item> ketQuaTimKiem = ShopManager.Instance.TimKiemVatPham("kiếm");

// Lấy vật phẩm theo loại
List<Item> vuKhi = ShopManager.Instance.LayDanhSachVatPhamTheoLoai(ItemType.VuKhi);
```

### Quản Lý Dữ Liệu Theo Ngày

```csharp
// Lấy dữ liệu hôm nay
DailyData homNay = GameDataManager.Instance.DuLieuHomNay;

// Lấy thống kê 7 ngày gần nhất
List<DailyData> thongKe7Ngay = GameDataManager.Instance.LayThongKe7NgayGanNhat();

// Lưu dữ liệu thủ công
GameDataManager.Instance.LuuDuLieu();

// Lấy tổng thống kê
string tongThongKe = GameDataManager.Instance.LayTongThongKe();
```

## Events Hệ Thống

### Currency Events
```csharp
// Đăng ký events
CurrencyManager.OnCurrencyChanged += OnTienThayDoi;
CurrencyManager.OnTransactionCompleted += OnGiaoDichHoanThanh;

private void OnTienThayDoi(int soTienMoi)
{
    Debug.Log($"Số tiền mới: {soTienMoi}");
}

private void OnGiaoDichHoanThanh(int soTienThayDoi, string lyDo)
{
    Debug.Log($"Giao dịch: {soTienThayDoi}, Lý do: {lyDo}");
}
```

### Shop Events
```csharp
// Đăng ký events
ShopManager.OnItemPurchased += OnMuaVatPham;
ShopManager.OnItemSold += OnBanVatPham;
ShopManager.OnTransactionFailed += OnGiaoDichThatBai;

private void OnMuaVatPham(Item item, int soLuong)
{
    Debug.Log($"Đã mua {soLuong}x {item.TenVatPham}");
}
```

### Inventory Events
```csharp
// Đăng ký events
InventoryManager.OnItemAdded += OnThemVatPham;
InventoryManager.OnItemRemoved += OnXoaVatPham;
InventoryManager.OnInventoryChanged += OnInventoryThayDoi;
```

## Tùy Chỉnh

### Tạo Loại Vật Phẩm Mới

1. **Mở rộng enum ItemType:**
   ```csharp
   public enum ItemType
   {
       VatLieu,
       CongCu,
       VuKhi,
       GiapGiap,
       TieuHao,
       QuyGia,
       Khac,
       // Thêm loại mới
       TrangSuc,
       BuaPhep
   }
   ```

2. **Cập nhật UI dropdown nếu cần**

### Thêm Thuộc Tính Vật Phẩm

1. **Mở rộng class Item:**
   ```csharp
   [Header("Thuộc Tính Mở Rộng")]
   [SerializeField] private int durability = 100;
   [SerializeField] private float weight = 1f;
   
   public int Durability => durability;
   public float Weight => weight;
   ```

### Tùy Chỉnh UI

1. **Thay đổi màu sắc độ hiếm trong ShopItemUI**
2. **Thêm hiệu ứng animation**
3. **Tùy chỉnh layout và font**

## Debug và Testing

### Debug Commands (chỉ trong Editor)

```csharp
// Trong Inspector của các Manager, có các Context Menu:
// - CurrencyManager: "Thêm 1000 Tiền", "Trừ 500 Tiền", "Reset Tiền"
// - InventoryManager: "Sắp Xếp Inventory", "Xóa Toàn Bộ Inventory"
// - ShopManager: "Test Mua Vật Phẩm"
// - GameDataManager: "Lưu Dữ Liệu", "Tải Dữ Liệu", "Xóa Tất Cả Dữ Liệu"
```

### Kiểm Tra Lỗi Thường Gặp

1. **Không tìm thấy Manager:**
   - Đảm bảo có EconomySystemSetup trong scene
   - Kiểm tra Singleton pattern hoạt động đúng

2. **UI không cập nhật:**
   - Kiểm tra events đã được đăng ký
   - Đảm bảo references UI được gán đúng

3. **Dữ liệu không lưu:**
   - Kiểm tra GameDataManager có trong scene
   - Xem log để biết lỗi cụ thể

## Mở Rộng Hệ Thống

### Thêm Nhiều Loại Tiền Tệ

1. Tạo class `MultiCurrencyManager`
2. Mở rộng UI để hiển thị nhiều loại tiền
3. Cập nhật ShopManager để hỗ trợ nhiều loại tiền

### Thêm Hệ Thống Craft

1. Tạo class `CraftingManager`
2. Định nghĩa recipes
3. Tích hợp với InventoryManager

### Thêm Hệ Thống Quest Rewards

1. Tạo class `QuestManager`
2. Tích hợp với CurrencyManager và InventoryManager
3. Thêm UI quest

## Demo và Testing

### Sử Dụng EconomyDemo

1. **Thêm EconomyDemo vào scene:**
   - Tạo GameObject mới
   - Thêm component `EconomyDemo`
   - Cấu hình các settings trong Inspector

2. **Test bằng phím tắt:**
   - F1: Thêm tiền
   - F2: Trừ tiền
   - F3: Mua vật phẩm
   - F4: Bán vật phẩm
   - F5: Hiển thị thống kê

3. **Auto Demo:**
   - Bật "Tu Dong Chay Demo" để test tự động
   - Xem kết quả trong Console và UI

### Context Menu Testing

Tất cả Manager đều có Context Menu để test:
- **CurrencyManager:** Thêm/trừ tiền, reset, lưu/tải
- **InventoryManager:** Sắp xếp, xóa inventory
- **ShopManager:** Test mua/bán vật phẩm
- **GameDataManager:** Lưu/tải/xóa dữ liệu
- **EconomySystemSetup:** Khởi tạo, reset, kiểm tra hệ thống

## Tính Năng Nâng Cao

### Lưu Trữ Dữ Liệu Theo Ngày

Hệ thống tự động:
- Phát hiện ngày mới và tạo dữ liệu mới
- Lưu thống kê hoạt động hàng ngày
- Backup dữ liệu an toàn
- Theo dõi tiến trình theo thời gian

### Events System

Tất cả Manager đều có events để tích hợp:
```csharp
// Đăng ký events
CurrencyManager.OnCurrencyChanged += OnTienThayDoi;
ShopManager.OnItemPurchased += OnMuaVatPham;
InventoryManager.OnInventoryChanged += OnInventoryThayDoi;
GameDataManager.OnNewDayStarted += OnNgayMoi;
```

### Custom Editor

ItemDatabase có Custom Editor với:
- Thống kê chi tiết
- Thêm vật phẩm dễ dàng
- Tạo vật phẩm mẫu
- Kiểm tra và validate dữ liệu

## Mở Rộng Hệ Thống

### Thêm Loại Tiền Tệ Mới

1. Mở rộng CurrencyManager để hỗ trợ nhiều loại tiền
2. Cập nhật UI để hiển thị nhiều loại tiền
3. Sửa đổi ShopManager để hỗ trợ giá bằng nhiều loại tiền

### Thêm Hệ Thống Craft

1. Tạo CraftingManager mới
2. Định nghĩa recipes với ItemDatabase
3. Tích hợp với InventoryManager

### Thêm Hệ Thống Quest

1. Tạo QuestManager
2. Tích hợp rewards với CurrencyManager
3. Sử dụng GameDataManager để lưu tiến trình

## Hỗ Trợ

### Kiểm Tra Lỗi Thường Gặp

1. **"Manager không tồn tại":**
   - Đảm bảo có EconomySystemSetup trong scene
   - Bấm "Khởi Tạo Hệ Thống" trong Inspector

2. **"UI không cập nhật":**
   - Kiểm tra events đã được đăng ký
   - Đảm bảo references UI được gán đúng

3. **"Dữ liệu không lưu":**
   - Kiểm tra GameDataManager có trong scene
   - Xem Console log để biết lỗi cụ thể

4. **"ItemDatabase trống":**
   - Sử dụng Custom Editor để thêm vật phẩm
   - Bấm "Tạo Vật Phẩm Mẫu" để có dữ liệu test

### Debug Tools

- **Console Logs:** Tất cả Manager đều có logs chi tiết
- **Context Menus:** Test nhanh các chức năng
- **EconomyDemo:** Script demo hoàn chỉnh
- **Custom Editor:** Quản lý ItemDatabase trực quan

### Performance

Hệ thống được tối ưu cho:
- ✅ Singleton pattern cho hiệu suất
- ✅ Events thay vì polling
- ✅ Lazy loading cho UI
- ✅ Efficient serialization
- ✅ Memory management

## Kết Luận

Hệ thống Economy với tiền tệ Lea này cung cấp:

🪙 **Quản lý tiền tệ hoàn chỉnh** với persistent storage
🎒 **Inventory system** với sorting và filtering
🏪 **Shop system** với mua/bán tự động
💾 **Daily data tracking** cho game theo ngày
🎮 **Easy setup** chỉ với vài click
🔧 **Highly customizable** qua Inspector
📊 **Rich analytics** và reporting
🚀 **Production ready** với error handling

Tất cả Inspector fields đều có thể điều chỉnh trong Edit mode để phù hợp với workflow phát triển của bạn. Hệ thống được thiết kế để dễ sử dụng, mở rộng và maintain.
